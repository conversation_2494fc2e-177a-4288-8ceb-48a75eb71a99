<script setup lang="uts">
	import api from './utils/api.uts'
	import { setStorage, UrlParamHash, isWeiXinBrowser, getUrlParam, isIOS } from './utils/util.uts'
	import { doLogin, isLogin, isUser } from './utils/login.uts'
	import { useGlobalDataStore } from './stores/globalData'
	import { useSystemStore } from './stores/system';
	import { getGlobalStyle } from '@/utils/tabbar.uts';
	// 检查版本更新的逻辑
	import { updateManager } from "@/utils/updateApp.uts"
	import __config from './config/env'

	// #ifdef APP-PLUS
	import APPUpdate from './public/APPUpdate/index.js'
	import { startPush } from './api/push'
	// #endif

	const globalDataStore = useGlobalDataStore()
	const systemStore = useSystemStore()

	// 应用启动时的初始化逻辑
	onLaunch((e : OnLaunchOptions) => {
		console.log('App Launch:', e)
		//全局加载字体
		loadFontFaceFrom()
		// #ifdef APP
		if (uni.getSystemInfoSync().platform === 'ios') {
			isNetwork(e)
		} else {
			resloveOnLaunch(e)
		}
		// #endif
		// #ifndef APP
		resloveOnLaunch(e)
		// #endif
	})

	onShow((e : OnShowOptions) => {
		console.log('App Show:', e)
		//暂时用不到先注释
		// globalDataStore.setAppState('show')
		if (e?.referrerInfo?.extraData != null) {
			// 从中学小程序跳转过来，购买校服的逻辑
			const schoolData = e.referrerInfo.extraData
			setStorage(schoolData, 'school_user_info')
		}

		// #ifdef APP
		if (!uni.getStorageSync('user_info')) {
			uni.setStorageSync('user_info', JSON.stringify({ id: null }))
		}
		setTimeout(() => {
			const args = plus.runtime.arguments
			if (args) {
				let url : string | null = null
				if (args.startsWith('songlei://')) {
					url = args.replace(/songlei:\/\//, '')
				}
				if (args.startsWith('meitao://')) {
					url = args.replace(/meitao:\/\//, '')
				}
				if (url) {
					uni.navigateTo({ url })
				}
				plus.runtime.arguments = ''
			}
		}, 100)
		// #endif
	})

	onHide(() => {
		console.log('App Hide')
		globalDataStore.safeUpdate({
			appState: 'hide'
		})
	})

	function resloveOnLaunch(e : OnLaunchOptions) {
		// #ifdef APP
		startPush()
		uni.hideTabBar()
		// #endif
		// 换px像素的话后面再观察还需要不需要
		// setGlobalSize()
		// #ifdef MP || APP
		// uni.onWindowResize((res) => {
		// 	setGlobalSize(res.size.windowWidth)
		// })
		// #endif

		const { scene, path, query } = e || {}
		if (query) {
			const { gdt_vid, weixinadinfo, sf, nGo } = query as UTSJSONObject
			if (gdt_vid) {
				globalDataStore.safeUpdate({
					gdtVid: gdt_vid
				});
			}
			globalDataStore.safeUpdate({
				sf,
				nGo
			});

			if (weixinadinfo) {
				const weixinadinfoArr = (weixinadinfo as string).split('.')
				globalDataStore.safeUpdate({
					weixinadinfoId: parseInt(weixinadinfoArr[0])
				});
			}

			const qrCodeScene = decodeURIComponent(query.scene as string || '')
			if (qrCodeScene) {
				const qrCodeSf = UrlParamHash(qrCodeScene, 'sf')
				if (qrCodeSf) {
					globalDataStore.safeUpdate({
						sf: qrCodeSf,
						nGo: globalDataStore.nGo
					});
				}
				const qrCodeNGo = UrlParamHash(qrCodeScene, 'nGo')
				if (qrCodeNGo) {
					globalDataStore.safeUpdate({
						sf: globalDataStore.sf,
						nGo: qrCodeNGo
					});
				}
			}
		}
		globalDataStore.safeUpdate({
			isSinglePage: scene === 1154
		}); 
		
		if (path == 'pages/home/<USER>' && scene != 1154) {
			globalDataStore.safeUpdate({
				showOpenScreen: true
			});
		}

		// #ifdef MP
		if (e.scene && e.scene == 1154) return
		const localLiveScenes = [1175, 1176, 1177, 1184, 1191, 1193, 1195, 1200, 1201, 1216, 1228]
		if (e.scene && localLiveScenes.includes(e.scene)) {
			globalDataStore.safeUpdate({
				payMode: true
			});
		}

		//微信版本更新，是否强制更新
		updateManager()
		//session登录
		doLogin()
		// #endif

		// #ifdef APP
		if (!uni.getStorageSync('user_info')) {
			uni.setStorageSync('user_info', JSON.stringify({ id: null }))
		}
		//app版本更新也需要重新梳理
		// APPUpdate()
		// #endif

		// #ifdef APP-PLUS
		globalDataStore.safeUpdate({
			tenantId: __config.tenantId
		});
		// #endif
		globalDataStore.safeUpdate({
			isShowingPage: true
		});
		// 或者主题配置
		getGlobalStyle()
	}

	function setGlobalSize(windowWidth ?: number) {
		// #ifdef APP
		if (windowWidth) {
			if (windowWidth !== systemStore.windowWidth) {
				const pages = getCurrentPages()
				const currPage = pages[pages.length - 1]
				uni.reLaunch({
					url: '/pages/login/blank?paths=' + currPage.$page.fullPath
				})
			}
		}
		// #endif

		if (!windowWidth) {
			windowWidth = uni.getSystemInfoSync().windowWidth
		}

		const pixelRatio = 750 / windowWidth

		// #ifdef MP-WEIXIN || MP-QQ
		const { width, height, bottom, top } = uni.getMenuButtonBoundingClientRect()
		const menuHeight = height
		const menuWidth = width
		// #endif

		uni.getSystemInfo({
			success: (e) => {
				let StatusBar = 0
				let CustomBar = 0
				// #ifndef MP
				StatusBar = e.statusBarHeight
				CustomBar = e.platform == 'android' ?
					e.statusBarHeight + 50 : e.statusBarHeight + 45
				// #endif

				// #ifdef MP-WEIXIN || MP-QQ
				StatusBar = e.statusBarHeight
				if (bottom > 0 && top > 0) {
					CustomBar = bottom + top - e.statusBarHeight
				} else {
					CustomBar = e.statusBarHeight + 50
				}
				// #endif

				// #ifdef MP-ALIPAY
				StatusBar = e.statusBarHeight
				CustomBar = e.statusBarHeight + e.titleBarHeight
				// #endif

				if (CustomBar < 10) {
					CustomBar = 60
				}

				let HeightBar = 0
				let leftMenuHeight = 0
				let leftMenuWidth = 0
				// #ifdef APP
				HeightBar = 40
				leftMenuWidth = 100
				let menuWidth = 0
				let menuHeight = 40
				// #endif

				// #ifndef APP
				HeightBar = menuHeight
				leftMenuHeight = menuHeight
				leftMenuWidth = menuWidth
				// #endif

				if (windowWidth < 350) {
					// #ifdef APP
					HeightBar = HeightBar * 0.8
					// #endif
					CustomBar = CustomBar - 4
					leftMenuHeight = leftMenuHeight * 0.6
					leftMenuWidth = leftMenuWidth * 0.6
				}

				systemStore.updateData({
					windowWidth,
					pixelRatio,
					menuHeight: menuHeight || 40,
					menuWidth: menuWidth || 0,
					CustomBar,
					HeightBar: HeightBar || menuHeight || 40,
					StatusBar,
					leftMenuWidth: leftMenuWidth || menuWidth || 100,
					leftMenuHeight: leftMenuHeight || menuHeight || 40,
					isPhone: windowWidth < 550,
					padWidth: 550,
					bigPhone: 430,
					smallWidth: 310,
					multipleView: windowWidth < 550 ? 2 : 1.2,
					isIOS: isIOS()
				})
				systemStore.updateRootFontSize()
			}
		})
	}


	function isNetwork(e : OnLaunchOptions) {
		uni.getNetworkType({
			success: (res) => {
				if (res.networkType === 'none') {
					const CALLBACK = (res : any) => {
						if (res.isConnected !== false) {
							const pages = getCurrentPages()
							const url = pages[pages.length - 1].$page.fullPath
							uni.offNetworkStatusChange(CALLBACK)
							resloveOnLaunch(e)
							uni.reLaunch({ url })
						}
					}
					uni.onNetworkStatusChange(CALLBACK)
				} else {
					resloveOnLaunch(e)
				}
			},
			fail: (err) => {
				console.log('判断网络错误提示', err)
				resloveOnLaunch(e)
			}
		})
	}

	function loadFontFaceFrom() {
		// #ifdef MP-WEIXIN
		if (uni.getSystemInfoSync().platform === 'android' ||
			uni.getSystemInfoSync().platform === 'devtools') {
			const fontEnUrl = 'https://slshop-file.oss-cn-beijing.aliyuncs.com/apk/songshufont/Harmonyos.ttf'
			uni.loadFontFace({
				global: true,
				family: 'font-en-regular',
				source: 'url("' + fontEnUrl + '")',
				success: () => {
					console.log('Harmonyos font loaded successfully')
				},
				fail: (e) => {
					console.log('Harmonyos font load failed', e)
				}
			})
		}
		// #endif
	}

	// 导出工具方法供页面使用
	const appMethods = {
		doLogin,
		isLogin,
		isUser
	}

	// 提供给全局使用
	provide('appMethods', appMethods)
</script>

<style lang="scss">
	/* #ifndef APP-PLUS-NVUE */
	@import "./app.scss";
	/* #endif */
</style>