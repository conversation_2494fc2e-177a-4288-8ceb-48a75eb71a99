## 此项目是UniApp-X 跨平台项目

### 基本规范
- 写法与vue3一致，vue文件-->uvue,ts文件-->uts
- 遵循这个写法

### UniApp-X 核心特性
- **UTS 语言**：强类型的 TypeScript 变体，编译为各平台原生语言
  - Web → JavaScript
  - Android → Kotlin  
  - iOS → Swift
  - 鸿蒙 → ETS
- **原生性能**：逻辑层和渲染层都是原生实现，性能等同原生应用
- **强类型约束**：需要显式类型声明，提供更好的类型安全

### 开发注意事项

#### 语法差异
1. **强类型语言**：必须显式声明变量类型
2. **类型转换**：更严格的类型检查和转换
3. **CSS 支持**：仅支持 CSS 子集，不完全兼容 Web CSS
4. **Vue 组件**：使用 `.uvue` 扩展名，语法与 Vue3 一致

#### 迁移注意
1. **不能直接迁移**：现有 uni-app 项目需要重写
2. **类型声明**：所有变量、函数参数、返回值都需要类型声明
3. **API 差异**：部分 API 可能与传统 uni-app 不同
4. **样式限制**：某些 Web CSS 特性可能不支持
  - uni-app-x 默认view flex布局
  - flex-direction 默认值是 column，也就是纵向排列
  - 推荐使用flex布局，少用margin

#### 状态管理
- 推荐使用 **Pinia** 进行状态管理
- 所有 store 都需要强类型定义
- actions 中直接修改状态，无需 mutations
- 无法使用 getApp().globalData获取全局变量，需要用store存取
