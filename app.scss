@import "./static/style/main.css";
@import "./static/style/icon.css";
@import "./static/style/animation.css";
@import "./static/style/coupon.css";
@import "./static/style/media.scss";

.flex-four {
	flex: 4
}

.overflow {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.overflow-1 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.flow-1 {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
}

.overflow-2 {
	/*允许在单词内换行*/
	word-break: break-all;
	text-overflow: -o-ellipsis-lastline;
	/*css3中webkit内核提供的一个方法类似 ellipsis*/
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	/*自适应盒子*/
	-webkit-line-clamp: 2;
	/*此处为1行,如果是两行就改成2*/
	-webkit-box-orient: vertical;
}

.two-line-text {
  white-space: normal; /* 允许文本换行 */
  overflow: auto; /* 启用滚动条 */
	max-height: 221upx; /* 限制文本高度为两行，根据你的字体大小调整 */
}


.display-ib {
	display: inline-block
}

.display-i {
	display: inline
}

.margin-top-bar {
	margin-top: 80rpx
}

.margin-bottom-bar {
	margin-bottom: 80rpx
}

.vertical-center {
	margin: auto 0rpx
}

.text-decorat {
	text-decoration: line-through;
}

.mar-top-30 {
	margin-top: -30rpx !important
}

.basis-3 {
	flex-basis: 30%;
}

.basis-7 {
	flex-basis: 70%;
}

.text-gold {
	color: #cdae92;
}

.bg-gradual-yellow {
	background-image: linear-gradient(90deg, #CDAD90 0%, #CDA185 100%);
	color: #ffffff;
}

.bg-gradual-scarlet {
	background-image: linear-gradient(90deg, #e5432e, #e53c43);
	color: #ffffff;
}

.bg-gradual-red {
	background-image: linear-gradient(90deg, #f43f3b, #ec008c);
	color: #ffffff;
}

.bg-gradual-orange {
	background-image: linear-gradient(90deg, #ff9700, #ed1c24);
	color: #ffffff;
}

.bg-gradual-green {
	background-image: linear-gradient(90deg, #39b54a, #8dc63f);
	color: #ffffff;
}

.bg-gradual-purple {
	background-image: linear-gradient(90deg, #9000ff, #5e00ff);
	color: #ffffff;
}

.bg-gradual-pink {
	background-image: linear-gradient(90deg, #ec008c, #6739b6);
	color: #ffffff;
}

.bg-gradual-darkblue {
	background-image: linear-gradient(90deg, #0055ff, #1cbbb4);
	color: #ffffff;
}

.bg-gradual-blue {
	background-image: linear-gradient(90deg, #0081ff, #1cbbb4);
	color: #ffffff;
}

.bg-darkblue {
	background-color: #0055ff;
	color: #ffffff;
}

.bg-scarlet {
	background-color: #ff4444;
	color: #ffffff;
}

.bg-scarlet-border {
	border: 2px solid #e53c43;
	color: #e53c43;
}

.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
	left: -10upx;
	bottom: 70upx;
	top: initial;
}

.price-original {
	font-weight: normal;
	text-decoration: line-through;
	color: #9c9ba1;
	font-size: 20rpx;
	margin-left: 4rpx;
	margin-top: 4rpx;
}



/* #ifdef H5 */
uni-checkbox[disabled] .uni-checkbox-wrapper .uni-checkbox-input {
	background: #d5d3d8 !important;
}

uni-toast {
	z-index: 9999;
}

/* #endif */


.sell-out {
	background-color: rgba(0, 0, 0, 0.3);
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 100%;
	height: 100%;
}


.sell-out-text {
	background-color: rgba(0, 0, 0, 0.7);
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	color: white;
	font-size: 30rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}


