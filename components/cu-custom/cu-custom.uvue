<template>
  <view>
    <view class="cu-custom" :style="{ height: customBar + 'px', backgroundColor: bgColor, display: 'block' }">
      <view class="fixed" :style="barStyle" :class="barClass">
        <view v-if="simple" class="action" @tap="backPage" style="padding-right: 20rpx">
          <text class="cuIcon-back font-xxxmd" :style="{ color: iconColor }"></text>
        </view>

        <view v-else-if="canBack && isBack" class="action font-xxxmd">
          <view :style="leftMenuStyle">
            <text @tap="backPage" style="display: flex; align-items: center; justify-content: center; flex: 1">
              <text class="cuIcon-back"></text>
            </text>
            <text class="height-60" :style="splitLineStyle"></text>
            <text @tap="goHome" style="display: flex; align-items: center; justify-content: center; flex: 1">
              <text class="cuIcon-home"></text>
            </text>
          </view>
        </view>

        <view v-else-if="!canBack && isBack" class="action" @tap="goHome">
          <text class="text-xdf" :style="homeButtonStyle">
            <text class="cuIcon-home"></text>
          </text>
        </view>

        <!--#ifdef MP-->
        <view v-if="!hideMarchContent" class="march-content" :style="marchContentStyleMP">
          <slot name="marchContent"></slot>
        </view>
        <!--#endif-->

        <!--#ifdef APP-->
        <view v-if="!hideMarchContent" class="march-content flex align-center" :style="marchContentStyleApp">
          <slot name="marchContent"></slot>
        </view>
        <!--#endif-->

        <!--#ifdef H5-->
        <view v-if="!hideMarchContent" class="march-content" :style="marchContentStyleH5">
          <slot name="marchContent"></slot>
        </view>
        <!--#endif-->

        <view v-else class="content" :style="{ top: statusBar + 'px' }">
          <slot name="content"></slot>
        </view>

        <slot name="right"></slot>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  import { computed, ref, onMounted } from 'vue'
  import { useSystemStore } from '@/stores/system'

  interface Props {
    pageTo ?: string
    textColor ?: string
    menuColor ?: string
    bgColor ?: string
    isBack ?: boolean
    bgImage ?: string
    hideMarchContent ?: boolean
    boderColor ?: string
    iconBgColor ?: string
    iconColor ?: string
    lineColor ?: string
    simple ?: boolean
    leftTitleProps ?: {
      showLeftTitle ?: boolean
      leftTitleColor ?: string
      leftTitleSize ?: string
      leftTitle ?: string
      leftTitleWidth ?: number
    }
  }

  const props = withDefaults(defineProps<Props>(), {
    pageTo: '',
    textColor: '#ffffff',
    menuColor: '#000000',
    bgColor: '',
    isBack: false,
    bgImage: '',
    hideMarchContent: false,
    boderColor: '',
    iconBgColor: '',
    iconColor: '',
    lineColor: '',
    simple: false,
    leftTitleProps: () => ({
      showLeftTitle: false,
      leftTitleColor: '#000',
      leftTitleSize: '12',
      leftTitle: '松鼠美淘',
      leftTitleWidth: 30
    })
  })

  const systemStore = useSystemStore()
  const canBack = ref<boolean>(true)

  const customBar = computed(() => systemStore.CustomBar || 0)
  const statusBar = computed(() => systemStore.StatusBar || 0)
  const menuHeight = computed(() => systemStore.menuHeight || 0)
  const menuWidth = computed(() => systemStore.menuWidth || 0)
  const leftMenuWidth = computed(() => systemStore.leftMenuWidth || 0)
  const leftMenuHeight = computed(() => systemStore.leftMenuHeight || 0)
  const windowWidth = computed(() => systemStore.windowWidth || 375)

  const customFiled = computed(() => systemStore.customFiled || {})

  const barStyle = computed(() => {
    const style = [
      `background-size: 750rpx ${customBar.value}px`,
      `background-repeat: no-repeat`,
      `height: ${customBar.value}px`,
      `padding-top: ${windowWidth.value > 310 ? statusBar.value : statusBar.value + 4}px`,
      `color: ${props.textColor}`,
      `background-color: ${props.bgColor}`,
      `align-items:center`,
      `flex-direction: row`
    ]

    if (props.bgImage) {
      style.push(`background-image: url(${props.bgImage})`)
    }

    return style.join(';')
  })

  const barClass = computed(() => {
    const classes : string[] = []
    if (props.bgImage) {
      classes.push('none-bg', 'text-white', 'bg-img')
    }
    if (props.bgColor) {
      classes.push(props.bgColor)
    }
    return classes
  })

  const iconColor = computed(() => {
    if (props.iconColor) return dealSameColor(props.iconColor)
    return dealSameColor(customFiled.value.topTabbarIconColor || '#000')
  })

  const leftMenuStyle = computed(() => ({
    color: iconColor.value,
    fontWeight: '500',
    justifyContent: 'space-between',
    width: leftMenuWidth.value + 'px',
    height: leftMenuHeight.value + 'px',
    borderRadius: '60rpx',
    display: 'flex',
    alignItems: 'center',
    background: customFiled.value.topTabbarIconBackgroundColor || 'transparent',
    border: `solid ${dealSameColor(props.boderColor || customFiled.value.topTabbarBorderColor || '#ddd')} 2rpx`
  }))

  const splitLineStyle = computed(() => ({
    transform: 'scale(0.45)',
    width: '1px',
    backgroundColor: dealSameColor(props.lineColor || customFiled.value.topTabbarSplitLineColor || '#ddd')
  }))

  const homeButtonStyle = computed(() => ({
    color: iconColor.value,
    width: leftMenuWidth.value / 2 + 'px',
    height: leftMenuHeight.value + 'px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    background: dealSameColor(customFiled.value.topTabbarIconBackgroundColor || 'transparent'),
    border: `solid ${dealSameColor(props.boderColor || customFiled.value.topTabbarBorderColor || '#ddd')} 2rpx`,
    borderRadius: '50rpx'
  }))

  const marchContentStyleMP = computed(() => {
    const baseWidth = windowWidth.value - menuWidth.value
    let width = baseWidth - 10

    if (canBack.value && props.isBack) {
      width = baseWidth - leftMenuWidth.value - 17
    } else if (!canBack.value && props.isBack) {
      width = baseWidth - leftMenuWidth.value / 2 - 17
    }

    return {
      height: menuHeight.value + 'px',
      lineHeight: customBar.value + 'px',
      display: 'flex',
      alignItems: 'center',
      minWidth: width + 'px',
      width: width + 'px',
      maxWidth: width + 'px',
    }
  })

  const marchContentStyleApp = computed(() => ({
    width: '100vw',
    height: menuHeight.value + 'px'
  }))

  const marchContentStyleH5 = computed(() => ({
    width: '100vw',
    height: menuHeight.value + 'px'
  }))

  function dealColor(color : string) : string {
    if (!color) return ''
    let tempColor = color.toLowerCase()
    if (tempColor.length === 4) {
      tempColor += tempColor.slice(1)
    }
    return tempColor
  }

  function dealSameColor(color : string) : string {
    const tempBgColor = dealColor(props.bgColor)
    const tempColor = dealColor(color)
    return tempBgColor === tempColor ? '#333333' : color
  }

  function backPage() : void {
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]

    if (currentPage.route === 'pages/login/index') {
      uni.reLaunch({ url: '/pages/home/<USER>' })
      return
    }

    if (pages.length > 1) {
      if (currentPage.route === 'pages/wallet/wallet-pages/index') {
        uni.switchTab({ url: '/pages/tab-personal/index' })
      } else {
        uni.navigateBack({ delta: 1 })
      }
    } else {
      uni.switchTab({ url: '/pages/home/<USER>' })
    }
  }

  function goHome() : void {
    uni.switchTab({ url: '/pages/home/<USER>' })
  }

  onMounted(() => {
    const pages = getCurrentPages()
    canBack.value = pages.length > 1 && pages[pages.length - 1].route !== 'pages/login/index'
  })
</script>