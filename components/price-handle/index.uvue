<template>
  <view class='flex-row' :class="propClass" style="line-height: 1;" :style="{ color, fontWeight }">
    <slot name="prefix"></slot>
    <text class="price-handle-big-font price-text" v-if="signShow" :style="{
        color: `${signColor || color || ''}`,
        '--font': signFonts,
        '--base': basesignFont
      }">￥</text>
    <text class="price-handle-big-font price-text" :style="{
        color: `${bigColor || color || ''}`,
        '--font': bigFonts,
        '--base': baseBigFont
      }">{{ integer }}</text>
    <text class="price-handle-big-font price-text" v-if="decimal" :style="{ 
        color: smallColor || color || '',
        '--font': smallFonts,
        '--base': baseSmallFont
      }">{{ decimal }}</text>
    <slot name="append"></slot>
  </view>
</template>

<script setup>
  /**
  *   功能: 整数和小数大小不同进行处理
  *   value: 传入的值(只能传入数值和数字字符, 必填)
  *   color: 整体传入的颜色(选填，会继承父元素的颜色)
  *   bigColor: 整数传入的颜色(选填，不填会继承color颜色，如color也没有，会继承父元素的颜色)
  *   signColor: ￥字符的颜色(选填，不填会继承color颜色，如color也没有，会继承父元素的颜色)
  *   smallColor: 小数传入的颜色(选填，不填会继承color颜色，如color也没有，会继承父元素的颜色)
  *   bigFont: 整数传入的字体(选填，默认值34rpx)
  *   smallFont: 小数传入的字体(选填，默认值26rpx)
  *   signFont: ￥字符的字体(选填，默认值26rpx)
  *   signShow: ￥字符是否显示(选填，默认值true)
  *   fontWeight: 传入的字体粗细大小(选填，默认值：600)
  *   pointTwo: 小数点位数(选填，默认是根据传入的值来的位数)
  * **/

  // Props定义
  const props = defineProps({
    value: {
      type: null, // 兼容小程序环境，接受任意类型
      default: 0
    },
    color: {
      type: String,
      default: ''
    },
    bigColor: {
      type: String,
      default: ''
    },
    smallColor: {
      type: String,
      default: ''
    },
    signColor: {
      type: String,
      default: ''
    },
    bigFont: {
      type: String,
      default: '34rpx'
    },
    smallFont: {
      type: String,
      default: '26rpx'
    },
    signFont: {
      type: String,
      default: '26rpx'
    },
    fontWeight: {
      type: String,
      default: 'bold'
    },
    signShow: {
      type: Boolean,
      default: true
    },
    pointTwo: {
      type: Number,
      default: -1
    },
    propClass: {
      type: String,
      default: ''
    }
  })

  // 解构props以便在watch中使用
  const { value, pointTwo, bigFont, smallFont, signFont } = toRefs(props)

  // 响应式数据
  const integer = ref(0)
  const decimal = ref('')

  // 计算基础字体偏移量的公共函数
  const calculateBaseFont = (fontSize) => {
    const fontValue = parseInt(fontSize)
    let font = 4

    if (fontValue >= 38 && fontValue < 48) {
      font = 6
    } else if (fontValue >= 48 && fontValue < 80) {
      font = 8
    } else if (fontValue >= 80) {
      font = 0
    }

    // #ifdef APP
    font = font / 2 + 'px'
    // #endif
    // #ifdef MP
    font += 'rpx'
    // #endif

    return font
  }

  // 转换字体大小的公共函数
  const convertFontSize = (fontSize) => {
    let font = fontSize
    // #ifdef APP
    font = parseInt(fontSize)
    font = font / 2 + 'px'
    // #endif
    return font
  }

  // 处理价格数据的函数
  const processPriceData = (newValue, pointTwoValue) => {
    if (pointTwoValue === -1) {
      // 默认处理：保持原有小数位数
      if (newValue) {
        let integerPart = 0
        let decimalPart = ''

        if (newValue === ~~newValue) {
          integerPart = newValue
        } else {
          const parts = String(newValue).split('.')
          integerPart = parts[0]
          if (parts[1]) {
            decimalPart = '.' + parts[1].slice(0, 2)
          }
        }

        return { integerPart, decimalPart }
      } else {
        return { integerPart: 0, decimalPart: '' }
      }
    } else {
      // 指定小数位数处理
      if (newValue) {
        let integerPart = 0
        let decimalPart = ''

        if (pointTwoValue) {
          const parts = String(Number(newValue).toFixed(pointTwoValue)).split('.')
          integerPart = parts[0]
          decimalPart = '.' + parts[1]
        } else {
          integerPart = ~~newValue
        }

        return { integerPart, decimalPart }
      } else {
        const integerPart = 0
        const decimalPart = pointTwoValue
          ? String(0.0.toFixed(pointTwoValue)).slice((pointTwoValue + 1) * -1)
          : ''

        return { integerPart, decimalPart }
      }
    }
  }

  // 计算属性
  const baseBigFont = computed(() => calculateBaseFont(bigFont.value))
  const baseSmallFont = computed(() => calculateBaseFont(smallFont.value))
  const basesignFont = computed(() => calculateBaseFont(signFont.value))
  const bigFonts = computed(() => convertFontSize(bigFont.value))
  const smallFonts = computed(() => convertFontSize(smallFont.value))
  const signFonts = computed(() => convertFontSize(signFont.value))

  // 监听value变化
  watch(
    [value, pointTwo],
    ([newValue, newPointTwo]) => {
      const { integerPart, decimalPart } = processPriceData(newValue, newPointTwo)
      integer.value = integerPart
      decimal.value = decimalPart
    },
    { immediate: true }
  )
</script>

<style scoped>
  /* 根据视图窗口进行字体大小的判断 */
  @media (max-width: 310px) {
    .price-handle-big-font {
      font-size: calc(var(--font) + var(--base));
    }
  }

  @media (min-width: 310px) {
    .price-handle-big-font {
      font-size: calc(var(--font));
    }
  }

  .price-handle-big-font {
    /* font-family: 'Gill Sans', 'Gill Sans MT', Calibri, 'Trebuchet MS', sans-serif; */
  }

  .price-handle-component {
    align-items: baseline;
  }

  .price-handle-component .price-text {}
</style>