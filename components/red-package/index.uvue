<template>
  <view
    :class="showType == 'top' ? 'red-package-top' : 'red-package-bottom'"
    :style="{ marginTop: 0 }"
    v-if="refState.redPackageImg"
    @tap="handleClick"
  >
    <image style="width:100%" :src="refState.redPackageImg" mode="widthFix" />
    <image
      class="close-btn width-md height-md"
      :style="{
        top: showType == 'top' ? '' : 0,
        bottom: showType == 'top' ? 0 : '',
        left: showType == 'top' ? 0 : '',
        right: showType == 'top' ? '' : 0
      }"
      :src="$imgUrl('1/material/c6a0de8e-7360-4fb2-837e-b48dbd0787eb.png')"
      fit="cover"
      @tap.stop="handleClose"
    />
  </view>
</template>

<script lang="uts">
import { advertNewYear } from '@/components/red-package/api.uts'
import { gotoPage } from '@/page-components/div-components/div-base/div-page-urls.uts'
import { reactive, onMounted } from 'vue'

interface RedPackageData {
  redPackageImg: string
  linkUrl: string
}

export default {
  name: 'RedPackage',
  props: {
    showType: {
      type: String,
      default: 'bottom' // 顶部和底部
    }
  },
  setup(props) {
    const refState = reactive<RedPackageData>({
      redPackageImg: '',
      linkUrl: ''
    })

    // 获取广告信息
    const getAdvertisement = () => {
      advertNewYear({
        bigClass: 'NEW_YEAR'
      }).then((res: any) => {
        if (res.data && res.data.imgUrl) {
          refState.redPackageImg = res.data.imgUrl
          refState.linkUrl = res.data.linkUrl
        }
      }).catch((err: any) => {
        console.error('获取红包广告失败:', err)
      })
    }

    // 关闭红包
    const handleClose = () => {
      refState.redPackageImg = ''
    }

    // 点击红包
    const handleClick = (e: any) => {
      const pages = getCurrentPages()
      console.log('点击红包:', e, refState.linkUrl)
      if (refState.linkUrl) {
        gotoPage(refState.linkUrl, pages.length)
      }
    }

    onMounted(() => {
      getAdvertisement()
    })

    return {
      refState,
      handleClose,
      handleClick
    }
  }
}
</script>

<style lang="scss" scoped>
.red-package-bottom {
  position: fixed;
  bottom: -100%;
  animation-delay: 2s;
  animation: popUp 1s ease forwards;
  width: 100%;
  z-index: 9999;
}

@keyframes popUp {
  0% {
    bottom: -100%;
  }
  100% {
    bottom: 0;
  }
}

.red-package-top {
  position: fixed;
  top: -100%;
  animation-delay: 2s;
  animation: popBottom 1s ease forwards;
  width: 100%;
  z-index: 9999;
}

@keyframes popBottom {
  0% {
    top: -100%;
  }
  100% {
    top: 0;
  }
}

.close-btn {
  position: absolute;
}
</style>
