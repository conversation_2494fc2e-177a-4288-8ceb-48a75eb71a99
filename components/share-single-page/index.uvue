<template>
  <view>
    <view
      class="statusbar"
      style="background-color: red;"
      :style="{height: `${refState.statusBarHeight}px`}"
    >
      <!-- 顶部 -->
    </view>
    <view
      style="background-color: red;"
      :style="{height: `${refState.titleBarHeight}px`, lineHeight: `${refState.titleBarHeight}px`}"
    >首页
      <!-- 导航栏 -->
    </view>
    <view class="share">
      <view class="shareImgBox">
        <image
          v-if="refState.shareImgUrl?.imgUrl"
          :src="refState.shareImgUrl.imgUrl"
          mode="widthFix"
        ></image>
        <view class="ptb-sm  font-s">松鼠美淘</view>
        <view class="text-sm text-gray">- 来自松鼠美淘 -</view>
      </view>
    </view>
  </view>
</template>

<script lang="uts">
import api from "@/utils/api.uts"
import { reactive, onMounted } from 'vue'

interface ShareImgData {
  imgUrl: string
}

export default {
  name: 'ShareSinglePage',
  setup() {
    const refState = reactive({
      titleBarHeight: 0,
      statusBarHeight: 0,
      shareImgUrl: null as ShareImgData | null
    })

    const initSystemInfo = () => {
      const menuButtonObject = uni.getMenuButtonBoundingClientRect()
      uni.getSystemInfo({
        success: (res: UniApp.GetSystemInfoSuccessCallbackResult) => {
          const navHeight = menuButtonObject.height + (menuButtonObject.top - res.statusBarHeight) * 2
          refState.titleBarHeight = navHeight
          refState.statusBarHeight = res.statusBarHeight
        }
      })
    }

    const getAdvertisementInfo = () => {
      api.advertisementinfo({ bigClass: 'WECHAT_MOMENTS' })
        .then((res: any) => {
          refState.shareImgUrl = res.data
        })
    }

    onMounted(() => {
      initSystemInfo()
      getAdvertisementInfo()
    })

    return { refState }
  }
}
</script>

<style lang="scss" scoped>
.status_bar {
  width: 100%;
  background-color: red;
}
.share {
  text-align: center;
  margin: 0rpx auto;
  box-shadow:  5px  #ccc;
} 
</style>