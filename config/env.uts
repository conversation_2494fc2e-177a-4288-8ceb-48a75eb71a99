// 获取小程序当前版本

interface URLConfig {
  BASE_URL: string
}

interface EnvConfig {
  basePath: string
  nineGridUrl: string
  walletPayUrl: string
  walletPwdUrl: string
  walletAgreement: string
  carAgreement: string
  originAppid: string
  mchId: string
  chatId: string
  mapKey: string
  tenantId: number
  h5HostUrl: string
  wxAppId: string
  appUpdateUrl: string
  showPrivacyPolicy: boolean
  privacyPolicyUrl: string
  protocolUrl: string
  debug: boolean
}

let basePath: URLConfig | string
// #ifdef MP
const accountInfo = uni.getAccountInfoSync()
const urlConfig: string = accountInfo.miniProgram.appId
const envVersion: string = accountInfo.miniProgram.envVersion
// console.log("urlConfig", urlConfig)
// console.log("envVersion", envVersion)

// 根据不同的环境配置不同的接口地址
// 测试环境 https://site.songlei.com
const URLMap: UTSJSONObject = {
  develop: {
    BASE_URL: 'https://site.songlei.com',
   // BASE_URL: 'https://shopapi.songlei.com',
  },
  trial: {
    // BASE_URL: 'https://site.songlei.com',  
    BASE_URL: 'https://shopapi.songlei.com',
  },
  release: {
    BASE_URL: 'https://shopapi.songlei.com',
  }
}
basePath = URLMap[envVersion] as URLConfig
const mpBasePath: string = urlConfig === "wxc507d1f4d41ad94a" ? 'https://site.songlei.com' : (basePath as URLConfig).BASE_URL
// const mpBasePath = urlConfig === "wx5bdb3c8113221e5d" ? 'https://shopapi.songlei.com' : basePath.BASE_URL
// #endif
// #ifdef APP
basePath = { 
  BASE_URL: 'https://site.songlei.com',
  // BASE_URL: 'https://shopapi.songlei.com',
}
const appBasePath: string = (basePath as URLConfig).BASE_URL
// #endif
// #ifdef H5
basePath = "https://shopapi.songlei.com"
// #endif

const envConfig: EnvConfig = {
  // 线上环境    松鼠美淘：wx5bdb3c8113221e5d
  // 测试环境    松鼠美妆：wxc507d1f4d41ad94a
  // 警告：发松鼠美妆测试请在manifest.json 修改AppId wxc507d1f4d41ad94a; 其他请修改环境配置
  // #ifdef  MP
  basePath: mpBasePath,
  nineGridUrl: mpBasePath + '/slshop-h5/activities/LuckyDraw/nineGrid/index.html',
  // 钱包H5页面链接
  walletPayUrl: mpBasePath + '/slshop-h5/wallet/pay/index.html',
  walletPwdUrl: mpBasePath + '/slshop-h5/wallet/set-password/index.html',
  walletAgreement: mpBasePath + '/slshop-h5/agreement/wallet.html',
  carAgreement: mpBasePath + '/slshop-h5/agreement/caragreement.html',
  
  // #endif
   // #ifdef APP-PLUS
   basePath: appBasePath,
   nineGridUrl: appBasePath + '/slshop-h5/activities/LuckyDraw/nineGrid/index.html',
   // 钱包H5页面链接
   walletPayUrl: appBasePath + '/slshop-h5/wallet/pay/index.html',
   walletPwdUrl: appBasePath + '/slshop-h5/wallet/set-password/index.html',
   walletAgreement: appBasePath + '/slshop-h5/agreement/wallet.html',
   carAgreement: appBasePath + '/slshop-h5/agreement/caragreement.html',
   // #endif
   // #ifdef H5
   basePath: basePath as string,
   nineGridUrl: (basePath as string) + '/slshop-h5/activities/LuckyDraw/nineGrid/index.html',
   // 钱包H5页面链接
   walletPayUrl: (basePath as string) + '/slshop-h5/wallet/pay/index.html',
   walletPwdUrl: (basePath as string) + '/slshop-h5/wallet/set-password/index.html',
   walletAgreement: (basePath as string) + '/slshop-h5/agreement/wallet.html',
   carAgreement: (basePath as string) + '/slshop-h5/agreement/caragreement.html',
   // #endif
  
  // 原始appid
  originAppid: 'gh_d15708754b46',   // 正式
  // originAppid: 'gh_160a1989abea',   // 测试
  
  // 商圈积分商户ID
  mchId: '1566733171',
  chatId: 'ww857f67e558e01641',
  // 高德
  // mapKey: '3A4BZ-6CYCG-HNCQE-I6TFJ-GKEVV-MKBD4',
  // 腾讯
  mapKey: '3A4BZ-6CYCG-HNCQE-I6TFJ-GKEVV-MKBD4',

  /**
   * 租户ID；
   * 仅供APP端使用
   */
  tenantId: 1,
  /**
   * 分享链接、海报二维码链接域名
   */
  h5HostUrl: 'https://shopapi.songlei.com',
  /**
   * 公众号appId；
   * 供app端和h5端生成分享链接、海报二维码链接时使用
   */
  wxAppId: 'wx5bdb3c8113221e5d',
  /**
   * 版本更新地址，取的是后台（/public）下的一个json文件，App启动时会自动请求该文件然后判断是否需要更新，json格式请查看 /public/APPUpdate/APPUpdate.md；
   * 仅供APP端使用
   */
  // #ifdef APP-PLUS
  appUpdateUrl: (basePath as URLConfig).BASE_URL == 'https://shopapi.songlei.com' ?
    'https://shopapi.songlei.com/slshop-h5/app/meitaoappversion.json' : 'https://site.songlei.com/slshop-h5/app/devmeitaoappversion.json',
  // #endif
  // #ifdef MP
  appUpdateUrl: (basePath as URLConfig).BASE_URL == 'https://shopapi.songlei.com' ?
    'https://shopapi.songlei.com/slshop-h5/app/meitaoappversion.json' : 'https://site.songlei.com/slshop-h5/app/devmeitaoappversion.json',
  // #endif
  // #ifdef H5
  appUpdateUrl: (basePath as string) == 'https://shopapi.songlei.com' ?
    'https://shopapi.songlei.com/slshop-h5/app/meitaoappversion.json' : 'https://site.songlei.com/slshop-h5/app/devmeitaoappversion.json',
  // #endif
  /**
   * 是否显示 隐私政策、用户协议 相关功能。目前所有app上架到应用宝，苹果等各个商店平台需要隐私政策信息。因为上架手续繁琐，如有需要请查看文档进行修改。
   * 仅供APP端使用
   */
  showPrivacyPolicy: true,
  /**
   * 隐私政策网络地址
   * 仅供APP端使用
   */
  privacyPolicyUrl: 'https://shopapi.songlei.com/slshop-h5/agreement/user.html',
  /**
   * 用户协议网络地址
   * 仅供APP端使用
   */
  protocolUrl: 'https://shopapi.songlei.com/slshop-h5/agreement/useragreement.html',
  debug: false
}

export default envConfig
