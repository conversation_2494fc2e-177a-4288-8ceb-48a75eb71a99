import App from './App.uvue'
import { createSSRApp } from 'vue'
import { createPinia } from 'pinia'
import CuCustom from "@/components/cu-custom/cu-custom.uvue"

// 全局图片格式化函数
const formatImg750 = (url) => {
  if (!url) return "https://img.songlei.com/live/img/no_pic.png"
  if (url.endsWith('gif') && url.startsWith('https://img.songlei.com/')) return url + '-gif_q90_s80';
  else if (url.startsWith('https://img.songlei.com/')) return url + '-jpg_w750_q70';
  return url
}

const formatImg360 = (url) => {
  if (Array.isArray(url)) {
    url = url[0]
  }
  if (!url) return "https://img.songlei.com/live/img/no_pic.png"
  else if (url && url.endsWith('gif') && url.startsWith('https://img.songlei.com/')) return url;
  else if (url.startsWith('https://img.songlei.com/')) return url + '-jpg_w360_q90';
  return url
}

const imgUrl = (url) => {
  return 'https://img.songlei.com/' + url.replace(/^\//, '');
}

const saleNumFilter = (saleNum) => {
  if (saleNum >= 10000) {
    return Math.floor(saleNum / 10000) + '万+'
  } else if (saleNum >= 1000) {
    return Math.floor(saleNum / 1000) + 'k+'
  }
  return saleNum.toString()
}

export function createApp() {
  const app = createSSRApp(App)
  const pinia = createPinia()
  app.use(pinia)
  app.component("cu-custom", CuCustom);

  // 设置全局属性，可在模板中直接使用
  app.config.globalProperties.$formatImg750 = formatImg750
  app.config.globalProperties.$formatImg360 = formatImg360
  app.config.globalProperties.$saleNumFilter = saleNumFilter
  app.config.globalProperties.$imgUrl = imgUrl

  return {
    app,
    pinia
  }
}