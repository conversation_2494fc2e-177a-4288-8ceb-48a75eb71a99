# div-componentlist 组件包

这是从松鼠美妆项目中提取的动态组件渲染系统，包含div-componentlist核心组件及其所有依赖组件。

## 组件结构

```
extracted-components/
├── div-components/
│   ├── div-componentslist/          # 核心渲染组件
│   ├── div-advert/                  # 广告组件
│   ├── div-article-list/            # 文章列表组件
│   ├── div-base/                    # 基础工具组件
│   ├── div-goods-category/          # 商品分类组件
│   ├── div-goods-groups/            # 商品分组组件
│   ├── div-goods-row/               # 商品行组件
│   ├── div-goods/                   # 商品展示组件
│   ├── div-head/                    # 页面头部组件
│   ├── div-image-list/              # 图片列表组件
│   ├── div-living-list/             # 直播列表组件
│   ├── div-nav-button/              # 导航按钮组件
│   ├── div-new-person/              # 新人专享组件
│   ├── div-search/                  # 搜索组件
│   ├── div-swiper/                  # 轮播图组件
│   └── index.css                    # 样式文件
└── activation-coupon/               # 激活券组件

## 核心组件: div-componentslist

### 功能特点
- **动态渲染**: 根据 `componentName` 动态渲染不同组件
- **性能优化**: 
  - 组件数量>20时分批加载(先10个，延迟100ms后全部)
  - 开屏期间隐藏重型组件 (`showHeavy1/showHeavy2`)
- **平台兼容**: 支持微信小程序条件编译

### Props 配置
```javascript
props: {
  componentsList: Array,    // 组件配置数组
  isBack: Boolean,         // 返回按钮显示
  showScreen: Boolean,     // 开屏显示状态
  showMinutes: Number,     // 开屏时长
  pageSelectPoi: Object    // 页面选择POI对象
}
```

### 支持的组件类型
- `headdivComponent` - 页面头部
- `searchComponent` - 搜索框
- `advertComponent` - 广告轮播
- `swiperComponent` - 轮播图
- `navButtonComponent` - 导航按钮
- `goodsComponent` - 商品展示
- `goodsRowComponent` - 商品行
- `goodsGroupingComponent` - 商品分组
- `goodsCategoryComponent` - 商品分类
- `imageListComponent` - 图片列表
- `articleListComponent` - 文章列表
- `livingListComponent` - 直播列表 (仅微信小程序)
- `activateComponent` - 激活券
- `newPersonComponent` - 新人专享

## 使用方法

### 1. 基础使用
```vue
<template>
  <div-componentslist 
    :componentsList="pageData" 
    :isBack="true"
    @getpage="handlePageChange"
    @openRulePopup="handleRulePopup"
  />
</template>

<script>
import divComponentslist from './div-components/div-componentslist/index.vue'

export default {
  components: {
    divComponentslist
  },
  data() {
    return {
      pageData: [
        {
          componentName: 'headdivComponent',
          data: { title: '页面标题' }
        },
        {
          componentName: 'swiperComponent', 
          data: { images: [...] }
        }
      ]
    }
  }
}
</script>
```

### 2. 数据格式
```javascript
// 组件配置数组格式
componentsList: [
  {
    componentName: "advertComponent",  // 组件类型
    data: {                           // 组件数据
      images: [],
      autoplay: true
    },
    id: "123"                        // 组件ID
  }
]
```

## 移植注意事项

### 1. 路径调整
移植到新项目时需要调整以下导入路径：
- `@/components/div-components/xxx` → 相对路径
- `@/components/activation-coupon` → 相对路径

### 2. 依赖检查
确保新项目中存在以下依赖：
- `getApp()` 全局函数或替换为项目特定实现
- uni-app 相关API (如果不是uni-app项目需要适配)

### 3. 样式文件
记得引入 `index.css` 样式文件

### 4. 平台兼容
如果不是微信小程序，需要处理 `#ifdef MP-WEIXIN` 条件编译

## 组件统计
- **总组件数**: 34个Vue文件
- **包大小**: ~312KB
- **活跃组件**: 13个
- **支持组件类型**: 14种

## 扩展说明
该组件系统设计灵活，可以轻松添加新的组件类型：
1. 创建新的组件文件
2. 在div-componentslist中添加对应的模板和导入
3. 注册到components中
4. 支持对应的componentName

适合用于需要动态配置页面内容的项目，如电商、内容管理等场景。