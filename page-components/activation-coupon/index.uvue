<template>
  <view class="coupon-container" :style="{ backgroundColor: value.background }">
    <view class="coupon-content">
      <view class="input-section">
        <view class="search-form">
          <input 
            class="coupon-input" 
            v-model="eventId" 
            type="text" 
            confirm-type="search"
            @confirm="activeAccntNo" 
            placeholder="请输入兑换码"
          />
          <image 
            class="scan-icon" 
            mode="aspectFit"
            src="https://img.songlei.com/share/scan.png"
          />
          <text 
            @tap="scanCode" 
            class="scan-button"
          />
        </view>
      </view>
      <view 
        class="activation-btn" 
        @click="activeAccntNo" 
        :style="{
          backgroundColor: value.btn_background,
          color: value.color
        }"
      >
        兑换
      </view>
    </view>
    <view class="detail-text" :style="{ color: value.detail_color }">
      {{ value.detail }}
    </view>
  </view>
</template>

<script setup lang="uts">
import { ref } from 'vue'
import { activeAccntNo } from '@/utils/api.uts'

type CouponData = {
  background?: string
  btn_background?: string
  detail_color?: string
  detail?: string
  btn_color?: string
  color?: string
  [key: string]: any
}

type UserInfo = {
  id?: string
  erpCid?: string
  [key: string]: any
}

defineProps<{
  modelValue: CouponData
}>()

const eventId = ref('')

const scanCode = () => {
  uni.scanCode({
    scanType: ["barCode", "qrCode"],
    success: (res) => {
      if (res.result) {
        const code = res.result as string
        if (code.startsWith('https://shopapi.songlei.com/slshop-h5/ma/couponActivation')) {
          const urlParts = code.split('?')
          if (urlParts.length > 1) {
            const params = new URLSearchParams(urlParts[1])
            const id = params.get('id')
            if (id) {
              eventId.value = id
              activeAccntNo()
            }
          }
        } else {
          uni.showToast({
            title: '二维码不正确'
          })
        }
      } else {
        uni.showToast({
          title: '请重新扫描'
        })
      }
    }
  })
}

const activeAccntNo = () => {
  const userInfo = uni.getStorageSync('user_info') as UserInfo
  const { id, erpCid } = userInfo

  if (!id || !erpCid) {
    showModel('您当前还未登录，请先登录后在使用该功能')
    return
  }
  
  if (!eventId.value) {
    showModel('请先输入券编码')
    return
  }

  activeAccntNo(eventId.value).then((res: any) => {
    const { code, msg } = res
    if (+code === 0) {
      uni.showModal({
        title: '提示',
        content: '激活成功',
        cancelText: '确定',
        confirmText: '去使用',
        success: (modalRes) => {
          if (modalRes.confirm) {
            uni.navigateTo({
              url: '/pages/coupon/coupon-user-list/index?navTabCur=1'
            })
          }
        }
      })
      eventId.value = ''
    } else {
      showModel(msg)
    }
  })
}

const showModel = (content: string) => {
  uni.showModal({
    title: '提示',
    content,
    showCancel: false
  })
}
</script>

<style scoped>
.coupon-container {
  padding: 10upx 20upx;
}

.coupon-content {
  display: flex;
  justify-content: center;
}

.input-section {
  flex: 1;
}

.search-form {
  height: 40px;
  line-height: 100%;
  margin-right: 20rpx;
  margin-left: 0;
  border: 1rpx solid #EAEAEA;
  background: #fff;
  align-items: center;
  padding-left: 30upx;
  border-radius: 40upx;
  display: flex;
}

.coupon-input {
  flex: 1;
}

.scan-icon {
  max-height: 48rpx;
  width: 4rpx;
  height: 60rpx;
  width: 20rpx;
}

.scan-button {
  font-size: 32rpx;
  padding: 0rpx 20rpx 0rpx 16rpx;
}

.activation-btn {
  width: 180upx;
  height: 40px;
  font-size: 28upx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  color: #fff;
  border-radius: 40upx;
}

.detail-text {
  padding: 0 10upx;
}
</style>