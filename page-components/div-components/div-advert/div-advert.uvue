<template>
  <view v-if="props.value">
    <div-status-bar v-if="props.isStatusBar" :isStatusBar="props.isStatusBar" :value="props.value" />
    <div-hot-horizontal-zone v-else-if="props.value.advertType == 'horizontalLine'" :isStatusBar="props.isStatusBar"
      :value="props.value" />
    <one-line-one v-else-if="props.value.advertType == 'oneLineOne'" :isStatusBar="props.isStatusBar"
      :value="props.value" />
    <div-tab-swiper v-else-if="props.value.advertType == 'tabSwiper'" :isStatusBar="props.isStatusBar"
      :value="props.value" />
    <div-tab-anchor v-else-if="props.value.advertType == 'tabAnchor'" :isStatusBar="props.isStatusBar"
      :value="props.value" />
    <seckill-hot v-else-if="props.value.advertType == 'seckillHotZone'" :isStatusBar="props.isStatusBar"
      :value="props.value" />
    <div-hot-swiper v-else v-model="props.value" :is-status-bar="props.isStatusBar" />
  </view>
</template>

<script setup lang="uts">
  import divHotSwiper from "./div-hot-swiper.uvue"
  import divHotHorizontalZone from "./div-hot-horizontal-zone.uvue"
  import oneLineOne from "./oneLineOne.uvue"
  import seckillHot from "./seckillHot.uvue"
  import divTabSwiper from "./div-tab-swiper.uvue"
  import divTabAnchor from "./div-tab-anchor.uvue"
  import divStatusBar from "./div-status-bar.uvue"

  type AdvertData = {
    pageUrl ?: string
    imageUrl ?: string
    height ?: number
    advertType ?: string
  }

  const props = defineProps<{
    value : AdvertData
    isStatusBar ?: boolean
  }>()
</script>