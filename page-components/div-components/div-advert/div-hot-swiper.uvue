<template>
  <!-- 轮播图组件 -->
  <view class="bg-white" :style="containerStyles" :class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''">
    <view v-show="newData.swiperType == 'card-swiper'" :class="'bg-' + theme.backgroundColor"
      :style="{ height: `${divHeight}rpx` }"></view>

    <view style="position: relative">
      <swiper @change="cardSwiper" class="screen-swiper" :class="newData.dotStyle" :circular="true"
        :indicator-color="indicatorColor" :indicator-active-color="indicatorActiveColor"
        :indicator-dots="newData && newData.swiperList && newData.swiperList.length > 1 && newData.dotStyle != 'none'"
        :style="{ height: `${divHeight}rpx` }" :autoplay="newData.interval > 0" :interval="newData.interval">
        <swiper-item v-for="(item, index) in newData.swiperList" :key="index">
          <view class="hot-zone" :key="index" :style="hotZoneStyles">
            <image :src="$formatImg750(item.imageUrl)" :show-menu-by-longpress="newData.canLongPress == 1" :style="imageStyles"></image>
            <view class="zone" v-for="(zone, index) in item.hotZones" :key="index" :style="getZonePositionStyles(zone)">
              <view class="hot-area" @tap="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
                <text class="hot-txt" hover-class="none">{{ zone.pageName }}{{ newData.borderRadius }}</text>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup lang="uts">
  import { gotoPage } from '../div-base/div-page-urls.uts'
  import { useSystemStore } from '@/stores/system'
  import { useGlobalDataStore } from '@/stores/globalData'

  // 定义类型接口
  interface HotZone {
    pageUrl : string
    pageName : string
    widthPer : number
    heightPer : number
    topPer : number
    leftPer : number
  }

  interface SwiperItem {
    imageUrl : string
    hotZones : HotZone[]
  }

  interface ComponentData {
    swiperType : string
    height : number
    interval : number
    borderRadius : number
    radiusLeftTop ?: number
    radiusRightTop ?: number
    radiusLeftBottom ?: number
    radiusRightBottom ?: number
    imageSpacing : number
    dotStyle ?: string
    indicatorColor ?: string
    indicatorActiveColor ?: string
    canLongPress ?: number
    background ?: string
    bgImage ?: string
    marginBottomSpacing : number
    marginTopSpacing : number
    marginLeftSpacing : number
    marginRightSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
    swiperList : SwiperItem[]
    pagesLength : number
  }

  // Props 定义
  interface Props {
    modelValue : ComponentData | null
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      swiperType: 'screen-swiper',
      height: 150,
      interval: 3000,
      borderRadius: 0,
      imageSpacing: 0,
      marginBottomSpacing: 0,
      marginTopSpacing: 0,
      marginLeftSpacing: 0,
      marginRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0,
      swiperList: [],
      pagesLength: 1
    })
  })

  // Store 和全局数据
  const systemStore = useSystemStore()
  const globalDataStore = useGlobalDataStore()

  // 响应式数据
  const newData = ref<ComponentData>(props.modelValue || {
    swiperType: 'screen-swiper',
    height: 150,
    interval: 3000,
    borderRadius: 0,
    imageSpacing: 0,
    marginBottomSpacing: 0,
    marginTopSpacing: 0,
    marginLeftSpacing: 0,
    marginRightSpacing: 0,
    paddingBottomSpacing: 0,
    paddingTopSpacing: 0,
    paddingLeftSpacing: 0,
    paddingRightSpacing: 0,
    swiperList: [],
    pagesLength: 1
  })

  const theme = ref(globalDataStore.theme)
  const curDot = ref<number>(0)
  const lastSecs = ref<number>(40000)
  const hour = ref<number>(10)
  const pagesLength = ref<number>(1)
  const timer = ref<number | null>(null)

  // 计算属性
  const isPhone = computed(() => systemStore.isPhone)

  const divHeight = computed(() : number => {
    return isPhone.value ? newData.value.height * 2 : newData.value.height * 1.2
  })

  const indicatorColor = computed(() : string => {
    if (newData.value && newData.value.indicatorColor) {
      return newData.value.indicatorColor
    }
    return '#aaa'
  })

  const indicatorActiveColor = computed(() : string => {
    if (newData.value && newData.value.indicatorActiveColor) {
      return newData.value.indicatorActiveColor
    }
    return '#eee'
  })

  const formatSeconds = computed(() : string => {
    const result = parseInt(lastSecs.value.toString())
    const h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600).toString()
    const m = Math.floor((result / 60) % 60) < 10 ? '0' + Math.floor((result / 60) % 60) : Math.floor((result / 60) % 60).toString()
    const s = Math.floor(result % 60) < 10 ? '0' + Math.floor(result % 60) : Math.floor(result % 60).toString()

    let res = ''
    if (h !== '00') res += `${h}:`
    if (m !== '00') res += `${m}:`
    res += `${s}`
    return res
  })

  const containerStyles = computed(() => {
    return {
      backgroundSize: '100% auto',
      backgroundRepeat: 'no-repeat',
      backgroundImage: `url(${newData.value.bgImage})`,
      backgroundColor: newData.value.background,
      marginBottom: `${newData.value.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.value.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.value.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.value.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.value.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.value.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.value.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.value.paddingRightSpacing * 2}rpx`
    }
  })

  const hotZoneStyles = computed(() => {
    return {
      width: '100%',
      overflowX: 'scroll',
      overflowY: 'hidden',
      height: `${divHeight.value}rpx`,
      marginTop: '-1px'
    }
  })

  const imageStyles = computed(() => {
    return {
      width: '100%',
      height: `${divHeight.value}rpx`,
      borderTopLeftRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusLeftTop * 2 + 'rpx' : '0rpx'}`,
      borderTopRightRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusRightTop * 2 + 'rpx' : '0rpx'}`,
      borderBottomLeftRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusLeftBottom * 2 + 'rpx' : '0rpx'}`,
      borderBottomRightRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusRightBottom * 2 + 'rpx' : '0rpx'}`
    }
  })

  // 方法定义
  const leftImg = () => {
    const num = newData.value.swiperList.length - 1
    if (curDot.value <= 0) {
      curDot.value = num
    } else {
      curDot.value--
    }
  }

  const rightImg = () => {
    const num = newData.value.swiperList.length - 1
    if (curDot.value >= num) {
      curDot.value = 0
    } else {
      curDot.value++
    }
  }

  const cardSwiper = (e : any) => {
    curDot.value = e.detail.current
  }

  const getZoneStyle = (val : number) : string => {
    return `${(val || 0) * 100}%`
  }

  const getZonePositionStyles = (zone : HotZone) => {
    return {
      width: zone.pageUrl == 'customcomponentseckilltimer' ? '100%' : getZoneStyle(zone.widthPer),
      height: getZoneStyle(zone.heightPer),
      top: getZoneStyle(zone.topPer),
      left: getZoneStyle(zone.leftPer)
    }
  }


  const handleZoneClick = (e : any, page : string) => {
    console.log(e, page)
    if (page) {
      gotoPage(page, pagesLength.value)
    }
  }

  const getLastTime = () => {
    timer.value = setInterval(() => {
      lastSecs.value--
      if (lastSecs.value === 0 && timer.value) {
        clearInterval(timer.value)
      }
    }, 1000)
  }

  // 初始化
  const initComponent = () => {
    getLastTime()
    // 监听当前页面栈的个数内容
    const pages = getCurrentPages()
    pagesLength.value = pages.length // 当前页面栈的个数
  }

  // 生命周期
  onMounted(() => {
    initComponent()
  })

  onUnmounted(() => {
    if (timer.value) {
      clearInterval(timer.value)
    }
  })

  // 监听 props 变化
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      newData.value = newValue
    }
  }, { deep: true, immediate: true })
</script>

<style scoped lang="scss">
  .screen-swiper {
    min-height: 90upx !important;
  }

  .hot-zone {
    position: relative;
  }

  .zone {
    position: absolute;

    .hot-area {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: rgba($color: #000000, $alpha: 0);

      .hot-txt {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        height: 40px;
        color: rgba($color: #000000, $alpha: 0);
      }
    }
  }

  .left-back,
  .right-forword {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 60rpx;
    color: #c8b19b;
  }

  .left-back {
    left: 0;
  }

  .right-forword {
    right: 0;
  }

  .sec-container {
    display: flex;
    align-items: center;
    height: 25px;
  }

  .seckill_num {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: inline-flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    background-image: -webkit-gradient(linear, right top, left top, from(#f10101), to(#fe4d17));
    background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
    background-image: linear-gradient(-90deg, #f10101, #fe4d17);
    font-size: 24rpx;
    color: #fff;
    letter-spacing: 0;
    border-radius: 2rpx 2rpx 2rpx 0;
    padding: 2px;
    z-index: 4;
    width: 80rpx;
  }

  .timer {
    background: #ecb4d9;
    color: #f10101;
    font-size: 24rpx;
    padding: 3px 6px 3px 10px;
    margin-left: -6px;
  }
</style>