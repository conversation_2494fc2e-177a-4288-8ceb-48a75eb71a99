<template>
  <!-- 图文广告显示组件 -->
  <view :style="{
      backgroundSize: '100% auto',
      backgroundRepeat: 'no-repeat',
      backgroundImage: isStatusBar ? '' : `url(${newData.bgImage})`,
      backgroundColor: newData.background,
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${isStatusBar ? computedHeight - newData.searchHeight : newData.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
      height: `${computedHeight}px`,
    }" :class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''">
    <view :style="{
        width: '100%',
      }">
      <view style="display: flex; flex-direction: row; align-items: center">
        <template v-for="(item, index) in newData.swiperList" :key="index">
          <view class="hot-zone" :style="{
              width: item.width ? item.width * 2 + 'rpx' : '100%',
            }">
            <image :src="$formatImg750(item.imageUrl)" mode="aspectFit"
              :show-menu-by-longpress="newData.canLongPress == 1" :style="{
                width: '100%',
                display: 'block',
                height: `${computedHeight}px`,
                marginTop: '-1px',
                borderTopLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftTop * 2 + 'rpx' : '0rpx'}`,
                borderTopRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightTop * 2 + 'rpx' : '0rpx'}`,
                borderBottomLeftRadius: `${newData.borderRadius == 1 ? newData.radiusLeftBottom * 2 + 'rpx' : '0rpx'}`,
                borderBottomRightRadius: `${newData.borderRadius == 1 ? newData.radiusRightBottom * 2 + 'rpx' : '0rpx'}`,
              }"></image>
            <view class="zone" v-for="(zone, index) in item.hotZones" :key="index" :style="{
                width: getZoneStyle(zone.widthPer),
                height: getZoneStyle(zone.heightPer),
                top: getZoneStyle(zone.topPer),
                left: getZoneStyle(zone.leftPer),
              }">
              <view class="hot-area" @click="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
                <text class="hot-txt" hover-class="none">{{ zone.pageName }}{{ newData.borderRadius }}</text>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  import { gotoPage } from '../div-base/div-page-urls.uts'
  import { useSystemStore } from '@/stores/system'
  import { useGlobalDataStore } from '@/stores/globalData'
  import { ref, computed, onMounted, watch } from 'vue'

  // 定义类型接口
  interface HotZone {
    pageUrl : string
    pageName : string
    widthPer : number
    heightPer : number
    topPer : number
    leftPer : number
  }

  interface SwiperItem {
    imageUrl : string
    width ?: number
    hotZones : HotZone[]
  }

  interface ComponentData {
    pageUrl ?: string
    imageUrl ?: string
    height : number
    borderRadius ?: number
    radiusLeftTop ?: number
    radiusRightTop ?: number
    radiusLeftBottom ?: number
    radiusRightBottom ?: number
    canLongPress ?: number
    background ?: string
    bgImage ?: string
    marginBottomSpacing : number
    marginTopSpacing : number
    marginLeftSpacing : number
    marginRightSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
    searchHeight ?: number
    swiperList : SwiperItem[]
  }

  // Props 定义
  interface Props {
    modelValue : ComponentData | null
    isStatusBar : boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      pageUrl: '',
      imageUrl: '',
      height: 100,
      marginBottomSpacing: 0,
      marginTopSpacing: 0,
      marginLeftSpacing: 0,
      marginRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0,
      swiperList: []
    }),
    isStatusBar: false
  })

  // Store
  const systemStore = useSystemStore()
  const globalDataStore = useGlobalDataStore()

  // 响应式数据
  const newData = ref<ComponentData>(props.modelValue || {
    pageUrl: '',
    imageUrl: '',
    height: 100,
    marginBottomSpacing: 0,
    marginTopSpacing: 0,
    marginLeftSpacing: 0,
    marginRightSpacing: 0,
    paddingBottomSpacing: 0,
    paddingTopSpacing: 0,
    paddingLeftSpacing: 0,
    paddingRightSpacing: 0,
    swiperList: []
  })

  const theme = ref(globalDataStore.theme)
  const pagesLength = ref<number>(1)

  // 计算属性
  const StatusBar = computed(() => systemStore.StatusBar)
  const CustomBar = computed(() => systemStore.CustomBar)

  const computedHeight = computed(() : number => {
    return CustomBar.value - StatusBar.value
  })

  // 方法定义

  const getZoneStyle = (val : number) : string => {
    return `${(val || 0) * 100}%`
  }

  const handleZoneClick = (e : any, page : string) => {
    console.log(e, page)
    if (page) {
      gotoPage(page, pagesLength.value)
    }
  }

  // 生命周期
  onMounted(() => {
    // 监听当前页面栈的个数内容
    const pages = getCurrentPages()
    pagesLength.value = pages.length // 当前页面栈的个数
  })

  // 监听 props 变化
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      newData.value = newValue
    }
  }, { deep: true, immediate: true })
</script>

<style scoped lang="scss">
  .hot-zone {
    position: relative;
  }

  .zone {
    position: absolute;
    // cursor: pointer;
    // border: 1px solid #155bd4;
    // background: rgba(21, 91, 212, 0.5);
    // font-size: 12px;
    // color: #fff;
    // user-select: auto;
    // touch-action: none;

    .hot-area {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;

      .hot-txt {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        height: 40px;
        color: rgba($color: #000000, $alpha: 0);
      }
    }
  }
</style>