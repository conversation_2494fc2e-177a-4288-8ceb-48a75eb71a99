<template>
	<view class="bg-white" :style="{
      backgroundSize: '100% auto',
	  backgroundRepeat:'no-repeat',
      backgroundImage: `url(${newData.bgImage})`,
      backgroundColor: newData.background,
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
    }" :class="
      newData.background && newData.background.indexOf('bg-') != -1
        ? newData.background
        : ''
    ">
		<view class="cu-bar" style="min-height: 20px">
			<scroll-view scroll-x="true" style=" width: 100%; white-space: nowrap;" :scroll-into-view="'id-'+curDot">
				<view class="nav text-white text-sm">
					<view style="position: relative; display: flex">
						<view v-for="(item, index) in newData.swiperList" :key="item.id">
							<image @click="tabSelect" :data-index="index" :id="'id-'+index"
								:show-menu-by-longpress="newData.canLongPress==1" :style="{
                  display: 'block',
                  overflowX: 'inherit',
                  overflowY: 'hidden',
                  color:(newData.textColor||'#fff'),
                  fontSize:(newData.textSize||24)+'rpx',
                  marginLeft: ((newData.itemSpace*2)||0) + 'rpx',
                  marginRight: ((newData.itemSpace*2)||0) + 'px',
                  width: ((item.itemWidth*2)||180)+'rpx',
                  minWidth: ((item.itemWidth*2)||180)+'rpx',
                  padding:0,
                  textAlign:'center',
                  marginTop: 0,
                  marginBottom: 0,	
                  borderTopLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftTop*2)+'rpx':'0rpx'}`,
                  borderTopRightRadius:`${newData.borderRadius == 1 ? (newData.radiusRightTop*2)+'rpx' : '0rpx'}`,
                  borderBottomLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
                  borderBottomRightRadius:`${newData.borderRadius == 1 ?(newData.radiusRightBottom*2)+'rpx':'0rpx'}`,
                 }" class="img" :src="item.iconPath" mode="widthFix" />
							<image @click="tabSelect" :data-index="index" :id="'id-'+index" :style="{
                  display: 'block',
                  overflowX: 'inherit',
                  overflowY: 'hidden',
                  color: (newData.selectTextColor||'#ff0'),
                  fontSize:(newData.selectTextSize||28)+'rpx',
                  marginLeft: ((newData.itemSpace*2)||0) + 'rpx',
                  marginRight: ((newData.itemSpace*2)||0) + 'px',
                  width: ((item.itemWidth*2)||180)+'rpx',
                  minWidth: ((item.itemWidth*2)||180)+'rpx',
                  padding:0,
                  textAlign:'center',
                  marginTop: 0,
                  marginBottom: 0,	
                  borderTopLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftTop*2)+'rpx':'0rpx'}`,
                  borderTopRightRadius:`${newData.borderRadius == 1 ? (newData.radiusRightTop*2)+'rpx' : '0rpx'}`,
                  borderBottomLeftRadius:`${newData.borderRadius == 1 ? (newData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
                  borderBottomRightRadius:`${newData.borderRadius == 1 ?(newData.radiusRightBottom*2)+'rpx':'0rpx'}`,
                  opacity: `${index == curDot ? 1 : 0}`,
                  position: 'absolute',
                  top: 0
                 }" class="img" :src="item.selectedIconPath" mode="widthFix" />
						</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<view v-show="newData.swiperType == 'card-swiper'" :class="'bg-' + theme.backgroundColor"
			:style="{  height: `${newData.swiperList[curDot].height>0?(newData.swiperList[curDot].height*2):(newData.height * 2)}rpx` }">
		</view>
		<template v-for="(item, swiperIndex) in newData.swiperList" :key="swiperIndex">
			<!-- <scroll-view scroll-x="true" scroll-left="0" :key="index"> -->
			<view class="hot-zone" :style="{
							  width: item.width ? (item.width*2) + 'rpx' : '100%',
							  overflowX: 'scroll',
							  overflowY: 'hidden',
							  lineHeight:'0',
								borderTopLeftRadius:`${newData.borderRadius == '1' ? (newData.radiusLeftTop*2)+'rpx':'0px'}`,
								borderTopRightRadius:`${newData.borderRadius == '1' ? (newData.radiusRightTop*2)+'rpx' : '0px'}`,
								borderBottomLeftRadius:`${newData.borderRadius == '1' ? (newData.radiusLeftBottom*2)+'rpx':'0px'}`,
								borderBottomRightRadius:`${newData.borderRadius == '1' ?(newData.radiusRightBottom*2)+'rpx':'0px'}`,
							}"  :id="'section'+swiperIndex">
				<image :src="$formatImg750(item.imageUrl)" :show-menu-by-longpress="newData.canLongPress==1"
					mode="widthFix" :style="{
								height:'auto',
								width: '100%',
								display: 'block',
								borderRadius: `${newData.borderRadius == '1' ? '12rpx' : '0px'}`,
							  }"></image>

				<view class="zone" v-for="(zone, index) in item.hotZones" :key="index" :style="{
								width: zone.pageUrl=='customcomponentseckilltimer'?'100%':getZoneStyle(zone.widthPer),
								height: getZoneStyle(zone.heightPer),
								top: getZoneStyle(zone.topPer),
								left: getZoneStyle(zone.leftPer),
							  }">
					<view class="hot-area" @tap="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
						<text class="hot-txt" hover-class="none">
							{{ zone.pageName }}{{zone.pageUrl.startsWith('/pages/seckill/seckill-list/index')}}
						</text>
					</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted, getCurrentInstance } from 'vue'
	import { nextTick } from 'vue'
	import { useGlobalDataStore } from '/stores/globalData.uts'
	import popSpu from "./relate-component/pop-spu.uvue"
	import {
		gotoPage
	} from "../div-base/div-page-urls.js"

	const props = defineProps({
		value: {
			type: Object,
			default: () => ({
				swiperType: "screen-swiper",
				height: 150,
				interval: 3000,
				borderRadius: 0,
				imageSpacing: 0,
				swiperList: [],
				pagesLength: 1,
			})
		}
	})

	const globalDataStore = useGlobalDataStore()
	const theme = computed(() => globalDataStore.theme)
	const newData = ref(props.value)
	const curDot = ref(0)
	const hour = ref(10)
	const pagesLength = ref(1)
	const instance = getCurrentInstance()


	const getHeight = computed(() => {
		return function(curDotIndex) {
			if (newData.value.swiperList[curDotIndex].contentType == 'pop') {
				return '760rpx'
			} else {
				return (newData.value.swiperList[curDotIndex].height > 0 ? (newData.value.swiperList[curDotIndex].height * 2) : (newData.value.height * 2)) + 'rpx'
			}
		}
	})

	const tabSelect = (e) => {
		let tabIndex = e.currentTarget.dataset.index
		if (+tabIndex === +curDot.value) {
			return
		}
		curDot.value = tabIndex
		console.log("=====11=====",'#section' + tabIndex)
		nextTick(()=> {
			console.log("====22======",'#section' + tabIndex)
			let element = "#section" + tabIndex
			let query = uni.createSelectorQuery().in(instance)
			query.select(element).boundingClientRect((rect)=> {
				console.log("====33======",rect)
				uni.pageScrollTo({
					scrollTop: rect.top,
					duration: 500
				})
			}).exec()
		})
	}
		
	const getZoneStyle = (val) => {
		return `${(val || 0) * 100}%`
	}
	
	const handleZoneClick = (e, page) => {
		if (page) {
			gotoPage(page, pagesLength.value)
		}
	}

	onMounted(() => {
		const pages = getCurrentPages()
		pagesLength.value = pages.length
	})
</script>

<style scoped lang="scss">
	.img {
		width: 100%;
		height: auto;
	}

	.scrollBox {
		overflow-x: scroll;
	}

	.screen-swiper {
		min-height: 90upx !important;
	}

	.hot-zone {
		position: relative;
	}

	.zone {
		position: absolute;
		// cursor: pointer;
		// border: 1px solid #155bd4;
		// background: rgba(21, 91, 212, 0.5);
		// font-size: 12px;
		// color: #fff;

		.hot-area {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100%;
			height: 100%;
			background: rgba($color: #000000, $alpha: 0);

			.hot-txt {
				overflow: hidden;
				text-overflow: ellipsis;
				-webkit-line-clamp: 2;
				height: 40px;
				color: rgba($color: #000000, $alpha: 0);
			}
		}
	}

	.left-back,
	.right-forword {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		font-size: 60rpx;
		color: #c8b19b;
	}

	.left-back {
		left: 0;
	}

	.right-forword {
		right: 0;
	}

	.sec-container {
		display: flex;
		align-items: center;
		height: 25px;
	}

	.seckill_num {
		display: -webkit-inline-box;
		display: -webkit-inline-flex;
		display: inline-flex;
		-webkit-box-pack: center;
		-webkit-justify-content: center;
		justify-content: center;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;
		background-image: -webkit-gradient(linear,
				right top,
				left top,
				from(#f10101),
				to(#fe4d17));
		background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
		background-image: linear-gradient(-90deg, #f10101, #fe4d17);
		// box-shadow: 0 4rpx 8rpx 0 rgb(241 1 1 / 20%);
		font-size: 24rpx;
		color: #fff;
		letter-spacing: 0;
		border-radius: 2rpx 2rpx 2rpx 0;
		padding: 2px;
		z-index: 4;
		width: 80rpx;
	}

	.timer {
		background: #ecb4d9;
		color: #f10101;
		font-size: 24rpx;
		padding: 3px 6px 3px 10px;
		margin-left: -6px;
	}
</style>