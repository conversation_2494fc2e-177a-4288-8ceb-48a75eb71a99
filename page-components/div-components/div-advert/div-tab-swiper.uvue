<template>
  <view class="swiper-container" :style="containerStyle" :class="backgroundClass">
    <!-- 顶部导航栏 -->
    <view class="nav-bar" v-if="hasNavigation">
      <scroll-view scroll-x="true" class="nav-scroll" :scroll-into-view="`nav-item-${curDot}`">
        <view class="nav-content">
          <view class="nav-items flex-row">
            <view v-for="(item, index) in swiperList" :key="item.id || index" class="nav-item-wrapper"
              :style="getNavItemSpacing(index)">
              <!-- 默认状态图标 -->
              <image :id="`nav-item-${index}`" :src="item.iconPath" :style="getNavItemStyle(item, index, false)"
                class="nav-icon" mode="widthFix" :show-menu-by-longpress="canLongPress" @click="selectTab(index)" />
              <!-- 选中状态图标 -->
              <image :src="item.selectedIconPath" :style="getNavItemStyle(item, index, true)"
                class="nav-icon nav-icon-selected" mode="widthFix" @click="selectTab(index)" />
            </view>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 轮播内容区域 -->
    <view class="swiper-wrapper">
      <swiper :current="curDot" :circular="true" :duration="300" :style="swiperStyle" class="main-swiper"
        indicator-color="#cccccc" indicator-active-color="#ffffff" @animationfinish="handleSwiperChange">
        <swiper-item v-for="(item, index) in swiperList" :key="index" class="swiper-item">
          <!-- 排行榜组件 -->
          <pop-spu v-if="item.contentType === 'pop'" :popProps="item" />

          <!-- 图片轮播项 -->
          <view v-else class="image-container" :style="getImageContainerStyle(item)">
            <image :src="$formatImg750(item.imageUrl)" :style="getImageStyle(item)" class="swiper-image"
              mode="scaleToFill" />

            <!-- 热区 -->
            <view v-for="(zone, zoneIndex) in item.hotZones" :key="zoneIndex" class="hot-zone"
              :style="getHotZoneStyle(zone)">
              <view class="hot-area" @tap="handleZoneClick(zone.pageUrl)" hover-class="none">
                <text class="hot-text" hover-class="none">
                  {{ zone.pageName }}
                </text>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, toRefs } from 'vue'
  import { useGlobalDataStore } from '/stores/globalData.uts'
  import popSpu from "./relate-component/pop-spu.uvue"
  import { gotoPage } from "../div-base/div-page-urls.js"

  // Props 定义
  const props = defineProps({
    value: {
      type: Object,
      default: () => ({
        swiperType: "screen-swiper",
        height: 150,
        interval: 3000,
        borderRadius: 0,
        imageSpacing: 0,
        swiperList: [],
        pagesLength: 1,
        canLongPress: 0,
        textColor: '#fff',
        textSize: 24,
        selectTextColor: '#ff0',
        selectTextSize: 28,
        itemSpace: 0,
        radiusLeftTop: 0,
        radiusRightTop: 0,
        radiusLeftBottom: 0,
        radiusRightBottom: 0,
        marginBottomSpacing: 0,
        marginTopSpacing: 0,
        marginLeftSpacing: 0,
        marginRightSpacing: 0,
        paddingBottomSpacing: 0,
        paddingTopSpacing: 0,
        paddingLeftSpacing: 0,
        paddingRightSpacing: 0,
        bgImage: '',
        background: ''
      })
    }
  })

  // 解构 props
  const { value } = toRefs(props)

  // Store
  const globalDataStore = useGlobalDataStore()

  // 响应式数据
  const curDot = ref(0)
  const pagesLength = ref(1)
  const lastSecs = ref(40000)
  let timer = null

  // 计算属性
  const theme = computed(() => globalDataStore.theme)
  const swiperList = computed(() => value.value.swiperList || [])
  const canLongPress = computed(() => value.value.canLongPress === 1)
  const hasNavigation = computed(() => swiperList.value.some(item => item.iconPath))

  // 容器样式
  const containerStyle = computed(() => {
    const data = value.value
    return {
      backgroundSize: '100% auto',
      backgroundRepeat: 'no-repeat',
      backgroundImage: data.bgImage ? `url(${data.bgImage})` : 'none',
      backgroundColor: data.background || 'transparent',
      marginBottom: `${(data.marginBottomSpacing || 0) * 2}rpx`,
      marginTop: `${(data.marginTopSpacing || 0) * 2}rpx`,
      marginLeft: `${(data.marginLeftSpacing || 0) * 2}rpx`,
      marginRight: `${(data.marginRightSpacing || 0) * 2}rpx`,
      paddingBottom: `${(data.paddingBottomSpacing || 0) * 2}rpx`,
      paddingTop: `${(data.paddingTopSpacing || 0) * 2}rpx`,
      paddingLeft: `${(data.paddingLeftSpacing || 0) * 2}rpx`,
      paddingRight: `${(data.paddingRightSpacing || 0) * 2}rpx`,
    }
  })

  // 背景类名
  const backgroundClass = computed(() => {
    const bg = value.value.background
    return bg && bg.indexOf('bg-') !== -1 ? bg : 'bg-white'
  })

  // 轮播样式
  const swiperStyle = computed(() => ({
    height: getCurrentHeight()
  }))

  // 获取当前轮播项高度
  const getCurrentHeight = () => {
    const currentItem = swiperList.value[curDot.value]
    if (!currentItem) return `${value.value.height * 2}rpx`

    if (currentItem.contentType === 'pop') {
      return '760rpx'
    }

    const itemHeight = currentItem.height > 0 ? currentItem.height * 2 : value.value.height * 2
    return `${itemHeight}rpx`
  }

  // 导航项间距样式
  const getNavItemSpacing = (index) => {
    const spacing = (value.value.itemSpace || 0) * 2
    return {
      marginLeft: index === 0 ? '0rpx' : `${spacing}rpx`,
      marginRight: `${spacing}rpx`
    }
  }

  // 导航项样式
  const getNavItemStyle = (item, index, isSelected) => {
    const data = value.value
    const isCurrentSelected = index === curDot.value
    const opacity = isSelected ? (isCurrentSelected ? 1 : 0) : 1
    const position = isSelected ? 'absolute' : 'relative'

    return {
      display: 'block',
      width: `${(item.itemWidth || 90) * 2}rpx`,
      minWidth: `${(item.itemWidth || 90) * 2}rpx`,
      color: isSelected ? (data.selectTextColor || '#ff0') : (data.textColor || '#fff'),
      fontSize: `${isSelected ? (data.selectTextSize || 28) : (data.textSize || 24)}rpx`,
      textAlign: 'center',
      opacity,
      position,
      top: isSelected ? '0' : 'auto',
      ...getBorderRadius()
    }
  }

  // 边框圆角样式
  const getBorderRadius = () => {
    const data = value.value
    if (data.borderRadius !== 1) return {}

    return {
      borderTopLeftRadius: `${(data.radiusLeftTop || 0) * 2}rpx`,
      borderTopRightRadius: `${(data.radiusRightTop || 0) * 2}rpx`,
      borderBottomLeftRadius: `${(data.radiusLeftBottom || 0) * 2}rpx`,
      borderBottomRightRadius: `${(data.radiusRightBottom || 0) * 2}rpx`
    }
  }

  // 图片容器样式
  const getImageContainerStyle = (item) => ({
    width: '100%',
    height: `${item.height > 0 ? item.height * 2 : value.value.height * 2}rpx`,
    marginTop: '-1px',
    position: 'relative'
  })

  // 图片样式
  const getImageStyle = (item) => ({
    height: `${item.height > 0 ? item.height * 2 : value.value.height * 2}rpx`,
    width: '100%',
    ...getBorderRadius()
  })

  // 热区样式
  const getHotZoneStyle = (zone) => {
    const isFullWidth = zone.pageUrl === 'customcomponentseckilltimer'
    return {
      position: 'absolute',
      width: isFullWidth ? '100%' : `${(zone.widthPer || 0) * 100}%`,
      height: `${(zone.heightPer || 0) * 100}%`,
      top: `${(zone.topPer || 0) * 100}%`,
      left: `${(zone.leftPer || 0) * 100}%`
    }
  }

  // 方法
  const selectTab = (index) => {
    if (index === curDot.value) return
    curDot.value = index
  }

  const handleSwiperChange = (e) => {
    const newIndex = e.detail.current
    if (curDot.value === newIndex) return
    curDot.value = newIndex
  }

  const handleZoneClick = (pageUrl) => {
    if (pageUrl) {
      gotoPage(pageUrl, pagesLength.value)
    }
  }

  // 定时器相关
  const startTimer = () => {
    if (timer) clearInterval(timer)

    timer = setInterval(() => {
      lastSecs.value--
      if (lastSecs.value <= 0) {
        clearInterval(timer)
        timer = null
      }
    }, 1000)
  }

  const stopTimer = () => {
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  }

  // 生命周期
  onMounted(() => {
    startTimer()
    const pages = getCurrentPages()
    pagesLength.value = pages.length
  })

  onUnmounted(() => {
    stopTimer()
  })
</script>

<style scoped lang="scss">
  .swiper-container {
    position: relative;
    overflow: hidden;
  }

  .nav-bar {
    min-height: 20px;
  }

  .nav-scroll {
    width: 100%;
    white-space: nowrap;
  }

  .nav-content {
    position: relative;
    display: flex;
  }

  .nav-items {
    position: relative;
    display: flex;
    align-items: center;
  }

  .nav-item-wrapper {
    position: relative;
    display: inline-block;
  }

  .nav-icon {
    display: block;
    width: 100%;
    height: auto;
    overflow: hidden;
  }

  .nav-icon-selected {
    position: absolute;
    top: 0;
    left: 0;
  }

  .swiper-wrapper {
    position: relative;
  }

  .main-swiper {
    min-height: 90rpx;
  }

  .swiper-item {
    position: relative;
  }

  .image-container {
    position: relative;
    overflow: hidden;
  }

  .swiper-image {
    display: block;
    width: 100%;
  }

  .hot-zone {
    position: absolute;
  }

  .hot-area {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0);
  }

  .hot-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    height: 40px;
    color: rgba(0, 0, 0, 0);
  }
</style>