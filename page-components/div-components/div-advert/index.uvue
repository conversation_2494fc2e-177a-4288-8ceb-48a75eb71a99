<template>
  <view class="div-advert">
    <cu-custom v-if="isStatusBar" :bgColor="modelValue?.background" :bgImage="modelValue?.bgImage">
      <block slot="backText">返回</block>
      <block slot="marchContent">
        <advert :value="modelValue" :isStatusBar="true" />
      </block>
    </cu-custom>
    <advert v-else :value="modelValue" />
  </view>
</template>

<script setup lang="uts">
import advert from "./div-advert.uvue"

type AdvertData = {
  background?: string
  bgImage?: string
  [key: string]: any
}

defineProps<{
  modelValue: AdvertData
  isStatusBar?: boolean
}>()
</script>