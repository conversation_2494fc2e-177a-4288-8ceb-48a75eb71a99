<template>
  <!-- 图文广告显示组件 -->
  <view :style="{
      backgroundSize: '100% auto',
	  backgroundRepeat:'no-repeat',
      backgroundImage: isStatusBar?'':`url(${newData.bgImage})`,
      backgroundColor: newData.background,
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.paddingRightSpacing * 2}rpx`,
    }" :class="
      newData.background && newData.background.indexOf('bg-') != -1
        ? newData.background
        : ''
    ">
    <view>
      <template v-for="(item, swiperIndex) in newData.swiperList" :key="swiperIndex">
        <view class="hot-zone" :style="{
						  width: item.width ? (item.width*2) + 'rpx' : '100%',
						  overflowX: 'scroll',
						  overflowY: 'hidden',
						  lineHeight:'0',
							borderTopLeftRadius:`${newData.borderRadius == '1' ? (newData.radiusLeftTop*2)+'rpx':'0px'}`,
							borderTopRightRadius:`${newData.borderRadius == '1' ? (newData.radiusRightTop*2)+'rpx' : '0px'}`,
							borderBottomLeftRadius:`${newData.borderRadius == '1' ? (newData.radiusLeftBottom*2)+'rpx':'0px'}`,
							borderBottomRightRadius:`${newData.borderRadius == '1' ?(newData.radiusRightBottom*2)+'rpx':'0px'}`,
						}">
          <image :src="$formatImg750(item.imageUrl)" :show-menu-by-longpress="newData.canLongPress==1" mode="widthFix"
            :style="{
							height:'auto',
							width: '100%',
							display: 'block',
							borderRadius: `${newData.borderRadius == '1' ? '12rpx' : '0px'}`,
						  }"></image>

          <view class="zone" v-for="(zone, index) in item.hotZones" :key="index" :style="{
							width: zone.pageUrl=='customcomponentseckilltimer'?'100%':getZoneStyle(zone.widthPer),
							height: getZoneStyle(zone.heightPer),
							top: getZoneStyle(zone.topPer),
							left: getZoneStyle(zone.leftPer),
						  }">
            <view class="hot-area" @tap="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
              <text class="hot-txt" hover-class="none">
                {{ zone.pageName }}{{zone.pageUrl.startsWith('/pages/seckill/seckill-list/index')}}
              </text>
            </view>
          </view>
        </view>
      </template>
    </view>
  </view>
</template>

<script setup lang="uts">
  import { gotoPage } from "../div-base/div-page-urls.uts"
  import seckillSpu from "./relate-component/seckill-spu.uvue"
  import bargainSpu from "./relate-component/bargain-spu.uvue"
  import { useGlobalDataStore } from '@/stores/globalData'
  import { ref, onMounted, watch } from 'vue'

  // 定义类型接口
  interface HotZone {
    pageUrl : string
    pageName : string
    widthPer : number
    heightPer : number
    topPer : number
    leftPer : number
  }

  interface SwiperItem {
    imageUrl : string
    width ?: number
    hotZones : HotZone[]
  }

  interface ComponentData {
    pageUrl ?: string
    imageUrl ?: string
    height : number
    borderRadius ?: string
    radiusLeftTop ?: number
    radiusRightTop ?: number
    radiusLeftBottom ?: number
    radiusRightBottom ?: number
    canLongPress ?: number
    background ?: string
    bgImage ?: string
    marginBottomSpacing : number
    marginTopSpacing : number
    marginLeftSpacing : number
    marginRightSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
    swiperList : SwiperItem[]
  }

  // Props 定义
  interface Props {
    modelValue : ComponentData | null
    isStatusBar ?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      pageUrl: '',
      imageUrl: '',
      height: 100,
      marginBottomSpacing: 0,
      marginTopSpacing: 0,
      marginLeftSpacing: 0,
      marginRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0,
      swiperList: []
    }),
    isStatusBar: false
  })

  // 获取全局数据store
  const globalDataStore = useGlobalDataStore()

  // 响应式数据
  const newData = ref<ComponentData>(props.modelValue || {
    pageUrl: '',
    imageUrl: '',
    height: 100,
    marginBottomSpacing: 0,
    marginTopSpacing: 0,
    marginLeftSpacing: 0,
    marginRightSpacing: 0,
    paddingBottomSpacing: 0,
    paddingTopSpacing: 0,
    paddingLeftSpacing: 0,
    paddingRightSpacing: 0,
    swiperList: []
  })

  const theme = ref(globalDataStore.theme)
  const hour = ref<number>(10)
  const pagesLength = ref<number>(1)

  // 方法定义

  const getZoneStyle = (val : number) : string => {
    return `${(val || 0) * 100}%`
  }

  const handleZoneClick = (e : any, page : string) => {
    console.log('handleZoneClick-->', e, page)
    if (page) {
      gotoPage(page, pagesLength.value)
    }
  }

  // 初始化
  const initComponent = () => {
    // 监听当前页面栈的个数内容
    const pages = getCurrentPages()
    pagesLength.value = pages.length // 当前页面栈的个数
  }

  // 生命周期
  onMounted(() => {
    initComponent()
  })

  // 监听 props 变化
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      newData.value = newValue
    }
  }, { deep: true, immediate: true })
</script>

<style scoped lang="scss">
  .hot-zone {
    position: relative;
  }

  .zone {
    position: absolute;

    .hot-area {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      position: absolute;

      .hot-txt {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        height: 40px;
        color: rgba($color: #000000, $alpha: 0);
      }
    }
  }

  .sec-container {
    display: flex;
    align-items: center;
    height: 25px;
  }

  .seckill_num {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: inline-flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    background-image: -webkit-gradient(linear,
        right top,
        left top,
        from(#f10101),
        to(#fe4d17));
    background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
    background-image: linear-gradient(-90deg, #f10101, #fe4d17);
    font-size: 24rpx;
    color: #fff;
    letter-spacing: 0;
    border-radius: 2rpx 2rpx 2rpx 0;
    padding: 2px;
    z-index: 4;
    width: 80rpx;
  }

  .timer {
    background: #ecb4d9;
    color: #f10101;
    font-size: 24rpx;
    padding: 3px 6px 3px 10px;
    margin-left: -6px;
    flex-direction: row;
  }
</style>