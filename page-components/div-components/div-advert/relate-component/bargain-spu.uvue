<template>
	<view class="two-goods" v-if="bargainGoods && bargainGoods.length > 0">
		<view v-for="(item, index) in bargainGoods" class="item" :key="index">
			<view @click="imgclick(item)">
				<image :src="$formatImg360(item.picUrl)" mode="aspectFit" class="product-image"> </image>
				<view class="product-name">{{item.name}}</view>
				<view class="price-container">
					<view class="original-price">
						￥{{item.cutGoodsValue || item.goodsSku.salesPrice}}</view>
					<view class="bargain-price">
						<span class="price-symbol">￥</span>
						<span class="price-value">{{item.bargainPrice}}</span>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="uts">
import { ref, onMounted } from 'vue'
import { gotoPage } from "../../div-base/div-page-urls.js"
import api from 'utils/api'

// 类型定义
interface BargainGood {
	id: string | number
	name: string
	picUrl: string
	cutGoodsValue?: number
	bargainPrice: number
	goodsSku: {
		salesPrice: number
	}
}

interface BargainInfo {
	bargainInfos?: BargainGood[]
	[key: string]: any
}

// 响应式数据
const bargainGoods = ref<BargainGood[]>([])
const bargainInfo = ref<BargainInfo>({})

// 方法

const imgclick = (item: BargainGood): void => {
	if (item) {
		gotoPage(`/pages/bargain/bargain-list/index`)
	}
}

const getGoods = async (): Promise<void> => {
	try {
		const res = await api.bargainhallNowSpuList()
		if (res.data) {
			if (res.data.bargainInfos && res.data.bargainInfos.length > 0) {
				bargainGoods.value = res.data.bargainInfos.slice(0, 2)
			}
			bargainInfo.value = res.data
		}
	} catch (error) {
		console.error('获取砍价商品失败:', error)
	}
}

// 生命周期
onMounted(() => {
	getGoods()
})
</script>

<style scoped lang="scss">
	.two-goods {
		display: flex;
		justify-content: space-between;
		align-items: center;
		width: 100%;
		// height: 100%;
		position: absolute;
	    top: 0;
		left: 0;
		// background-color: #fff;
		.item:nth-child(2n) {
			padding-left: 6rpx;
			flex: 1;
		}

		.item:nth-child(2n+1) {
			padding-right: 6rpx;
			flex: 1;
		}
	}

	.product-image {
		width: 100%;
		height: 124rpx;
		display: block;
	}

	.product-name {
		color: #000;
		font-size: 22rpx;
		position: relative;
		width: 100%;
		line-height: 24rpx;
		height: 24rpx;
		overflow: hidden;
		margin-left: 6rpx;
		margin-top: 6rpx;
		margin-right: 6rpx;
	}

	.price-container {
		width: 100%;
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
	}

	.original-price {
		font-weight: bold;
		color: #ccc;
		text-decoration: line-through;
		min-height: 20rpx;
		font-size: 16rpx;
		margin-left: 6rpx;
		margin-right: 6rpx;
		margin-top: 4rpx;
	}

	.bargain-price {
		color: #ff0000;
		min-height: 50rpx;
		font-weight: bold;
		
		.price-symbol {
			font-size: 16rpx;
		}
		
		.price-value {
			font-size: 30rpx;
		}
	}
</style>