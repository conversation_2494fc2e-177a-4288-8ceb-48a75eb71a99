<template>
  <view :style="{
		background: popProps.contentType=='pop'?popProps.background:'transparent',
		borderRadius:popProps.contentType=='pop'? popProps.backgroundRadius+'px' :'0'
	}">
    <view class="two-goods" v-if="popGoods && popGoods.length > 0">
      <view v-for="(item, index) in popGoods.slice(0,6)" class="item" :key="index">
        <view v-if="item.goodsSpu" @click="imgclick(item)" class="item-content">
          <image :src="$formatImg360(item.goodsSpu.picUrls[0])" mode="aspectFit" class="pop-item-image"> </image>
          <view class="name">{{item.goodsSpu.name}}</view>
          <view class="pop-original-price">
            ￥{{item.goodsSpu.estimatedPriceVo.originalPrice}}</view>
          <view class="price-pop">
            <view class="savings-container">
              <view class="savings-badge">
                省</view>
              <view class="savings-amount">
                {{Math.round(item.goodsSpu.estimatedPriceVo.originalPrice-item.goodsSpu.estimatedPriceVo.estimatedPrice)}}
              </view>
            </view>
            <view class="buy-price">
              <text class="final-price-symbol">￥</text>
              <text class="final-price-value">{{item.goodsSpu.estimatedPriceVo.estimatedPrice}}</text>
            </view>
          </view>
        </view>
        <image :src="getPopImg(index)" mode="aspectFit" class="ranking-badge"> </image>
        <view v-if="index>2" class="ranking-number">{{index+1}}</view>
      </view>
      <view class="more" @tap="more">查看更多></view>
    </view>
    <view v-else class="no-ranking-message"> 请先配置排行榜 </view>

  </view>
</template>

<script setup lang="uts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { gotoPage } from "../../div-base/div-page-urls.js"
  import api from 'utils/api'

  // Props类型定义
  interface PopProps {
    contentType : string
    background : string
    backgroundRadius : number
    rankingGoodsId : Array<{ id : string | number }>
    [key : string] : any
  }

  interface PopGood {
    spuId : string | number
    goodsSpu : {
      name : string
      picUrls : string[]
      estimatedPriceVo : {
        originalPrice : number
        estimatedPrice : number
      }
    }
  }

  // Props定义
  const popProps = defineProps<{
    popProps : PopProps
  }>()

  // 响应式数据
  const popGoods = ref<PopGood[]>([])
  const pagesLength = ref<number>(1)

  // 计算属性
  const getPopImg = computed(() => {
    return (index : number) : string => {
      if (index < 3) {
        return `http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/pop${index + 1}.png`
      } else {
        return `http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/popmore.png`
      }
    }
  })

  // 方法
  const more = () : void => {
    gotoPage(
      `/pages/ranking/goods-ranking/index?id=${popProps.popProps.rankingGoodsId[0].id}`,
      pagesLength.value
    )
  }

  const imgclick = (item : PopGood) : void => {
    console.log("popGoods==item=", item)
    if (item) {
      gotoPage(
        `/pages/goods/goods-detail/index?id=${item.spuId}`
      )
    }
  }


  const skillCategoryId = (pageUrl : string) : string => {
    return pageUrl ? pageUrl.substring(pageUrl.indexOf('=') + 1, pageUrl.length) : ''
  }

  const getGoods = async () : Promise<void> => {
    console.log("popProps", popProps.popProps)

    try {
      const res = await api.getRanking(
        popProps.popProps.rankingGoodsId[0].id
      )
      console.log("nowSeckillSpus===", res.data)
      popGoods.value = res.data.rankSpuList
      console.log("popGoods===", popGoods.value)
    } catch (error) {
      console.error('获取排行榜商品失败:', error)
    }
  }

  // 监听props变化
  watch(() => popProps.popProps, (newVal) => {
    if (newVal) {
      getGoods()
    }
  }, { immediate: true })

  // 生命周期
  onMounted(() => {
    // 监听当前页面栈的个数内容
    const pages = getCurrentPages()
    pagesLength.value = pages.length // 当前页面栈的个数
  })
</script>

<style scoped lang="scss">
  .two-goods {
    width: 100%;
    padding: 10rpx;

    .item {
      display: inline-block;
      width: 31%;
      position: relative;
      height: 330rpx;
      background: #FFFDFE;
      border-radius: 16rpx;
      margin: 8rpx;

      .item-content {
        display: flex;
        width: 100%;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 4rpx;
      }

      .name {
        color: #000;
        font-size: 22rpx;
        position: relative;
        width: 100%;
        line-height: 24rpx;
        height: 22rpx;
        overflow: hidden;
        margin-bottom: 6rpx;
        margin-top: 10rpx;
      }

      .price-original {
        padding-left: 6rpx;
        font-weight: bold;
        color: #939393;
        text-decoration: line-through;
        min-height: 20rpx;
        font-size: 16rpx;
        margin-left: 6rpx;
        margin-right: 6rpx;
        margin-top: 4rpx;
      }

      .price-pop {
        width: 100%;
        display: flex;
        justify-content: space-between;
        background-size: 100% 100%;
        height: 45rpx;
        background-image: url("http://slshop-file.oss-cn-beijing.aliyuncs.com/live/goods/pop_price_bg.png")
      }

      .buy-price {
        color: #ff0000;
        width: 110rpx;
        font-weight: bold;
        background: #FF3955;
        padding: 8rpx;
        border-radius: 16rpx;
        color: #fff;
        height: 34rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10rpx;
      }
    }
  }

  .more {
    font-size: 27rpx;
    font-weight: 500;
    color: #695A57;
    line-height: 60rpx;
    text-align: center;
  }

  .pop-item-image {
    width: 100%;
    height: 160rpx;
    display: block;
    text-align: center;
    margin-top: 44rpx;
  }

  .pop-original-price {
    width: 100%;
    color: #ccc;
    text-decoration: line-through;
    font-size: 16rpx;
  }

  .savings-container {
    display: flex;
    align-items: center;

    .savings-badge {
      background-color: #FF3955;
      color: #fff;
      border-radius: 50%;
      width: 32rpx;
      height: 32rpx;
      font-size: 22rpx;
      text-align: center;
    }

    .savings-amount {
      color: #FF3955;
      font-size: 24rpx;
      margin-left: 2rpx;
      width: 55rpx;
      text-align: center;
    }
  }

  .final-price-symbol {
    font-size: 16rpx;
  }

  .final-price-value {
    font-size: 28rpx;
  }

  .ranking-badge {
    width: 42rpx;
    height: 72rpx;
    position: absolute;
    left: 6rpx;
    top: 0;
  }

  .ranking-number {
    position: absolute;
    left: 17rpx;
    top: 25rpx;
    color: #717171;
    font-size: 28rpx;
    font-weight: bold;
  }

  .no-ranking-message {
    height: 100rpx;
    line-height: 100rpx;
    text-align: center;
  }
</style>