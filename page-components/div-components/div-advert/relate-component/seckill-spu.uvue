<template>
  <view class="two-goods" v-if="skillGoods && skillGoods.length > 0">
    <view v-for="(item, index) in skillGoods" class="item" :key="index">
      <block v-if="item.length > 1">
        <view class="seckill-item-container">
          <block v-for="(n, m) in item" :key="n.id">
            <view @click="imgclick(n)" class="item-content seckill-animated"
              :class="m % 2 ? 'css-animate' : 'css-animate1'" :style="{ '--s': (index % 2) ? '3s' : '0s' }">
              <image :src="$formatImg360(n.picUrl)" mode="aspectFill" class="seckill-product-image"></image>
              <view class="seckill-product-name">{{n.name}}</view>
              <view class="seckill-price-container">
                <view class="seckill-original-price flex-row">
                  ￥{{n.originalPrice || n.salesPrice}}
                </view>
                <view class="seckill-final-price flex-row">
                  <price-handle :value="n.seckillPrice" signFont="12rpx" bigFont="22rpx"
                    smallFont="16rpx"></price-handle>
                </view>
              </view>
            </view>
          </block>
        </view>
      </block>
      <block v-else-if="item.length === 1">
        <view class="seckill-item-container">
          <view @click="imgclick(item[0])" class="item-content seckill-single-item">
            <image :src="$formatImg360(item[0].picUrl)" mode="aspectFill" class="seckill-product-image"></image>
            <view class="seckill-product-name">{{item[0].name}}</view>
            <view class="seckill-price-container">
              <view class="seckill-original-price flex-row">
                ￥{{item[0].originalPrice || item[0].salesPrice}}
              </view>
              <view class="seckill-final-price">
                <price-handle class="seckill-price-handle flex-row" :value="item[0].seckillPrice" signFont="12rpx"
                  bigFont="22rpx" smallFont="16rpx"></price-handle>
              </view>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script setup lang="uts">
  import PriceHandle from "@/components/price-handle/index.uvue"
  import { gotoPage } from "../../div-base/div-page-urls.js"
  import api from 'utils/api'

  // 类型定义
  interface SeckillItem {
    id : string | number
    name : string
    picUrl : string
    originalPrice ?: number
    salesPrice ?: number
    seckillPrice : number
    seckillHallId : string | number
  }

  // Props定义
  const categoryId = defineProps<{
    categoryId : string
  }>()

  // 响应式数据
  const skillGoods = ref<SeckillItem[][]>([])

  // 方法

  const imgclick = (item : SeckillItem) : void => {
    if (item) {
      gotoPage(`/pages/seckill/seckill-list/index?id=${item.id}&seckillHallId=${item.seckillHallId}&categoryId=${skillCategoryId(categoryId.categoryId)}`)
    }
  }

  const skillCategoryId = (pageUrl : string) : string => {
    return pageUrl ? pageUrl.substring(pageUrl.indexOf('=') + 1, pageUrl.length) : ''
  }

  const getGoods = async () : Promise<void> => {
    try {
      const res = await api.nowSeckillSpus({
        categoryId: skillCategoryId(categoryId.categoryId),
        num: 4
      })

      const { data } = res
      let skillGoodsData : SeckillItem[][] = []

      while (data.length >= 2) {
        skillGoodsData.push([data.shift(), data.shift()])
      }

      if (data.length === 1) {
        skillGoodsData.push(data)
      }

      if (skillGoodsData.length > 2) {
        skillGoodsData = skillGoodsData.slice(0, 2)
      }

      skillGoods.value = skillGoodsData
    } catch (error) {
      console.error('获取秒杀商品失败:', error)
    }
  }

  // 监听categoryId变化
  watch(() => categoryId.categoryId, (newVal) => {
    if (newVal) {
      getGoods()
    }
  }, { immediate: true })

  // 生命周期
  onMounted(() => {
    getGoods()
  })
</script>

<style scoped lang="scss">
  .item-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex: 1;
    max-width: 166rpx;
    background-color: #fff;
    width: 100%;
    height: 100%;
  }

  .two-goods {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;
    flex-direction: row;

    // border: 1px solid red;
    // overflow: scroll;
    .item:nth-child(2n) {
      padding-left: 6rpx;
      flex: 1;
      height: 100%;
    }

    .item:nth-child(2n+1) {
      padding-right: 6rpx;
      flex: 1;
      height: 100%;
    }
  }

  .css-animate {
    animation: keys1 8s infinite var(--s);
    position: absolute;
    left: 0;
  }

  .css-animate1 {
    animation: keys2 8s infinite var(--s);
    position: absolute;
    left: 0;
  }

  .css-animate.noHover {
    opacity: 0;
    transform: scale(.8);
  }

  @keyframes keys1 {
    0% {
      opacity: 1;
      transform: scale(1);
    }

    45% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0;
      transform: scale(.8);
    }

    95% {
      opacity: 0;
      transform: scale(.8);
    }

    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes keys2 {
    0% {
      opacity: 0;
      transform: scale(.8);
    }

    45% {
      opacity: 0;
      transform: scale(.8);
    }

    50% {
      opacity: 1;
      transform: scale(1);
    }

    95% {
      opacity: 1;
      transform: scale(1);
    }

    100% {
      opacity: 0;
      transform: scale(.8);
    }
  }

  .seckill-item-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .seckill-animated {
    // 动画相关样式已在 css-animate 和 css-animate1 中定义
  }

  .seckill-single-item {
    position: absolute;
    top: 0;
    left: 0;
  }

  .seckill-product-image {
    width: 100%;
    max-width: 166rpx;
    height: 124rpx;
    display: block;
    text-align: center;
  }

  .seckill-product-name {
    color: #000;
    font-size: 20rpx;
    position: relative;
    width: 100%;
    line-height: 24rpx;
    height: 22rpx;
    overflow: hidden;
    margin-left: 6rpx;
    margin-top: 6rpx;
    margin-right: 6rpx;
  }

  .seckill-price-container {
    width: 100%;
    display: flex;
    max-width: 166rpx;
    align-items: center;
    flex: 1;
    justify-content: space-around;
    flex-direction: row;
  }

  .seckill-original-price {
    color: #555;
    font-size: 18rpx;
    margin-top: 5rpx;
  }

  .seckill-final-price {
    height: 100%;
    color: #ff0000;
  }

  .seckill-price-handle {
    line-height: 100%;
  }
</style>