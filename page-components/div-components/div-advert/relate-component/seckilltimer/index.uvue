<template>
  <view class="sec-container">
	  秒杀倒计时组件
<!--    <text class="seckill_num">{{hour}}点场</text>
    <text class="timer">{{formatSeconds}}</text> -->
  </view>
</template>

<script setup lang="uts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 响应式数据
const lastSecs = ref<number>(40000)
const hour = ref<number>(10)
const timer = ref<number | null>(null)

// 计算属性
const formatSeconds = computed((): string => {
	const result = parseInt(lastSecs.value.toString())
	const h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600).toString()
	const m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60)).toString()
	const s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60)).toString()

	let res = ''
	if (h !== '00') res += `${h}:`
	if (m !== '00') res += `${m}:`
	res += `${s}`
	return res
})

// 方法
const getLastTime = (): void => {
	timer.value = setInterval(() => {
		lastSecs.value--
		if (lastSecs.value === 0) {
			if (timer.value !== null) {
				clearInterval(timer.value)
			}
		}
	}, 1000)
}

// 生命周期
onMounted(() => {
	getLastTime()
})

onUnmounted(() => {
	if (timer.value !== null) {
		clearInterval(timer.value)
	}
})
</script>

<style scoped lang="scss">
.sec-container {
  display: flex;
  align-items: center;
  height: 25px;
}

.seckill_num {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: inline-flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  background-image: -webkit-gradient(
    linear,
    right top,
    left top,
    from(#f10101),
    to(#fe4d17)
  );
  background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
  background-image: linear-gradient(-90deg, #f10101, #fe4d17);
  box-shadow: 0 0.1rem 0.2rem 0 rgb(241 1 1 / 20%);
  font-size: 0.5rem;
  color: #fff;
  letter-spacing: 0;
  border-radius: 0.35rem 0.35rem 0.35rem 0;
  padding: 2px;
  z-index: 4;
}

.timer {
    background: #ecb4d9;
    color: #f10101;
    font-size: 0.5rem;
    padding: 3px 6px 3px 10px;
    margin-left: -6px;
}
</style>