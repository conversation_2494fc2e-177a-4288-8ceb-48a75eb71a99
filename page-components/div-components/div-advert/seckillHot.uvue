<template>
  <!-- 图文广告显示组件 -->
  <view :style="containerStyles" :class="
      newData.background && newData.background.indexOf('bg-') != -1
        ? newData.background
        : ''
    ">
    <view>
      <template v-for="(item, swiperIndex) in newData.swiperList" :key="swiperIndex">
        <!-- <scroll-view scroll-x="true" scroll-left="0" :key="index"> -->
        <view class="hot-zone" :style="getHotZoneStyles(item)">
          <image :src="$formatImg750(item.imageUrl)" :show-menu-by-longpress="newData.canLongPress==1" mode="widthFix"
            :style="imageStyles"></image>

          <view class="zone" v-for="(zone, index) in item.hotZones" :key="index" :style="getZonePositionStyles(zone)">
            <view v-if="zone.pageUrl.startsWith('/pages/seckill/seckill-list/index')" style="height: 100%;">
              <seckill-spu v-if='showCompont' :categoryId="zone.pageUrl" />
            </view>
            <view class="hot-area" v-else-if="zone.pageUrl.startsWith('/pages/bargain/bargain-list/index')">
              <bargain-spu />
            </view>
            <!-- <view v-if="zone.pageUrl&&zone.pageUrl.startsWith('customcomponent')">
								<view v-if="zone.pageUrl=='customcomponentseckilltimer' && hasSeckill"
									class="sec-container">
									<text class="seckill_num">{{curSeckillHall.hallTime}}点场</text>
									<text class="timer">{{hour}}: {{minute}}: {{second}}</text>
								</view>
							</view> -->
            <view v-else class="hot-area" @tap="(e) => handleZoneClick(e, zone.pageUrl)" hover-class="none">
              <text class="hot-txt" hover-class="none">
                {{ zone.pageName }}
              </text>
            </view>
          </view>
        </view>
        <!-- </scroll-view> -->
      </template>
    </view>
  </view>
</template>

<script setup>
  import { nextTick } from 'vue'
  import { useGlobalDataStore } from '/stores/globalData.uts'
  import {
    gotoPage
  } from "../div-base/div-page-urls.js"
  import seckillSpu from "./relate-component/seckill-spu.uvue"
  import bargainSpu from "./relate-component/bargain-spu.uvue"
  // import {
  //   EventBus
  // } from '@/utils/eventBus.js'

  const props = defineProps({
    value: {
      type: Object,
      default: () => ({
        pageUrl: ``,
        imageUrl: "",
        height: 100,
      })
    },
    isStatusBar: {
      type: Boolean,
      default: false
    }
  })

  const globalDataStore = useGlobalDataStore()
  const theme = computed(() => globalDataStore.theme)
  const newData = ref(props.value)
  const lastSecs = ref(40000)
  const hour = ref(10)
  const showCompont = ref(true)
  const pagesLength = ref(1)
  let timer = null


  const formatSeconds = computed(() => {
    let result = parseInt(lastSecs.value)
    let h = Math.floor(result / 3600) < 10 ? '0' + Math.floor(result / 3600) : Math.floor(result / 3600)
    let m = Math.floor((result / 60 % 60)) < 10 ? '0' + Math.floor((result / 60 % 60)) : Math.floor((result / 60 % 60))
    let s = Math.floor((result % 60)) < 10 ? '0' + Math.floor((result % 60)) : Math.floor((result % 60))

    let res = ''
    if (h !== '00') res += `${h}:`
    if (m !== '00') res += `${m}:`
    res += `${s}`
    return res
  })

  const containerStyles = computed(() => {
    return {
      backgroundSize: '100% auto',
      backgroundRepeat: 'no-repeat',
      backgroundImage: props.isStatusBar ? '' : `url(${newData.value.bgImage})`,
      backgroundColor: newData.value.background,
      marginBottom: `${newData.value.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.value.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.value.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.value.marginRightSpacing * 2}rpx`,
      paddingBottom: `${newData.value.paddingBottomSpacing * 2}rpx`,
      paddingTop: `${newData.value.paddingTopSpacing * 2}rpx`,
      paddingLeft: `${newData.value.paddingLeftSpacing * 2}rpx`,
      paddingRight: `${newData.value.paddingRightSpacing * 2}rpx`
    }
  })

  const imageStyles = computed(() => {
    return {
      height: 'auto',
      width: '100%',
      display: 'block',
      borderTopLeftRadius: `${newData.value.borderRadius == 1 ? (newData.value.radiusLeftTop * 2) + 'rpx' : '0rpx'}`,
      borderTopRightRadius: `${newData.value.borderRadius == 1 ? (newData.value.radiusRightTop * 2) + 'rpx' : '0rpx'}`,
      borderBottomLeftRadius: `${newData.value.borderRadius == 1 ? (newData.value.radiusLeftBottom * 2) + 'rpx' : '0rpx'}`,
      borderBottomRightRadius: `${newData.value.borderRadius == 1 ? (newData.value.radiusRightBottom * 2) + 'rpx' : '0rpx'}`
    }
  })

  const showcompons = () => {
    showCompont.value = false
    nextTick(() => {
      showCompont.value = true
    })
  }

  const getZoneStyle = (val) => {
    return `${(val || 0) * 100}%`
  }

  const getHotZoneStyles = (item) => {
    return {
      width: item.width ? (item.width * 2) + 'rpx' : '100%',
      overflowX: 'scroll',
      overflowY: 'hidden',
      lineHeight: '0'
    }
  }

  const getZonePositionStyles = (zone) => {
    return {
      width: zone.pageUrl == 'customcomponentseckilltimer' ? '100%' : getZoneStyle(zone.widthPer),
      height: getZoneStyle(zone.heightPer),
      top: getZoneStyle(zone.topPer),
      left: getZoneStyle(zone.leftPer)
    }
  }

  const handleZoneClick = (e, page) => {
    if (page) {
      gotoPage(page, pagesLength.value)
    }
  }

  const getLastTime = () => {
    timer = setInterval(() => {
      lastSecs.value--
      if (lastSecs.value === 0) {
        clearInterval(timer)
      }
    }, 1000)
  }

  onMounted(() => {
    getLastTime()
    const pages = getCurrentPages()
    pagesLength.value = pages.length
    // EventBus.$on("showcompons", () => {
    //   showcompons()
    // })
  })

  onUnmounted(() => {
    if (timer) {
      clearInterval(timer)
    }
    // EventBus.$off("showcompons")
  })
</script>

<style scoped lang="scss">
  .hot-zone {
    position: relative;
  }

  .zone {
    position: absolute;
    // cursor: pointer;
    // border: 1px solid #155bd4;
    // background: rgba(21, 91, 212, 0.5);
    // font-size: 12px;
    // color: #fff;
    // user-select: auto;
    // touch-action: none;

    .hot-area {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      position: absolute;
      flex-direction: row;

      .hot-txt {
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 2;
        height: 40px;
        color: rgba($color: #000000, $alpha: 0);
      }
    }
  }

  .sec-container {
    display: flex;
    align-items: center;
    height: 25px;
    flex-direction: row;
  }

  .seckill_num {
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: inline-flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    background-image: -webkit-gradient(linear,
        right top,
        left top,
        from(#f10101),
        to(#fe4d17));
    background-image: -webkit-linear-gradient(right, #f10101, #fe4d17);
    background-image: linear-gradient(-90deg, #f10101, #fe4d17);
    // box-shadow: 0 4rpx 8rpx 0 rgb(241 1 1 / 20%);
    font-size: 24rpx;
    color: #fff;
    letter-spacing: 0;
    border-radius: 2rpx 2rpx 2rpx 0;
    padding: 2px;
    z-index: 4;
    width: 80rpx;
  }

  .timer {
    background: #ecb4d9;
    color: #f10101;
    font-size: 24rpx;
    padding: 3px 6px 3px 10px;
    margin-left: -6px;
  }
</style>