<template>
  <view :style="{
    marginBottom: `${modelValue.marginBottomSpacing * 2}rpx`,
    marginTop: `${modelValue.marginTopSpacing * 2}rpx`,
    marginLeft: `${modelValue.marginLeftSpacing * 2}rpx`,
    marginRight: `${modelValue.marginRightSpacing * 2}rpx`
  }" :class="modelValue.background && modelValue.background.length > 0 ? modelValue.background : ''">
    <!--    <view :style="{
      backgroundColor: modelValue.background,
      backgroundSize: 'cover',
      paddingBottom: `${modelValue.paddingBottomSpacing}px`,
      paddingTop: `${modelValue.paddingTopSpacing}px`,
      paddingLeft: `${modelValue.paddingLeftSpacing}px`,
      paddingRight: `${modelValue.paddingRightSpacing}px`,
    }">
      <scroll-view scroll-x class="nav nav-margin">
        <view class="menu-bar">
          <view class="menu-item" :class="index == tabCur ? 'select' : ''" v-for="(item, index) in articleCategoryList"
            :key="index" @tap="tabSelect(item.id, index)">
            {{ item.name }}
          </view>
        </view>
      </scroll-view>

      <template v-if="articleList && articleList.length > 0">
        <view class="goods-container flex">
          <view class="article-item-container" v-for="(item, index) in articleList" :key="index">
            <view class="article-item-wrapper">
              <view :style="{ padding: `${modelValue.liveSpace * 2}rpx` }">
                <view class="goods-box">
                  <navigator hover-class="none" class="article-navigator"
                    :url="`/pages/article/article-info/index?id=${item.id}`">
                    <view class="img-box">
                      <lazy-load :borderRadius="12" :image="$formatImg360(item.picUrl)"
                        :markUrls="item.goodsMarkInfoVo ? item.goodsMarkInfoVo.angleMarkUrl : ''" />
                    </view>

                    <view class="cu-list" class="article-content">
                      <view class="article-title">
                        {{ item.articleTitle }}
                      </view>
                      <view class="text-gray text-sm flex justify-between margin-top-xs">
                        <view class="author-info">
                          <image class="author-avatar" :src="item.picUrl" />
                          <view class="author-name">
                            {{ item.authorName }}
                          </view>
                        </view>
                      </view>
                    </view>
                  </navigator>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
      <div-no-data v-else />
      <view v-if="loadmore" :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
    </view> -->
  </view>
</template>

<script setup lang="uts">
  import { ref, onMounted, onUnmounted } from 'vue'
  // import { EventBus } from '@/utils/eventBus.uts'
  import api from '@/utils/api.uts'
  import { filterForm } from '@/utils/util.uts'
  import divBaseNavigator from '../div-base/div-base-navigator.uvue'
  import divNoData from '@/components/base-nodata/index.uvue'
  import lazyLoad from '@/components/lazy-load/index.uvue'

  type ArticleItem = {
    id : string
    picUrl : string
    articleTitle : string
    authorName : string
    goodsMarkInfoVo ?: {
      angleMarkUrl : string
    }
    [key : string] : any
  }

  type CategoryItem = {
    id : string
    name : string
    [key : string] : any
  }

  type ArticleData = {
    marginBottomSpacing ?: number
    marginTopSpacing ?: number
    marginLeftSpacing ?: number
    marginRightSpacing ?: number
    background ?: string
    paddingBottomSpacing ?: number
    paddingTopSpacing ?: number
    paddingLeftSpacing ?: number
    paddingRightSpacing ?: number
    liveSpace ?: number
    [key : string] : any
  }

  type PageInfo = {
    searchCount : boolean
    current : number
    size : number
    ascs : string
    descs : string
  }

  defineProps<{
    modelValue : ArticleData
  }>()

  const page = ref<PageInfo>({
    searchCount: false,
    current: 1,
    size: 10,
    ascs: '',
    descs: 'create_time'
  })

  const parameter = ref<Record<string, any>>({})
  const loadmore = ref(true)
  const articleList = ref<ArticleItem[]>([])
  const articleCategoryList = ref<CategoryItem[]>([])
  const tabCur = ref(0)


  const reachBottom = () => {
    if (loadmore.value) {
      page.value.current = page.value.current + 1
      articlePage()
    }
  }

  const articlePage = () => {
    api.articlePage({ ...page.value, ...filterForm(parameter.value) }).then((res : any) => {
      const newArticleList = res.data.records as ArticleItem[]
      articleList.value = [...articleList.value, ...newArticleList]
      if (newArticleList.length < page.value.size || page.value.size === 0) {
        loadmore.value = false
      }
    })
  }

  const articleCategoryPage = () => {
    api.articleCategoryPage({
      searchCount: false,
      current: 1,
      size: 10,
      ascs: '',
      descs: ''
    }).then((res : any) => {
      const newCategoryList = res.data.records as CategoryItem[]
      articleCategoryList.value = newCategoryList
      if (newCategoryList && newCategoryList.length > 0) {
        tabSelect(newCategoryList[0].id, 0)
      } else {
        loadmore.value = false
      }
    })
  }

  const tabSelect = (id : string, index : number) => {
    tabCur.value = index
    articleList.value = []
    loadmore.value = true
    page.value.current = 1
    parameter.value = {
      categoryId: id,
      isHot: null
    }
    articlePage()
  }

  const jumpPage = (id : string) => {
    uni.navigateTo({
      url: `/pages/article/article-info/index?id=${id}`
    })
  }

  onMounted(() => {
    articleCategoryPage()
    // EventBus.$on("divGoodsGroupsReachBottom", reachBottom)
  })

  onUnmounted(() => {
    // EventBus.$off("divGoodsGroupsReachBottom", reachBottom)
  })

  defineExpose({
    reachBottom
  })
</script>

<style scoped lang="scss">
  .goods-container {
    justify-content: space-between;
    flex-wrap: wrap;
    box-sizing: content-box;
  }

  .menu-bar {
    display: flex;
    background: #f0f0f0;

    .menu-item {
      background-color: #fff;
      font-size: 12px;
      color: #404040;
      height: 46rpx;
      line-height: 46rpx;
      border-radius: 46rpx;
      text-align: center;
      margin: 0 6rpx;
      width: 229rpx;
    }

    .select {
      background: linear-gradient(0deg, #CDAD90 0%, #CDA185 100%);
      color: #fff;
    }
  }

  .goods-box {
    width: 100%;
    background-color: #fff;
    overflow: hidden;
    border-radius: 12rpx;
  }

  .goods-box .img-box {
    width: 100%;
    height: 370rpx;
    overflow: hidden;
  }

  .goods-box .img-box image {
    width: 100%;
    height: 349rpx;
  }

  .nav-margin {
    margin: 12rpx 0 6rpx 0;
  }

  .article-item-container {
    width: 50%;
  }

  .article-item-wrapper {
    width: 100%;
  }

  .article-navigator {
    background-color: #ffffff;
  }

  .article-content {
    background-color: #FFFFFF;
    padding: 20rpx;
    border-radius: 0 0 20rpx 20rpx;
  }

  .article-title {
    height: 78rpx;
    line-height: 37rpx;
  }

  .author-info {
    display: flex;
  }

  .author-avatar {
    width: 30rpx;
    height: 30rpx;
    border-radius: 50%;
  }

  .author-name {
    margin-left: 10rpx;
  }
</style>