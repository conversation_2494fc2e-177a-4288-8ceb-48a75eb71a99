<template>
  <view @click="goPage" :style="styleProps">
    <slot></slot>
  </view>
</template>

<script setup lang="uts">
  import { pageUrls, gotoPage } from './div-page-urls.uts'
  type StyleProps = {
    background ?: string
    [key : string] : any
  }

  const props = defineProps<{
    pageUrl ?: string
    styleProps ?: StyleProps
  }>()

  const pagesLength = ref(1)

  const goPage = (e : Event) => {
    if (props.pageUrl) {
      gotoPage(props.pageUrl, pagesLength.value)
    }
  }

  onMounted(() => {
    const pages = getCurrentPages()
    pagesLength.value = pages.length
  })
</script>

<style scoped lang="scss"></style>