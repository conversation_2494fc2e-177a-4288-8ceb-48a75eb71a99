// import { EventBus } from "@/utils/eventBus.uts"
import api from '@/utils/api.uts'
import util from '@/utils/util.uts'
export const pageUrls = {
  tabPages: [
    "/pages/home/<USER>",
    "/pages/second-tab/index",
    "/pages/third-tab/index",
    "/pages/tab-personal/index"
  ]
}
export const gotoPage = (url : string, pageSize : number, isRedirect : boolean = false) : void => {
  let switchTab = false

  pageUrls.tabPages.forEach((item : string) => {
    if (item && url && url.indexOf(item) !== -1) {
      switchTab = true
    }
  })

  if (switchTab) {
    if (url.indexOf("?") !== -1) {
      const paramsString1 = url.split("?")[1]
      if (paramsString1 && paramsString1.indexOf('=') !== -1) {
        const paramParts = paramsString1.split("=")
        uni.setStorageSync(`tab-${paramParts[0]}`, paramParts[1])
      }
    }
    uni.switchTab({ url })
  } else if (url.startsWith('api:receiveCoupon')) {
    if (url.indexOf("?") !== -1 && url.indexOf('&') !== -1) {
      const paramsString = url.split("?")[1]
      const params = new URLSearchParams(paramsString)
      const couponId = params.get('couponId')
      const name = params.get('name')

      if (couponId) {
        receiveCoupon(couponId, name || '')
      }
    }
  } else if (url.startsWith('api:submitInfo')) {
    if (url.indexOf("?") !== -1) {
      const paramsString = url.split("?")[1]
      const params = new URLSearchParams(paramsString)
      const couponId = params.get('couponId')

      if (couponId) {
        submitCouponInfo(couponId)
      }
    }
  } else if (url === 'api:showLoginPage') {
    // EventBus.$emit('showLoginPage')
  } else {
    if (isRedirect) {
      uni.redirectTo({ url })
    } else if (pageSize >= 10) {
      uni.redirectTo({ url })
    } else {
      uni.navigateTo({ url })
    }
  }
}

const receiveCoupon = (couponId : string, name : string) : void => {
  uni.showLoading({ title: '领取中...' })

  api.receiveCoupon({ couponId }).then((res : any) => {
    uni.hideLoading()
    if (res.code === 0) {
      uni.showToast({
        title: '领取成功',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: res.msg || '领取失败',
        icon: 'none'
      })
    }
  }).catch(() => {
    uni.hideLoading()
    uni.showToast({
      title: '领取失败',
      icon: 'none'
    })
  })
}

const submitCouponInfo = (couponId : string) : void => {
  uni.showLoading({ title: '提交中...' })

  api.submitInfo({ couponId }).then((res : any) => {
    uni.hideLoading()
    if (res.code === 0) {
      uni.showToast({
        title: '提交成功',
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: res.msg || '提交失败',
        icon: 'none'
      })
    }
  }).catch(() => {
    uni.hideLoading()
    uni.showToast({
      title: '提交失败',
      icon: 'none'
    })
  })
}