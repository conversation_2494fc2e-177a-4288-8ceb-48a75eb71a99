<template>
  <!-- 商品分类组件 -->
  <view :style="containerStyle">
    <view :class="backgroundClass" :style="mainContentStyle">
      <view class="category-container" :style="categoryContainerStyle">
        <template v-if="hasNavButtons">
          <view v-for="(item, index) in navButtons" :key="`nav-${index}`" :data-index="index"
            :style="getItemStyle(item)" :role="ROLES.TAB" :aria-selected="index === TabCur"
            :aria-label="`分类 ${item.navName || '未命名'}`" @click="tabSelect">
            <view :style="getItemBackgroundStyle(item)">
              <!-- 轮播文本显示 -->
              <view v-if="isCarouselText(item.navName)" class="height100" :style="getCarouselContainerStyle(item)">
                <swiper vertical autoplay circular :interval="SWIPER_INTERVAL" class="height100"
                  :style="getSwiperStyle(item)">
                  <swiper-item v-for="(txt, txtIndex) in getCarouselTexts(item.navName)" :key="`swiper-${txtIndex}`"
                    @click.stop="handleSwiper(item, txt)">
                    <view :style="swiperTextStyle">
                      {{ txt }}
                    </view>
                  </swiper-item>
                </swiper>
              </view>
              <!-- 普通文本显示 -->
              <view v-else-if="item.navName" :class="getTextClass(index, item)" :style="getTextStyle(item)">
                {{ item.navName }}
              </view>
            </view>
          </view>
        </template>
      </view>

      <!-- 右侧固定按钮 -->
      <view v-if="showFixedRight">
        <div-base-navigator :pageUrl="newData.rightPageUrl" hover-class="none">
          <view class="padding-right padding-left-xs" :style="rightButtonStyle">
            <image v-if="newData.rightImageUrl" :src="newData.rightImageUrl" mode="aspectFit"
              :style="rightImageStyle" />
            {{ newData.rightLabel }}
          </view>
        </div-base-navigator>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  import api from '@/utils/api'
  import { pageUrls, gotoPage } from '../div-base/div-page-urls.uts'
  import divBaseNavigator from '../div-base/div-base-navigator.uvue'
  import { useSystemStore } from '@/stores/system'

  // 常量定义
  const SWIPER_INTERVAL = 3000
  const BORDER_WIDTH = '2rpx'
  const FONT_SIZE_SMALL = '24rpx'
  const CAROUSEL_SEPARATOR = ';'
  const STORAGE_KEY = 'param-goods-category-index'

  // 枚举定义
  enum NavigationType {
    EVENT = 0,
    NEW_PAGE = 1
  }

  const ROLES = {
    TAB: 'tab'
  } as const

  // 接口类型定义
  interface NavButton {
    imageUrl ?: string
    itemSpace : number
    itemWidth : number
    navName ?: string
    pageUrl : string
    isNewPage : NavigationType
    [key : string] : any
  }

  interface CategoryData {
    background : string
    bgImage ?: string
    navButtons ?: NavButton[]
    textColor : string
    fontSize : number
    borderBottom : string
    height : number
    marginBottomSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
    fixedRight : number
    rightPageUrl ?: string
    rightImageUrl ?: string
    rightLabel ?: string
    selectPoi ?: number
    [key : string] : any
  }

  interface Props {
    modelValue : CategoryData | null
    isStatusBar : boolean
    divType : string
    pageSelectPoi : UTSJSONObject
  }

  // Props 定义
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      background: '',
      textColor: '#000000',
      fontSize: 14,
      borderBottom: 'none',
      height: 80,
      marginBottomSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0,
      fixedRight: 0
    }),
    isStatusBar: false,
    divType: 'home',
    pageSelectPoi: () => ({})
  })

  // Emits 定义
  const emit = defineEmits<{
    getpage : [e: { index : number; microId : string | null }]
  }>()

  // Store
  const systemStore = useSystemStore()

  // 响应式数据
  const newData = shallowRef<CategoryData>({} as CategoryData)
  const TabCur = ref<number>(0)

  // 计算属性 - 系统信息
  const isPhone = computed(() : boolean => systemStore.isPhone)
  const HeightBar = computed(() : number => systemStore.HeightBar)
  const windowWidth = computed(() : number => systemStore.windowWidth)
  const smallWidth = computed(() : number => systemStore.smallWidth)
  const multipleView = computed(() : number => systemStore.multipleView)

  // 计算属性 - 数据相关
  const navButtons = computed(() : NavButton[] => newData.value?.navButtons || [])
  const hasNavButtons = computed(() : boolean => navButtons.value.length > 0)
  const showFixedRight = computed(() : boolean => newData.value?.fixedRight === 1)

  // 计算属性 - 样式相关
  const computedHeight = computed(() : string => {
    return props.isStatusBar
      ? `${HeightBar.value}px`
      : `${newData.value.height * multipleView.value}rpx`
  })

  const containerStyle = computed(() => ({
    minHeight: `${20 * multipleView.value}rpx`,
    backgroundSize: 'cover',
    backgroundImage: newData.value.bgImage ? `url(${newData.value.bgImage})` : 'none',
    backgroundColor: newData.value.background || 'transparent',
    marginBottom: props.isStatusBar
      ? '0'
      : `${newData.value.marginBottomSpacing * multipleView.value}rpx`
  }))

  const backgroundClass = computed(() : string => {
    const bg = newData.value.background
    return bg && bg.indexOf('bg-') !== -1 ? bg : ''
  })

  const mainContentStyle = computed(() => ({
    display: 'flex',
    justifyContent: 'space-between',
    paddingBottom: props.isStatusBar
      ? '0'
      : `${newData.value.paddingBottomSpacing * multipleView.value}rpx`,
    paddingTop: props.isStatusBar
      ? '0'
      : `${newData.value.paddingTopSpacing * multipleView.value}rpx`,
    paddingLeft: `${newData.value.paddingLeftSpacing * multipleView.value}rpx`,
    paddingRight: `${newData.value.paddingRightSpacing * multipleView.value}rpx`,
    flexDirection: 'row',
  }))

  const categoryContainerStyle = computed(() => ({
    flex: '1',
    height: computedHeight.value
  }))

  const swiperTextStyle = computed(() => ({
    lineHeight: computedHeight.value,
    fontSize: FONT_SIZE_SMALL,
    color: '#3f3e3e',
    marginLeft: '64rpx',
    textTransform: 'uppercase'
  }))

  const rightButtonStyle = computed(() => ({
    height: computedHeight.value,
    display: 'flex',
    alignItems: 'center',
    color: newData.value.textColor,
    fontSize: `${newData.value.fontSize * multipleView.value}rpx`,
    flexDirection: 'row',
  }))

  const rightImageStyle = computed(() => ({
    width: isPhone.value ? '50rpx' : '30rpx'
  }))

  // 工具函数
  const isCarouselText = (navName ?: string) : boolean => {
    return Boolean(navName && navName.indexOf(CAROUSEL_SEPARATOR) > -1)
  }

  const getCarouselTexts = (navName ?: string) : string[] => {
    if (!navName) return []
    return navName.split(CAROUSEL_SEPARATOR).filter(Boolean)
  }

  const getItemMargin = (item : NavButton) : number => {
    return windowWidth.value < smallWidth.value && props.isStatusBar
      ? 10
      : item.itemSpace * multipleView.value
  }

  const getItemStyle = (item : NavButton) => ({
    margin: `0 ${getItemMargin(item)}rpx`,
    padding: '0'
  })

  const getItemBackgroundStyle = (item : NavButton) => ({
    backgroundImage: item.imageUrl ? `url(${item.imageUrl})` : 'none',
    backgroundPosition: 'center',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
    height: '100%',
    width: `${Number(item.itemWidth) * multipleView.value}rpx`,
    margin: '0',
    padding: '0',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  })

  const getCarouselContainerStyle = (item : NavButton) => ({
    display: 'flex',
    alignItems: 'center',
    color: newData.value.textColor,
    borderBottom: newData.value.borderBottom === 'none' ? '' : BORDER_WIDTH
  })

  const getSwiperStyle = (item : NavButton) => ({
    width: `${Number(item.itemWidth) * multipleView.value}rpx`
  })

  const getTextClass = (index : number, item : NavButton) : string => {
    return index === TabCur.value && !item.imageUrl ? 'text-bold text-lg' : ''
  }

  const getTextStyle = (item : NavButton) => {
    const borderBottom = newData.value.borderBottom === 'none' ? '' : BORDER_WIDTH
    return {
      display: 'flex',
      alignItems: 'center',
      color: newData.value.textColor,
      fontSize: `${newData.value.fontSize * multipleView.value}rpx`,
      borderBottom
    }
  }

  const getParamsFromURL = (url : string) : UTSJSONObject => {
    if (!url || url === 'undefined') {
      return {}
    }

    const questionIndex = url.indexOf('?')
    if (questionIndex === -1) {
      return {}
    }

    try {
      const paraString = url.substring(questionIndex + 1).split('&')
      const paraObj : UTSJSONObject = {}

      for (const param of paraString) {
        const equalIndex = param.indexOf('=')
        if (equalIndex !== -1) {
          const key = param.substring(0, equalIndex)
          const value = param.substring(equalIndex + 1)
          paraObj[key] = decodeURIComponent(value)
        }
      }
      return paraObj
    } catch (error) {
      console.error('解析URL参数失败:', error)
      return {}
    }
  }

  // 事件处理函数
  const tabSelect = (e : TouchEvent) : void => {
    try {
      const target = e.currentTarget as Element
      const index = target.getAttribute('data-index')

      if (index === null) {
        console.warn('未找到tab索引')
        return
      }

      const TabCurValue = parseInt(index)
      if (isNaN(TabCurValue)) {
        console.warn('无效的tab索引:', index)
        return
      }

      // 存储选中的分类索引
      uni.setStorage({
        key: STORAGE_KEY,
        data: TabCurValue - 1,
        fail: (error) => {
          console.error('存储分类索引失败:', error)
        }
      })

      dealPage(TabCurValue)
    } catch (error) {
      console.error('处理tab选择失败:', error)
    }
  }

  const handleSwiper = (item : NavButton, key : string) : void => {
    try {
      if (!item.pageUrl) {
        console.warn('轮播项缺少页面URL')
        return
      }
      gotoPage(`${item.pageUrl}?key=${encodeURIComponent(key)}`)
    } catch (error) {
      console.error('处理轮播跳转失败:', error)
    }
  }

  const dealPage = (TabCurValue : number) : void => {
    try {
      const navButton = navButtons.value[TabCurValue]

      if (!navButton) {
        console.warn('无效的导航按钮索引:', TabCurValue)
        return
      }

      if (navButton.isNewPage === NavigationType.EVENT) {
        // 触发事件模式
        const urlParams = getParamsFromURL(navButton.pageUrl || '')
        emit('getpage', {
          index: TabCurValue,
          microId: urlParams['id'] || null
        })
        TabCur.value = TabCurValue
      } else if (navButton.isNewPage === NavigationType.NEW_PAGE && navButton.pageUrl) {
        // 新页面跳转模式
        gotoPage(navButton.pageUrl)
      }
    } catch (error) {
      console.error('处理页面跳转失败:', error)
    }
  }

  // 监听器
  watch(
    () => props.pageSelectPoi,
    (newVal : UTSJSONObject) => {
      try {
        if (newVal?.poi && Number(newVal.poi) > 0) {
          const newTabIndex = Number(newVal.poi)
          if (newTabIndex < navButtons.value.length) {
            TabCur.value = newTabIndex
            nextTick(() => {
              dealPage(newTabIndex)
            })
          }
        }
      } catch (error) {
        console.error('处理页面选择位置变化失败:', error)
      }
    },
    { immediate: true, deep: true }
  )

  // 生命周期
  onMounted(() => {
    try {
      newData.value = { ...props.modelValue } as CategoryData

      // 设置后台配置的默认分类
      if (newData.value?.selectPoi != null &&
        newData.value.selectPoi > -1 &&
        newData.value.selectPoi < navButtons.value.length) {
        TabCur.value = newData.value.selectPoi
        dealPage(TabCur.value)
      }
    } catch (error) {
      console.error('组件初始化失败:', error)
    }
  })

  onUnmounted(() => {
    // 组件卸载时可以进行其他清理工作
  })
</script>

<style scoped lang="scss">
  .cur {
    border-bottom: 2px solid;
  }

  .category-container {
    flex-wrap: nowrap;
    overflow-x: auto;
    position: relative;
    display: flex;
    white-space: nowrap;
    flex-direction: row;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .height100 {
    height: 100%;
  }
</style>