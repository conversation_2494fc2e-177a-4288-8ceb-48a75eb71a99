<template>
  <!-- 商品分类组件 -->
  <view class="div-category">
    <cu-custom v-if="isStatusBar" :bgColor="newData.background" :bgImage="newData.bgImage">
      <template #backText>返回</template>
      <template #marchContent>
        <category v-model="newData" @getpage="getpage" :isStatusBar="isStatusBar" :pageSelectPoi="pageSelectPoi">
        </category>
      </template>
    </cu-custom>
    <category v-else v-model="newData" @getpage="getpage" :isStatusBar="isStatusBar"></category>
  </view>
</template>

<script setup lang="uts">
  import { ref, toRefs } from 'vue'
  import category from './div-goods-category.uvue'

  // 定义 props 类型
  interface CategoryData {
    background : string
    bgImage ?: string
    [key : string] : any
  }

  interface Props {
    modelValue : CategoryData | null
    isStatusBar : boolean
    pageSelectPoi : UTSJSONObject
  }

  // 接收 props
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      background: ''
    }),
    isStatusBar: false,
    pageSelectPoi: () => ({})
  })

  // 定义 emits
  const emit = defineEmits<{
    getpage : [e: any]
  }>()

  // 响应式数据
  const { modelValue } = toRefs(props)
  const newData = ref<CategoryData>(modelValue.value || { background: '' })

  // 方法
  const getpage = (e : any) : void => {
    emit('getpage', e)
  }
</script>