<template>
  <!-- 商品分组组件 -->
  <view class="content" ref="content" :style="{
	  marginBottom: `${newData.marginBottomSpacing * multipleView}rpx`,
	  marginTop: `${newData.marginTopSpacing * multipleView}rpx`,
	  marginLeft: `${newData.marginLeftSpacing * multipleView}rpx`,
	  marginRight: `${newData.marginRightSpacing * multipleView}rpx`,
	}">
    <view class="pageComponent" id="titleBox" :style="{ backgroundColor: newData.background,
	      paddingBottom: `${newData.paddingBottomSpacing}px`,
	      paddingTop: `${newData.paddingTopSpacing}px`,
	      paddingLeft: `${newData.paddingLeftSpacing}px`,
	      paddingRight: `${newData.paddingRightSpacing}px`,
		  top:fixedTab? (CustomBar)+'px' :''}" :class="[fixedTab?'fixedBox':'' ]">
      <view class="cu-bar" style="min-height: 28rpx" :class="[newData.background && newData.background.indexOf('bg-') != -1
	            ? newData.background
	            : '' ]
	        ">
        <scroll-view scroll-x="true" style=" width: 100%; white-space: nowrap;" :scroll-into-view="'id-'+TabCur">
          <view class="nav text-white text-sm">
            <view style="position: relative; display: flex;">
              <!-- borderBottom: (item.titleStyle == '1' && index==TabCur)? ('solid '+ (newData.bottomBorderSize||1)+'px '+ (newData.selectTextColor||'#ff0')):'' -->
              <view :id="'id-'+index" @click="tabSelect" class="cu-item" v-for="(item, index) in newData.navButtons"
                :key="index" :data-index="index" :style="{
					  overflow: 'inherit',
	                  color: index==TabCur?(newData.selectTextColor||'#ff0'):(newData.textColor||'#fff'),
	                  fontSize:index==TabCur?(newData.selectTextSize*multipleView|| 26)+'rpx':(newData.textSize*multipleView || 24)+'rpx',
	                  marginLeft: ((newData.itemSpace*multipleView)||0) + 'rpx',
	                  marginRight: ((newData.itemSpace*multipleView)||0) + 'px',
	                  width: ((item.itemWidth*multipleView)||180)+'rpx',
					  minWidth: ((item.itemWidth*multipleView)||180)+'rpx',
					  padding:0,
					  textAlign:'center',
					  fontWeight: index==TabCur? 'bold':'normal'
	                }">
                <view v-if="item.titleStyle == '2'" class="blockImg" :class="item.titleStyle1 ? '' : 'singleStyle'">
                  <image :src="index == TabCur ? item.selectedIconPath : item.iconPath " mode="widthFix" />
                </view>
                <view v-else>{{ item.name }}</view>
                <view v-if="item.titleStyle1" class="subtitle">
                  <!-- {{item.iconPath1  }} -->
                  <view v-if="item.titleStyle1 == '2'" class="imgBox">
                    <image style="width: 100%" mode="widthFix" :src="
							index == TabCur ? item.selectedIconPath1 : item.iconPath1
						  "></image>
                  </view>

                  <text v-else>{{ item.name1 }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
    <swiper class="scroll-item-box" easing-function="linear" :current="TabCur" @change="swipeChange"
      :style="{ height: swiperHeight[TabCur] + 'px' }" @animationfinish="transitionChange">
      <block v-for="(item, index) in newData.navButtons" :key="index">
        <swiper-item>
          <view :id="'content-wrap' + index">

            <view-goods v-model="item.contentSet" :actType="newData.activityType" :params="{
				  ...newData
			  }" :contentBack="newData.background1" :loadingWay="newData.loadingWay" :child="true">
            </view-goods>
          </view>
        </swiper-item>
      </block>
    </swiper>
  </view>
</template>

<script setup lang="uts">
  import api from '@/utils/api'
  // import { EventBus } from '@/utils/eventBus.js'
  import viewGoods from "../div-goods/div-goods.uvue";
  import { useSystemStore } from '@/stores/system'
  import { useGlobalDataStore } from '@/stores/globalData'
  import { ref, reactive, computed, nextTick, watch, onMounted, onUnmounted, getCurrentInstance } from 'vue'
  // 定义类型接口
  interface NavButton {
    name : string
    name1 ?: string
    titleStyle : string
    titleStyle1 ?: string
    iconPath ?: string
    selectedIconPath ?: string
    iconPath1 ?: string
    selectedIconPath1 ?: string
    itemWidth ?: number
    pageSize ?: number
    contentSet : {
      contentStyle : string
      goodsList : any[]
      groupingList : any[] | string
      airecType ?: string
      paddingBottomSpacing ?: number
      paddingTopSpacing ?: number
      paddingLeftSpacing ?: number
      paddingRightSpacing ?: number
    }
  }

  interface ComponentData {
    background : string
    background1 ?: string
    marginBottomSpacing : number
    marginTopSpacing : number
    marginLeftSpacing : number
    marginRightSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
    bottomBorderSize ?: number
    selectTextColor ?: string
    textColor ?: string
    selectTextSize ?: number
    textSize ?: number
    itemSpace ?: number
    showType ?: string
    activityType ?: string
    loadingWay ?: string
    isShowLottery ?: number
    navButtons : NavButton[]
  }

  interface PageInfo {
    current : number
    size : number
  }

  interface ReachBottomState {
    reachBottom : boolean
  }

  // Props 定义
  interface Props {
    modelValue : ComponentData | null
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      background: '',
      marginBottomSpacing: 0,
      marginTopSpacing: 0,
      marginLeftSpacing: 0,
      marginRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0,
      navButtons: []
    })
  })

  // Store
  const systemStore = useSystemStore()
  const globalDataStore = useGlobalDataStore()

  // 响应式数据
  const newData = ref<ComponentData>(props.modelValue || {
    background: '',
    marginBottomSpacing: 0,
    marginTopSpacing: 0,
    marginLeftSpacing: 0,
    marginRightSpacing: 0,
    paddingBottomSpacing: 0,
    paddingTopSpacing: 0,
    paddingLeftSpacing: 0,
    paddingRightSpacing: 0,
    navButtons: []
  })

  const TabCur = ref<number>(0)
  const scrollLeft = ref<number>(0)
  const pageInfoCopy = reactive<PageInfo>({
    current: 1,
    size: 10
  })
  const pageInfo = reactive<Record<number, PageInfo>>({})
  const current = ref<number>(0)
  const reachBottomState = reactive<Record<number, ReachBottomState>>({})
  const swiperHeight = reactive<Record<number, number>>({})
  const fixedTab = ref<boolean>(false)
  const titleBoxHeight = ref<number>(0)
  const contentElemToTop = ref<number>(0)
  const CustomBar = ref<number>(globalDataStore.CustomBar || 0)
  const theme = ref(globalDataStore.theme)

  // 计算属性
  const isPhone = computed(() => systemStore.isPhone)
  const windowWidth = computed(() => systemStore.windowWidth)
  const multipleView = computed(() => systemStore.multipleView)

  const screenHeight = computed(() : string => {
    const _systemInfo = uni.getSystemInfoSync()
    const pixelRatio = _systemInfo.pixelRatio
    const screenHeight = _systemInfo.windowHeight
    return (screenHeight - CustomBar.value - 90 / pixelRatio) + 'px'
  })

  const calculateWidth = computed(() : number => {
    if (!newData.value || !newData.value.showType || !newData.value.navButtons) return 0

    let _width = 0
    const code = newData.value.showType
    const num = newData.value.navButtons.length

    if (code == "60") {
      _width = (100 / 6) * num
    } else if (code == "72") {
      _width = (100 / 5) * num
    } else {
      _width = (100 / 4) * num
    }
    return _width
  })

  // 方法定义
  const tabSelect = (e : any) => {
    const tabIndex = e.currentTarget.dataset.index
    if (tabIndex == TabCur.value) {
      return
    }
    TabCur.value = tabIndex
  }

  const getData = () => {
    const _index = TabCur.value
    const _navButtonList = newData.value.navButtons
    const contentStyle = _navButtonList[_index].contentSet.contentStyle

    if (_navButtonList[_index].contentSet.goodsList.length <= 0) {
      if (contentStyle == "2") {
        getGroupsGoods()
      } else if (contentStyle == "3") {
        getGoodsRecommend(_navButtonList[_index].contentSet.airecType || '')
      } else {
        getElementHeight()
      }
    } else {
      getElementHeight()
    }
  }

  const getGroupsGoods = () => {
    const _index = TabCur.value
    const groupingList = newData.value.navButtons[_index].contentSet.groupingList

    api.goodsGroupsGet(
      Object.assign(
        pageInfo[_index], {
        goodsGroupIds: groupingList
      }
      )
    ).then((res : any) => {
      const records = res.data.records
      if (records.length < pageInfo[_index].size) {
        reachBottomState[_index].reachBottom = false
      }
      newData.value.navButtons[_index].contentSet.goodsList.push(...records)
      nextTick(() => {
        getElementHeight()
      })
    })
  }

  const getGoodsRecommend = (sceneId : string) => {
    const _index = TabCur.value
    api.getRecommendList({
      sceneId: sceneId,
    }).then((res : any) => {
      if (res.data.length < pageInfo[_index].size) {
        reachBottomState[_index].reachBottom = false
      }
      newData.value.navButtons[_index].contentSet.goodsList.push(...res.data)
      nextTick(() => {
        getElementHeight()
      })
    })
  }

  const swipeChange = (event : any) => {
    TabCur.value = event.detail.current
    getData()
  }

  const scrolltolower = (e ?: any) => {
    console.error("=========触发底部============")

    if (newData.value.loadingWay == '1') {
      return
    }

    const _index = TabCur.value
    const _navButtonList = newData.value.navButtons
    const contentStyle = _navButtonList[_index].contentSet.contentStyle
    const reachBottom = reachBottomState[_index] ? reachBottomState[_index].reachBottom : false

    if (!reachBottom || contentStyle == "1") {
      return
    }

    pageInfo[_index].current += 1

    if (contentStyle == "2") {
      getGroupsGoods()
    } else if (contentStyle == "3") {
      // TODO: 处理智能推荐
    }
  }

  const getElementHeight = () => {
    nextTick(() => {
      const element = "#content-wrap" + TabCur.value
      const query = uni.createSelectorQuery().in(getCurrentInstance())
      query.select(element).boundingClientRect()

      query.exec((res : any[]) => {
        if (res && res[0]) {
          const _hei = res[0].height > 0 ? res[0].height : parseInt(screenHeight.value)
          console.log("看看高度：", _hei)
          swiperHeight[TabCur.value] = _hei
        }
      })
    })
  }

  const transitionChange = () => {
    getElementHeight()
  }

  const scrollToTop = (pageScroll : any) => {
    if (!(contentElemToTop.value > 0)) {
      const query = uni.createSelectorQuery().in(getCurrentInstance())
      query.select('#titleBox').boundingClientRect()
      query.exec((res : any[]) => {
        if (res && res[0]) {
          if (res[0].top <= CustomBar.value) {
            fixedTab.value = true
            contentElemToTop.value = pageScroll.scrollTop
          } else {
            fixedTab.value = false
          }
        }
      })
    } else {
      const scrollTop = pageScroll.scrollTop
      if (scrollTop >= contentElemToTop.value && contentElemToTop.value > 0) {
        fixedTab.value = true
      } else {
        fixedTab.value = false
      }
    }
  }

  const scrollViewScroll = (event : any) => {
    // 处理滚动事件
  }

  // 组件初始化
  const initComponent = () => {
    const _navButtonList = newData.value.navButtons
    _navButtonList.forEach((item : NavButton, index : number) => {
      if (item.contentSet.contentStyle != '1') {
        item.contentSet.goodsList = []
        const _page = JSON.parse(JSON.stringify(pageInfoCopy))
        if (item.pageSize) _page.size = item.pageSize
        pageInfo[index] = _page
        reachBottomState[index] = {
          reachBottom: true
        }
      }

      // 处理分组数据
      if (item.contentSet.contentStyle == '2') {
        const groupsId : string[] = []
        if (Array.isArray(item.contentSet.groupingList)) {
          item.contentSet.groupingList.forEach((groupItem : any) => {
            groupsId.push(groupItem.id)
          })
          item.contentSet.groupingList = groupsId.join(",")
        }
      }

      item.contentSet.paddingBottomSpacing = newData.value.paddingBottomSpacing
      item.contentSet.paddingTopSpacing = newData.value.paddingTopSpacing
      item.contentSet.paddingLeftSpacing = newData.value.paddingLeftSpacing
      item.contentSet.paddingRightSpacing = newData.value.paddingRightSpacing
    })

    // 判断第一个按钮下的商品类型
    const contentStyle = _navButtonList[0]?.contentSet.contentStyle
    if (contentStyle == "2") {
      getGroupsGoods()
    } else if (contentStyle == "3") {
      // TODO: 处理智能推荐
    } else {
      getElementHeight()
      setTimeout(() => {
        getElementHeight()
      }, 3000)
    }
  }

  // 生命周期
  onMounted(() => {
    initComponent()

    if (newData.value.isShowLottery == 1) {
      // @ts-ignore
      // getCurrentInstance()?.appContext.app.config.globalProperties.$EventBus?.$emit("handleNyRedPacket", newData.value.activityType)
    }

    // 监听页面滚动事件
    uni.$on('vonPageScroll', scrollToTop)
    // EventBus.$on("divGoodsGroupsReachBottom", () => {
    //   scrolltolower()
    // })
  })

  onUnmounted(() => {
    uni.$off('vonPageScroll', scrollToTop)
    // EventBus.$off('divGoodsGroupsReachBottom')
  })

  // 监听 props 变化
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      newData.value = newValue
    }
  }, { deep: true, immediate: true })
</script>

<style scoped lang="scss">
  .scrollBox {
    overflow-x: scroll;
  }

  .content {
    max-width: 750rpx;
    overflow: hidden;
  }

  .action {
    position: absolute;
    right: 12rpx;
    top: 50%;
    transform: translateY(-50%);
  }

  .title-box {
    height: 90rpx;
  }

  .cu-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: none;
    overflow: hidden;
    flex-direction: column;
    line-height: normal;

    view {
      width: 100%;
    }

    .nav-name {
      text-align: center;
    }

    .title {
      font-size: 28rpx;
      text-align: center;
    }

    .subtitle {
      font-size: 24rpx;
    }

    .imgBox {
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;
      align-items: center;
    }

    .blockImg {
      display: block;
      height: 90rpx !important;
    }

    .singleStyle {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .tit-amplification {
    // transform: scale(1.2);
  }

  .scroll-item-box {
    min-height: 80vh;
  }

  .fixedBox {
    position: fixed;
    left: 0;
    right: 0;
    top: 60px;
    z-index: 9999;
  }

  .paddingTop {
    padding-top: 60rpx;
  }
</style>