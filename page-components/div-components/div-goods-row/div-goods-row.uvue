<template>
  <!-- 商品显示组件-横向排列方式显示 -->
  <view :class="classCom" :style="{
	        marginBottom: `${Number(newData.marginBottomSpacing)*multipleView}rpx`,
	        marginTop: `${newData.marginTopSpacing*multipleView}rpx`,
	        marginLeft: `${newData.marginLeftSpacing*multipleView}rpx`,
	        marginRight: `${newData.marginRightSpacing*multipleView}rpx`,
	      }">
    <view class="wrapper-list-goods" :style="{
		   backgroundColor: newData.background,
		   backgroundImage: `url(${newData.bgImage})`,
		   backgroundSize: `cover`,
		   overflow: `hidden`,
		   paddingBottom: `${newData.paddingBottomSpacing*multipleView}rpx`,
		   paddingTop: `${newData.paddingTopSpacing*multipleView}rpx`,
		   paddingLeft: `${newData.paddingLeftSpacing*multipleView}rpx`,
		   paddingRight: `${newData.paddingRightSpacing*multipleView}rpx`,
		}">
      <view v-if="newData.titleShow == '1' || newData.moreType == '1'"
        class="flex align-center justify-between margin-bottom">
        <view class="text-df margin-left-sm" :style="{color: `${newData.titleColor}`}">
          <text class="text-bold" v-if="newData.titleShow == '1'" :class="newData.titleIcon"></text>
          <text class="margin-left-xs" v-if="newData.titleShow == '1'">{{newData.title}}</text>
        </view>
        <div-base-navigator v-if="newData.moreType == '1'" :pageUrl="newData.pageUrl" hover-class="none"
          class="text-sm">更多<text class="cuIcon-right"></text></div-base-navigator>
      </view>
      <navigator hover-class="none" style="flex: 1;" class="titleBox" v-if="newData.name"
        :url="'/pages/shop/shop-detail/index?id=' + newData.id">
        <view class="" style='display: flex;justify-content:space-between;align-items: center;'>
          <view style="display:flex;padding-bottom: 10rpx;">
            <view class="cuIcon-shop"></view>
            <text class="text-black text-bold margin-left-sm shop-name">{{newData.name}}</text>
          </view>
          <view class="" style="font-size: 25rpx;">
            进店看看 <text class="cuIcon-right text-sm"></text>
          </view>
        </view>
      </navigator>
      <view class="flex">
        <view>
          <image mode="aspectFill" style="width: 262rpx;height: 416rpx;" v-if="newData.bannerImage"
            class="margin-right-xs" :src="newData.bannerImage"></image>
        </view>

        <scroll-view class="scroll-view_x" scroll-x="true" style="overflow:hidden;">
          <block v-for="(item, index) in newData.goodsList" :key="index">
            <view hover-class="none" @click="toPage('/pages/goods/goods-detail/index?id=' + item.id,'goodDetail', item)"
              class="item flex goods-box radius">
              <view class="img-box">
                <view :class="item && item.estimatedPriceVo && (item.estimatedPriceVo.discountPrice &&
                item.estimatedPriceVo.discountPrice != '0.0'&&
                item.estimatedPriceVo.discountPrice != '0') ||
                (item.estimatedPriceVo.promotionsDiscountPrice &&
                  item.estimatedPriceVo.promotionsDiscountPrice !=
                    '0.0'&&item.estimatedPriceVo.promotionsDiscountPrice !='0') ||
                (item.estimatedPriceVo.coupon &&
                  item.estimatedPriceVo.coupon != '0.0'
                  &&item.estimatedPriceVo.coupon != '0')?'activity-img-box':''" :style="{
                  borderColor
                }">

                  <image lazy-load :lazy-load-margin="0" fade-show :src="$formatImg360(item.picUrls)" mode="aspectFill"
                    height="280"></image>
                  <member-icon v-if="item.memberLevelLimit&&item.memberLevelLimit!='101'"
                    :memberLevelLimit="item.memberLevelLimit">
                  </member-icon>
                </view>
                <view style="position: absolute;
                  left: 0;
                  right: 0;" :style="{
                  top: spuPriceStyle==1? 0:'auto',
                  bottom: spuPriceStyle==1? 0:'-10rpx'
                }" v-if="
              item && item.estimatedPriceVo && (item.estimatedPriceVo.discountPrice &&
              item.estimatedPriceVo.discountPrice != '0.0'&&
              item.estimatedPriceVo.discountPrice != '0') ||
              (item.estimatedPriceVo.promotionsDiscountPrice &&
                item.estimatedPriceVo.promotionsDiscountPrice !=
                  '0.0'&&item.estimatedPriceVo.promotionsDiscountPrice !='0') ||
              (item.estimatedPriceVo.coupon &&
                item.estimatedPriceVo.coupon != '0.0'
                &&item.estimatedPriceVo.coupon != '0')">
                  <good-price :goodsSpu="item" small></good-price>
                </view>
                <view class="sell-out" v-if="item.inventoryState==1">
                  <view class="sell-out-text">售罄</view>
                </view>
              </view>
              <view class="text-cut text-black text-xsm padding-left-sm padding-top-sm"
                :class="isIOS?'text-bold':'text-middle'">
                {{item.name}}
              </view>

              <!-- <view class="margin-top-xs text-sm text-gray padding-left-sm overflow-2">{{item.sellPoint}}</view> -->
              <!-- 积分商品 -->
              <view v-if="item.spuType == 3 || item.spuType == 5" class="margin-top-xs margin-left-sm ">
                <text class="text-gray text-xs">积分：</text>
                <text class="text-red text-lg text-bold">{{item.goodsPoints ? item.goodsPoints : 0}}</text>
              </view>
              <!-- 付费商品 -->
              <view v-else class="text-red margin-left-sm margin-top-xs text-lg text-bold">
                <!-- <text>{{item.priceDown}}</text> -->
                <price-handle :value="item.priceDown" signFont="24rpx" bigFont="28rpx" smallFont="22rpx">
                  <view slot="append" style="display: inline-block;">
                    <text v-if="item.estimatedPriceVo&&item.priceDown!=item.estimatedPriceVo.price"
                      class="text-red text-xs" style="font-weight: 500;padding-left: 6rpx">劵后价</text>
                    <text v-if="item.inventoryState==1" class="text-gray text-xs"
                      style="font-weight: 500;padding-left: 6rpx">已售罄</text>

                    <text
                      v-if="item.estimatedPriceVo&&item.estimatedPriceVo.originalPrice!='0.0'&&item.priceDown!= item.estimatedPriceVo.originalPrice"
                      class="price-original text-xs">￥{{ item.estimatedPriceVo.originalPrice }}</text>
                    <text class="text-xs padding-lr-xs"
                      style="color: #9c9ba1;font-weight: 300;">已售{{$saleNumFilter(item.saleNum)}}</text>
                  </view>
                </price-handle>
              </view>
            </view>
          </block>
          <view hover-class="none" class="item flex goods-box radius" style="background-color: #F1F1F1;">
            <div-base-navigator v-if="newData.moreType == '2'" :pageUrl="newData.pageUrl"
              :styleProps="{backgroundColor: '#F1F1F1'}"
              style="background-color: #F1F1F1; font-size: 24rpx;top: 0; box-shadow: 0;height: 344rpx;"
              class="item flex goods-box radius">
              <view style="position: absolute; top: 120rpx;">
                <text style="padding-left: 68rpx;">更多全部</text>
                <image style="width: 30rpx; height: 30rpx;vertical-align: text-top;"
                  src="https://img.songlei.com/1/material/8fe2787f-939e-4f08-be2a-5725d8a19894.png" />
              </view>
            </div-base-navigator>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  import lazyLoad from "@/components/lazy-load/index.uvue"
  import divBaseNavigator from '../div-base/div-base-navigator.uvue'
  // import goodPrice from "components/good-price/good-price.uvue"
  // import memberIcon from '@/components/member-icon/index.uvue'
  import { isIOS } from "@/utils/util.uts"
  // import { navigateUtil } from "@/static/mixins/navigateUtil.js"
  import { useSystemStore } from '@/stores/system'
  import { useGlobalDataStore } from '@/stores/globalData'


  // 定义类型接口
  interface EstimatedPriceVo {
    discountPrice ?: string
    promotionsDiscountPrice ?: string
    coupon ?: string
    price ?: string
    originalPrice ?: string
  }

  interface GoodsItem {
    id : string
    name : string
    picUrls : string[]
    memberLevelLimit ?: string
    inventoryState ?: number
    spuType ?: number
    goodsPoints ?: number
    priceDown : string
    estimatedPriceVo ?: EstimatedPriceVo
    saleNum : number
  }

  interface ComponentData {
    title ?: string
    titleColor ?: string
    titleIcon ?: string
    titleShow ?: string
    moreType ?: string
    pageUrl ?: string
    name ?: string
    id ?: string
    bannerImage ?: string
    pageSpacing ?: number
    goodsList : GoodsItem[]
    background ?: string
    bgImage ?: string
    marginBottomSpacing : number
    marginTopSpacing : number
    marginLeftSpacing : number
    marginRightSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
  }

  // Props 定义
  interface Props {
    modelValue : ComponentData | null
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      title: '商品甄选',
      titleColor: 'red',
      titleIcon: 'cuIcon-message',
      pageSpacing: 0,
      goodsList: [],
      background: "#fff",
      marginBottomSpacing: 0,
      marginTopSpacing: 0,
      marginLeftSpacing: 0,
      marginRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0
    })
  })

  // Store 和全局数据
  const systemStore = useSystemStore()
  const globalDataStore = useGlobalDataStore()

  // 响应式数据
  const newData = ref<ComponentData>(props.modelValue || {
    title: '商品甄选',
    titleColor: 'red',
    titleIcon: 'cuIcon-message',
    pageSpacing: 0,
    goodsList: [],
    background: "#fff",
    marginBottomSpacing: 0,
    marginTopSpacing: 0,
    marginLeftSpacing: 0,
    marginRightSpacing: 0,
    paddingBottomSpacing: 0,
    paddingTopSpacing: 0,
    paddingLeftSpacing: 0,
    paddingRightSpacing: 0
  })

  const isIOS = ref<boolean>(isIOS())
  const theme = ref(globalDataStore.theme)

  // 计算属性
  const isPhone = computed(() => systemStore.isPhone)
  const multipleView = computed(() => systemStore.multipleView)

  const classCom = computed(() : string => {
    return newData.value.background && newData.value.background.indexOf('bg-') != -1 ?
      newData.value.background :
      ''
  })

  const borderColor = computed(() : string => {
    if (theme.value && theme.value.customFiled) {
      const customFiled = JSON.parse(theme.value.customFiled)
      return customFiled.spuPriceStyle == 1 ? '' : customFiled.spuPicBorder
    }
    return ""
  })

  const spuPriceColor = computed(() : string => {
    if (theme.value && theme.value.customFiled) {
      const customFiled = JSON.parse(theme.value.customFiled)
      if (customFiled.spuPriceStyle == 1) {
        return customFiled.spuPriceColor1 || '#fff'
      }
      return customFiled.spuPriceColor2 || '#fff'
    }
    return "#fff"
  })

  const spuPriceStyle = computed(() : number => {
    if (theme.value && theme.value.customFiled) {
      const customFiled = JSON.parse(theme.value.customFiled)
      return customFiled.spuPriceStyle || 2
    }
    return 2
  })

  // 方法定义
  const jumpPage = (page : string) => {
    if (page) {
      uni.navigateTo({
        url: page
      })
    }
  }

  const imgLoaded = (item : GoodsItem) => {
    handleExposeSkuComponent(item)
  }

  // 使用 mixin 中的方法
  const { toPage, handleExposeSkuComponent } = { toPage: () => { }, handleExposeSkuComponent: () => { } }

  // 监听 props 变化
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      newData.value = newValue
    }
  }, { deep: true, immediate: true })
</script>

<style scoped lang="scss">
  .wrapper-list-goods {
    white-space: nowrap;
    // padding: 10rpx;
  }

  .wrapper-list-goods .item {
    display: inline-block;
    width: 260rpx;
    // height: 500rpx;
    height: auto;
    // margin-top: 20px;
    padding-bottom: 10px;
    // margin-left: 10rpx;
    margin-right: 10rpx;
    background-color: #fff;
    box-shadow: 0px 0px 20px #e5e5e5;
  }


  .wrapper-list-goods .item .img-box {
    width: 100%;
    height: 280rpx;
    max-height: 280rpx;
    overflow: hidden;
    position: relative;
  }

  .wrapper-list-goods .item .img-box image {
    width: 100%;
    height: 280rpx;
    // border-radius: 5rpx 5rpx 0 0;
  }

  .activity-img-box {
    width: 100%;
    border-left: solid 6rpx transparent;
    border-top: solid 6rpx transparent;
    border-right: solid 6rpx transparent;
  }

  .goods-box {
    overflow: hidden;
  }
</style>