<template>
  <view :style="containerStyle">
    <view class="title-bar" v-if="newData.titleShow">
      <view class="title-action" :style="{ color: newData.titleColor }">
        <text class="cuIcon-move"></text>
        <text class="text-sm" :class="newData.titleIcon"></text>
        {{ newData.title }}
        <text class="cuIcon-move"></text>
      </view>
    </view>

    <view v-if="newData.goodsList && newData.goodsList.length > 0">
      <template v-if="actType == '2'">
        <goods-row :goodsList="newData.goodsList" :params="params" :actType="actType"
          :goodsSpace="newData.goodsSpace" />
      </template>
      <template v-else-if="newData.showType == 'row'">
        <goods-row :goodsList="newData.goodsList" :newData="newData" :actType="actType"
          :goodsSpace="newData.goodsSpace" />
      </template>
      <view v-else-if="newData.showType == 'card'">
        <goods-card :goodsList="newData.goodsList" :goodsSpace="newData.goodsSpace"
          :borderRadiusLeftTop="newData.borderRadiusLeftTop" :borderRadiusRightTop="newData.borderRadiusRightTop"
          :customCoverImage="newData.coverImage" :isLazy="false" />
      </view>
      <template v-else-if="newData.showType == 'three'">
        <goods-three-column :goodsList="newData.goodsList" :goodsSpace="newData.goodsSpace" />
      </template>
      <template v-else-if="newData.showType == 'nine' && newData.goodsFromType != '7'">
        <goods-nine-column :newData="newData" :goodsList="newData.goodsList" :goodsSpace="newData.goodsSpace" />
      </template>
      <template v-else-if="newData.showType == 'waterfall'">
        <goods-waterfall :newData="newData" :fullWidth="fullWidth" :goodsSpace="newData.goodsSpace"
          :borderRadiusLeftTop="newData.borderRadiusLeftTop" :borderRadiusRightTop="newData.borderRadiusRightTop"
          :customCoverImage="newData.coverImage" />
      </template>
      <template v-else-if="newData.showType == 'tradeState'">
        <goods-waterfall-recommend-mixed :newData="newData" :goodsSpace="newData.goodsSpace"
          :borderRadiusLeftTop="newData.borderRadiusLeftTop" :borderRadiusRightTop="newData.borderRadiusRightTop"
          :customCoverImage="newData.coverImage" :newDataSize="size" :fullWidth="fullWidth" />
      </template>
    </view>

    <view v-if="loadingWay == '1'" class="button-container">
      <image :src="newData.buttonImg" mode="aspectFill" class="buttonImg" @click="jumpPage(newData.pageUrl)" />
    </view>
  </view>
</template>

<script setup lang="uts">
  // import goodsThreeColumn from '@/components/goods-three-column/index.uvue'
  // import goodsNineColumn from '@/components/goods-nine-column/index.uvue'
  // import goodsRow from '@/components/goods-row/index.uvue'
  // import goodsWaterfall from '@/components/goods-waterfall/index.uvue'
  // import goodsWaterfallRecommendMixed from '@/components/goods-waterfall-recommend-mixed/index.uvue'
  // import divBaseNavigator from '../div-base/div-base-navigator.uvue'
  // import noData from '@/components/base-nodata/index.uvue'

  type GoodsData = {
    showType ?: string
    pageSpacing ?: number
    goodsList ?: any[]
    background ?: string
    bgImage ?: string
    marginBottomSpacing ?: number
    marginTopSpacing ?: number
    marginLeftSpacing ?: number
    marginRightSpacing ?: number
    paddingBottomSpacing ?: number
    paddingTopSpacing ?: number
    paddingLeftSpacing ?: number
    paddingRightSpacing ?: number
    titleShow ?: boolean
    titleColor ?: string
    titleIcon ?: string
    title ?: string
    goodsSpace ?: number
    borderRadiusLeftTop ?: number
    borderRadiusRightTop ?: number
    coverImage ?: string
    goodsFromType ?: string
    buttonImg ?: string
    pageUrl ?: string
    [key : string] : any
  }

  const props = defineProps<{
    params ?: Record<string, any>
    modelValue : GoodsData
    child ?: boolean
    contentBack ?: string
    loadingWay ?: string
    shopId ?: string | number
    size ?: number
    actType ?: string
  }>()

  const newData = ref<GoodsData>({})

  const containerStyle = computed(() => ({
    backgroundColor: props.child ? props.contentBack : newData.value.background,
    backgroundRepeat: 'no-repeat',
    backgroundImage: `url(${newData.value.bgImage})`,
    backgroundSize: '100% 100%',
    width: newData.value.showType == 'nine' ? '' : '100%',
    minHeight: '100%',
    marginBottom: `${(newData.value.marginBottomSpacing || 0) * 2}rpx`,
    marginTop: `${(newData.value.marginTopSpacing || 0) * 2}rpx`,
    marginLeft: `${(newData.value.marginLeftSpacing || 0) * 2}rpx`,
    marginRight: `${(newData.value.marginRightSpacing || 0) * 2}rpx`,
    paddingBottom: `${(newData.value.paddingBottomSpacing || 0) * 2}rpx`,
    paddingTop: `${(newData.value.paddingTopSpacing || 0) * 2}rpx`,
    paddingLeft: `${(newData.value.paddingLeftSpacing || 0) * 2}rpx`,
    paddingRight: `${(newData.value.paddingRightSpacing || 0) * 2}rpx`
  }))

  const fullWidth = computed(() : number => {
    let result = 750
    if (newData.value.marginLeftSpacing && newData.value.marginLeftSpacing > 0) {
      result -= newData.value.marginLeftSpacing * 2
    }
    if (newData.value.paddingLeftSpacing && newData.value.paddingLeftSpacing > 0) {
      result -= newData.value.paddingLeftSpacing * 2
    }
    if (newData.value.marginRightSpacing && newData.value.marginRightSpacing > 0) {
      result -= newData.value.marginRightSpacing * 2
    }
    if (newData.value.paddingRightSpacing && newData.value.paddingRightSpacing > 0) {
      result -= newData.value.paddingRightSpacing * 2
    }
    return result
  })

  const jumpPage = (page ?: string) => {
    if (page) {
      uni.navigateTo({
        url: page
      })
    }
  }

  watch(() => props.modelValue, (newVal : GoodsData) => {
    newData.value = newVal
  }, { immediate: true })
</script>

<style scoped lang="scss">
  .title-bar {
    min-height: 80upx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .title-action {
    font-weight: bold;
    font-size: 14px;
  }

  .button-container {
    padding-bottom: 12rpx;
  }

  .buttonImg {
    width: 234rpx;
    height: 46rpx;
    margin: 0 auto;
    display: block;
  }
</style>