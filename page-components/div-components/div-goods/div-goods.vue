<template>
	<!-- 商品显示 组件 -->
	<view
		:style="{
			backgroundColor: child ? contentBack : newData.background,
			backgroundRepeat: 'no-repeat',
			backgroundImage: `url(${newData.bgImage})`,
			backgroundSize: '100% 100%',
			width: newData.showType == 'nine' ? '' : '100%',
			minHeight: '100%',
			marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			marginTop: `${newData.marginTopSpacing * 2}rpx`,
			marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			marginRight: `${newData.marginRightSpacing * 2}rpx`,
			paddingBottom: `${newData.paddingBottomSpacing * 2}rpx`,
			paddingTop: `${newData.paddingTopSpacing * 2}rpx`,
			paddingLeft: `${newData.paddingLeftSpacing * 2}rpx`,
			paddingRight: `${newData.paddingRightSpacing * 2}rpx`
		}"
	>
		<view class="cu-bar justify-center" style="min-height: 80upx" v-if="newData.titleShow">
			<view class="action text-bold text-sm" :style="{ color: `${newData.titleColor}` }">
				<text class="cuIcon-move"></text>
				<text class="text-sm" :class="newData.titleIcon"></text>
				{{ newData.title }}
				<text class="cuIcon-move"></text>
			</view>
		</view>

		<view v-if="newData.goodsList && newData.goodsList.length > 0">
			<template v-if="actType == '2'">
				<goods-row :goodsList="newData.goodsList"  :params="params" :actType="actType" :goodsSpace="newData.goodsSpace"></goods-row>
			</template>
			<template v-else-if="newData.showType == 'row'">
				<goods-row :goodsList="newData.goodsList"  :newData="newData" :actType="actType" :goodsSpace="newData.goodsSpace"></goods-row>
			</template>
			<view v-else-if="newData.showType == 'card'">
				<goods-card
					:goodsList="newData.goodsList"
					:goodsSpace="newData.goodsSpace"
					:borderRadiusLeftTop="newData.borderRadiusLeftTop"
					:borderRadiusRightTop="newData.borderRadiusRightTop"
					:customCoverImage="newData.coverImage"
					:isLazy="false"
				></goods-card>
			</view>
			<template v-else-if="newData.showType == 'three'">
				<goods-three-column :goodsList="newData.goodsList" :goodsSpace="newData.goodsSpace"></goods-three-column>
			</template>
			<template v-else-if="newData.showType == 'nine' && newData.goodsFromType != '7'">
				<goods-nine-column :newData="newData" :goodsList="newData.goodsList" :goodsSpace="newData.goodsSpace"></goods-nine-column>
			</template>
			<!-- 瀑布流 -->
			<template v-else-if="newData.showType == 'waterfall'">
				<goods-waterfall
					:newData="newData"
					:fullWidth="fullWidth"
					:goodsSpace="newData.goodsSpace"
					:borderRadiusLeftTop="newData.borderRadiusLeftTop"
					:borderRadiusRightTop="newData.borderRadiusRightTop"
					:customCoverImage="newData.coverImage"
				></goods-waterfall>
			</template>
			<!-- 推荐混排--瀑布流，包含商品，视频，直播，猜你想看更多等 tradeState-->
			<template v-else-if="newData.showType == 'tradeState'">
				<goods-waterfall-recommend-mixed
					:newData="newData"
					:goodsSpace="newData.goodsSpace"
					:borderRadiusLeftTop="newData.borderRadiusLeftTop"
					:borderRadiusRightTop="newData.borderRadiusRightTop"
					:customCoverImage="newData.coverImage"
					:newDataSize="size"
					:fullWidth="fullWidth"
				/>
			</template>
		</view>
		<!-- <no-data v-else  /> -->
		<view v-if="loadingWay == '1'" style="padding-bottom: 12rpx">
			<image :src="newData.buttonImg" mode="aspectFill" class="buttonImg" @click="jumpPage(newData.pageUrl)"></image>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import goodsThreeColumn from 'components/goods-three-column/index'
import goodsNineColumn from 'components/goods-nine-column/index'
import goodsRow from 'components/goods-row/index'
import goodsWaterfall from 'components/goods-waterfall/index'
import goodsWaterfallRecommendMixed from 'components/goods-waterfall-recommend-mixed/index'
import divBaseNavigator from '../div-base/div-base-navigator.vue'
import noData from 'components/base-nodata/index.vue'

const props = defineProps({
	params: {
		type: Object,
		default: () => ({})
	},
	value: {
		type: Object,
		default: () => ({
			showType: 'row',
			pageSpacing: 0,
			goodsList: []
		})
	},
	// 是否是嵌套在分组中的组件
	child: {
		type: Boolean,
		default: false
	},
	// 分组嵌套组件 传入的 内容背景颜色
	contentBack: {
		type: String,
		default: ''
	},
	// 分组加载方式   0：上滑加载；1：跳转页面
	loadingWay: {
		type: String,
		default: ''
	},
	shopId: {
		type: [String, Number],
		default: -1
	},
	//业态初始加载的数据页数
	size: {
		type: Number,
		default: 3
	},
	// 活动类型 1、常规活动，2、春节红包
	actType: {
		type: String,
		default: ''
	}
})

const newData = ref({})

watch(
	() => props.value,
	(newVal) => {
		newData.value = newVal
	},
	{ immediate: true }
)

const fullWidth = computed(() => {
	let result = 750
	if (newData.value.marginLeftSpacing > 0) result -= newData.value.marginLeftSpacing * 2
	if (newData.value.paddingLeftSpacing > 0) result -= newData.value.paddingLeftSpacing * 2
	if (newData.value.marginRightSpacing > 0) result -= newData.value.marginRightSpacing * 2
	if (newData.value.paddingRightSpacing > 0) result -= newData.value.paddingRightSpacing * 2
	return result
})

const jumpPage = (page) => {
	if (page) {
		uni.navigateTo({
			url: page
		})
	}
}
</script>

<style scoped lang="scss">
.buttonImg {
	width: 234rpx;
	height: 46rpx;
	margin: 0 auto;
	display: block;
}
</style>
