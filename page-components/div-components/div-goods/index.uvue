<template>
  <!-- 商品显示组件 -->
  <view>
    <div-goods v-model="newData" :params="params" :shopId="shopId" :size="recordCurrentPage" />
    <view v-if="newData && newData.showType == 'tradeState'"
      :class="'cu-load bg-gray ' + (loadmore ? 'loading' : 'over')"></view>
  </view>
</template>

<script setup lang="uts">
  import { ref, toRefs, onMounted } from 'vue'
  import divGoods from './div-goods.uvue'
  import api from '@/utils/api'
  // import { EventBus } from '@/utils/eventBus.js'

  // 定义接口类型
  interface GoodsData {
    showType : string
    pageSpacing : number
    goodsList : any[]
    [key : string] : any
  }

  interface Props {
    modelValue : GoodsData | null
    shopId : string | number
    params : UTSJSONObject
  }

  // 接收 props
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      showType: 'row',
      pageSpacing: 0,
      goodsList: []
    }),
    shopId: -1,
    params: () => ({})
  })

  // 响应式数据
  const { modelValue } = toRefs(props)
  const newData = ref<GoodsData>(modelValue.value || {
    showType: 'row',
    pageSpacing: 0,
    goodsList: []
  })
  const recordCurrentPage = ref<number>(10)
  const loadmore = ref<boolean>(true)

  // 生命周期
  onMounted(() => {
    if (props.modelValue) {
      newData.value = { ...props.modelValue }
    }
  })
</script>