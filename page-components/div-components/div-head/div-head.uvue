<template>
	<!-- 顶部导航栏组件 -->
	<cu-custom
		v-if="isStatusBar"
		:bgColor="newData.backgroundColor"
		:bgImage="newData.bgImage"
		:isBack="isBack"
		:hideMarchContent="true"
	>
		<template #content>
			<text :style="{
				color: newData.color,
				fontSize: newData.fontSize * 2 + 'rpx'
			}">{{ newData.title }}</text>
		</template>
	</cu-custom>

	<div-base-navigator
		v-else
		:pageUrl="newData.pageUrl"
	>
		<view class="head-container" :style="{
			backgroundColor: newData.backgroundColor,
			backgroundImage: `url(${newData.bgImage})`
		}">
			<text :style="{
				color: newData.color,
				fontSize: newData.fontSize * 2 + 'rpx'
			}">{{ newData.title }}</text>
		</view>
	</div-base-navigator>
</template>

<script setup lang="uts">
import { ref, toRefs } from 'vue'
import divBaseNavigator from '../div-base/div-base-navigator.uvue'

// 定义接口类型
interface HeadData {
	backgroundColor: string
	pageUrl: string
	bgImage: string
	color: string
	fontSize: number
	title: string
	[key: string]: any
}

interface Props {
	modelValue: HeadData | null
	isStatusBar: boolean
	isBack: boolean
}

// 接收 props
const props = withDefaults(defineProps<Props>(), {
	modelValue: () => ({
		backgroundColor: '',
		pageUrl: '',
		bgImage: '',
		color: '#000000',
		fontSize: 16,
		title: ''
	}),
	isStatusBar: false,
	isBack: true
})

// 响应式数据
const { modelValue } = toRefs(props)
const newData = ref<HeadData>(modelValue.value || {
	backgroundColor: '',
	pageUrl: '',
	bgImage: '',
	color: '#000000',
	fontSize: 16,
	title: ''
})
</script>

<style scoped lang="scss">
.head-container {
	min-height: 80rpx;
	background-size: cover;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>