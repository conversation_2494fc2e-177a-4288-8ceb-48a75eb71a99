<template>
	<!-- 单图显示组件 -->
	<view 
		:style="{
			marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
			marginTop: `${newData.marginTopSpacing * 2}rpx`,
			marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
			marginRight: `${newData.marginRightSpacing * 2}rpx`
		}" 
		:class="newData.background && newData.background.length > 0 && newData.background != undefined ? newData.background : ''"
	>
		<view :style="{
			backgroundColor: newData.background,
			backgroundSize: 'cover',
			paddingBottom: `${newData.paddingBottomSpacing}px`,
			paddingTop: `${newData.paddingTopSpacing}px`,
			paddingLeft: `${newData.paddingLeftSpacing}px`,
			paddingRight: `${newData.paddingRightSpacing}px`,
			lineHeight: '0'
		}">
			<view v-for="(item, index) in newData.imageUrls" :key="index">
				<div-base-navigator :pageUrl="newData.pageUrls[index]" style="background: #FFFFFF;">
					<image 
						:src="$formatImg750(item)" 
						style="width: 100%; display: block;" 
						mode="widthFix"  
						:show-menu-by-longpress="newData.canLongPress == 1"
					></image>
				</div-base-navigator>
			</view>
		</view>
	</view>
</template>

<script setup lang="uts">
import { ref, computed, toRefs } from 'vue'
import divBaseNavigator from '../div-base/div-base-navigator.uvue'

// 定义接口类型
interface ImageListData {
	pageUrls: string[]
	imageUrls: string[]
	background?: string
	marginBottomSpacing: number
	marginTopSpacing: number
	marginLeftSpacing: number
	marginRightSpacing: number
	paddingBottomSpacing: number
	paddingTopSpacing: number
	paddingLeftSpacing: number
	paddingRightSpacing: number
	canLongPress?: number
	[key: string]: any
}

interface Props {
	modelValue: ImageListData | null
}

// 接收 props
const props = withDefaults(defineProps<Props>(), {
	modelValue: () => ({
		pageUrls: [],
		imageUrls: [],
		marginBottomSpacing: 0,
		marginTopSpacing: 0,
		marginLeftSpacing: 0,
		marginRightSpacing: 0,
		paddingBottomSpacing: 0,
		paddingTopSpacing: 0,
		paddingLeftSpacing: 0,
		paddingRightSpacing: 0
	})
})

// 响应式数据
const { modelValue } = toRefs(props)
const newData = ref<ImageListData>(modelValue.value || {
	pageUrls: [],
	imageUrls: [],
	marginBottomSpacing: 0,
	marginTopSpacing: 0,
	marginLeftSpacing: 0,
	marginRightSpacing: 0,
	paddingBottomSpacing: 0,
	paddingTopSpacing: 0,
	paddingLeftSpacing: 0,
	paddingRightSpacing: 0
})

// 计算属性
</script>