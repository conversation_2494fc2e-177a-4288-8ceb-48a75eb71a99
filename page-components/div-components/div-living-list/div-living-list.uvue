<template>
  <!-- 直播列表组件 -->
  <view :style="{
      marginBottom: `${newData.marginBottomSpacing * 2}rpx`,
      marginTop: `${newData.marginTopSpacing * 2}rpx`,
      marginLeft: `${newData.marginLeftSpacing * 2}rpx`,
      marginRight: `${newData.marginRightSpacing * 2}rpx`,
    }" :class="
      newData.background &&
      newData.background.length > 0 &&
      newData.background != undefined
        ? newData.background
        : ''
    ">
    <view :style="{
        backgroundColor: newData.background,
        backgroundSize: 'contain',
        paddingBottom: `${newData.paddingBottomSpacing}px`,
        paddingTop: `${newData.paddingTopSpacing}px`,
        paddingLeft: `${newData.paddingLeftSpacing}px`,
        paddingRight: `${newData.paddingRightSpacing}px`,
        backgroundImage: 'url(' + newData.bgImage + ')',
        backgroundRepeat: 'repeat',
        minHeight: '100%',
      }">
      <!-- <live-item :roomList="roomLists" :loading="loading" :loadmore="loadmore" /> -->
    </view>
  </view>
</template>

<script setup lang="uts">
  import liveItem from "../div-living-list/live-item.uvue"
  import api from 'utils/api'
  // import { EventBus } from '@/utils/eventBus.js'
  import { useGlobalDataStore } from '@/stores/globalData'

  // 定义类型接口
  interface RoomItem {
    roomid ?: string
    roomId ?: string
    name : string
    coverImg : string
    liveStatus : string
    closeReplay ?: string
    goods ?: {
      coverImgUrl ?: string
      [key : string] : any
    }[]
    [key : string] : any
  }

  interface ComponentData {
    background ?: string
    bgImage ?: string
    marginBottomSpacing : number
    marginTopSpacing : number
    marginLeftSpacing : number
    marginRightSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
  }

  interface PageInfo {
    start : number
  }

  // Props 定义
  interface Props {
    modelValue : ComponentData | null
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      marginBottomSpacing: 0,
      marginTopSpacing: 0,
      marginLeftSpacing: 0,
      marginRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0
    })
  })

  // 获取全局数据store
  const globalDataStore = useGlobalDataStore()

  // 响应式数据
  const newData = ref<ComponentData>(props.modelValue || {
    marginBottomSpacing: 0,
    marginTopSpacing: 0,
    marginLeftSpacing: 0,
    marginRightSpacing: 0,
    paddingBottomSpacing: 0,
    paddingTopSpacing: 0,
    paddingLeftSpacing: 0,
    paddingRightSpacing: 0
  })

  const theme = ref(globalDataStore.theme)
  const page = reactive<PageInfo>({
    start: 1
  })
  const startTag = ref<number>(1)
  const loading = ref<boolean>(true)
  const loadmore = ref<boolean>(false)
  const canLoad = ref<boolean>(false)
  const page_title = ref<string>('直播大厅')
  const current = ref<number>(1)
  const total = ref<string>('')
  const roomLists = ref<RoomItem[]>([])

  // 方法定义
  const scrolltolower = () => {
    if (roomLists.value.length >= parseInt(total.value)) {
      loadmore.value = false
    } else {
      loadmore.value = true
      current.value = current.value + 1
      if (loadmore.value) {
        getData()
      }
    }
  }

  const refresh = () => {
    getData()
  }

  const getData = () => {
    if (current.value == 1) {
      roomLists.value = []
    }

    api.liveRoomInfoList({
      'liveStatus': '103,102,101',
      current: current.value,
      size: 10
    }).then((res : any) => {
      // 直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
      if (res.data && res.data.records && res.data.records.length > 0) {
        total.value = res.data.total
        roomLists.value = roomLists.value.concat(res.data.records)
      }
      if (roomLists.value.length >= res.data.total) {
        loadmore.value = false
      }
      loading.value = false
      uni.hideLoading()
    }).catch((e : any) => {
      canLoad.value = true
      uni.hideLoading()
    })
  }

  // 生命周期
  onMounted(() => {
    refresh()
    // EventBus.$on("pageFresh", () => {
    //   refresh()
    // })
    // EventBus.$on("divGoodsGroupsReachBottom", () => {
    //   scrolltolower()
    // })
  })

  onUnmounted(() => {
    // EventBus.$off("pageFresh")
    // EventBus.$off("divGoodsGroupsReachBottom")
  })

  // 监听 props 变化
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      newData.value = newValue
    }
  }, { deep: true, immediate: true })
</script>