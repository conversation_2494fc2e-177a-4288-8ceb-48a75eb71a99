<template>
  <!-- 导航按钮组件 -->
  <view :style="containerMarginStyles"
    :class="newData.background && newData.background.length > 0 && newData.background != undefined ? newData.background : ''">
    <view :style="backgroundStyles">
      <swiper :next-margin="swiperList && swiperList.length > 1 ? newData.nextMargin * multipleView : 0 + 'rpx'"
        class="screen-swiper square-dot"
        :indicator-color="swiperList && swiperList.length > 1 ? newData.indicatorColor || '#CDA185' : ''"
        :indicator-active-color="swiperList && swiperList.length > 1 ? newData.indicatorActiveColor || '#CDA185' : ''"
        indicator-dots :style="swiperHeightStyles">
        <swiper-item v-for="(swiperItem, swiperIndex) in swiperList" :key="swiperIndex">
          <view class="cu-list grid no-border navButton" :class="isPhone ? 'col-' + newData.rowNum : ''"
            v-for="(listItem, listIndex) in swiperItem" :key="listIndex" :style="getListItemStyles(listIndex)">
            <view class="cu-item" v-for="(item, index) in listItem" :key="index" :style="getCuItemStyles(listIndex)">
              <div-base-navigator :pageUrl="item.pageUrl" hover-class="none">
                <image :src="item.imageUrl" :style="imageBorderStyles" class="nav_bt_img" mode="aspectFit"></image>
                <text :style="textStyles">
                  {{ item.navName }}
                </text>
              </div-base-navigator>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script setup lang="uts">
  import divBaseNavigator from '../div-base/div-base-navigator.uvue'
  import { useSystemStore } from '@/stores/system'

  // 定义接口类型
  interface NavButton {
    imageUrl : string
    navName : string
    pageUrl ?: string
    [key : string] : any
  }

  interface NavButtonData {
    rowNum : number
    textColor : string
    pageSpacing : number
    navButtons : NavButton[]
    height : number
    background ?: string
    bgImage ?: string
    marginBottomSpacing : number
    marginTopSpacing : number
    marginLeftSpacing : number
    marginRightSpacing : number
    paddingBottomSpacing : number
    paddingTopSpacing : number
    paddingLeftSpacing : number
    paddingRightSpacing : number
    nextMargin ?: number
    indicatorColor ?: string
    indicatorActiveColor ?: string
    swipeRowNum : number
    fontSize : number
    imgFontSpacing : number
    lineSpacing : number
    borderRadius ?: number
    radiusLeftTop ?: number
    radiusRightTop ?: number
    radiusLeftBottom ?: number
    radiusRightBottom ?: number
    [key : string] : any
  }

  interface Theme {
    backgroundColor : string
    [key : string] : any
  }

  interface Props {
    modelValue : NavButtonData | null
  }

  // 接收 props
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      rowNum: 4,
      textColor: '#333333',
      pageSpacing: 0,
      navButtons: [],
      height: 100,
      marginBottomSpacing: 0,
      marginTopSpacing: 0,
      marginLeftSpacing: 0,
      marginRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingTopSpacing: 0,
      paddingLeftSpacing: 0,
      paddingRightSpacing: 0,
      swipeRowNum: 1,
      fontSize: 12,
      imgFontSpacing: 8,
      lineSpacing: 0
    })
  })

  // 使用 store
  const systemStore = useSystemStore()

  // 响应式数据
  const theme = ref<Theme>({ backgroundColor: 'white' })
  const { modelValue } = toRefs(props)
  const newData = ref<NavButtonData>(modelValue.value || {
    rowNum: 4,
    textColor: '#333333',
    pageSpacing: 0,
    navButtons: [],
    height: 100,
    marginBottomSpacing: 0,
    marginTopSpacing: 0,
    marginLeftSpacing: 0,
    marginRightSpacing: 0,
    paddingBottomSpacing: 0,
    paddingTopSpacing: 0,
    paddingLeftSpacing: 0,
    paddingRightSpacing: 0,
    swipeRowNum: 1,
    fontSize: 12,
    imgFontSpacing: 8,
    lineSpacing: 0
  })

  // 计算属性
  const isPhone = computed(() : boolean => systemStore.isPhone)
  const multipleView = computed(() : number => systemStore.multipleView)

  const swiperSlideLength = computed(() : number => {
    return Math.ceil((newData.value.navButtons.length / newData.value.rowNum) * (newData.value.swipeRowNum || 1))
  })

  const swiperList = computed(() : NavButton[][][] => {
    const threeDArray : NavButton[][][] = []
    let pageIndex = 0

    // pad版本样式写死
    if (!isPhone.value) {
      newData.value.swipeRowNum = 1
      newData.value.rowNum = 10
    } else {
      newData.value.swipeRowNum = newData.value.swipeRowNum || 1
    }

    const navButtons = newData.value.navButtons || []
    for (let i = 0; i < navButtons.length; i += newData.value.swipeRowNum * newData.value.rowNum) {
      if (!threeDArray[pageIndex]) {
        threeDArray[pageIndex] = []
      }
      for (let j = i; j < i + newData.value.swipeRowNum * newData.value.rowNum; j += newData.value.rowNum) {
        const row = navButtons.slice(j, j + newData.value.rowNum)
        if (row.length > 0) {
          threeDArray[pageIndex].push(row)
        }
      }
      pageIndex++
    }
    return threeDArray
  })

  const containerMarginStyles = computed(() => {
    return {
      marginBottom: `${newData.value.marginBottomSpacing * multipleView.value}rpx`,
      marginTop: `${newData.value.marginTopSpacing * multipleView.value}rpx`,
      marginLeft: `${newData.value.marginLeftSpacing * multipleView.value}rpx`,
      marginRight: `${newData.value.marginRightSpacing * multipleView.value}rpx`
    }
  })

  const backgroundStyles = computed(() => {
    return {
      backgroundColor: newData.value.background,
      backgroundSize: '100% 100%',
      backgroundImage: `url(${newData.value.bgImage})`,
      paddingBottom: `${newData.value.paddingBottomSpacing}px`,
      paddingTop: `${newData.value.paddingTopSpacing}px`,
      paddingLeft: `${newData.value.paddingLeftSpacing}px`,
      paddingRight: `${newData.value.paddingRightSpacing}px`
    }
  })

  const swiperHeightStyles = computed(() => {
    const height = newData.value.swipeRowNum > 1
      ? (74 + Number(newData.value.fontSize * multipleView.value) + Number(newData.value.imgFontSpacing * multipleView.value)) * 2 + 48
      : (isPhone.value ? 44 : 20) + Number(newData.value.fontSize * multipleView.value) + Number(newData.value.imgFontSpacing * multipleView.value) + 48

    return {
      height: `${height}rpx`
    }
  })

  const imageBorderStyles = computed(() => {
    return {
      borderTopLeftRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusLeftTop * multipleView.value + 'rpx' : '0rpx'}`,
      borderTopRightRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusRightTop * multipleView.value + 'rpx' : '0rpx'}`,
      borderBottomLeftRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusLeftBottom * multipleView.value + 'rpx' : '0rpx'}`,
      borderBottomRightRadius: `${newData.value.borderRadius == 1 ? newData.value.radiusRightBottom * multipleView.value + 'rpx' : '0rpx'}`
    }
  })

  const textStyles = computed(() => {
    return {
      marginTop: `${newData.value.imgFontSpacing * multipleView.value}rpx`,
      fontSize: `${newData.value.fontSize * multipleView.value}rpx`,
      color: `${newData.value.textColor}`
    }
  })

  // 方法
  const getListItemStyles = (listIndex : number) => {
    return {
      justifyContent: 'space-between',
      backgroundColor: 'transparent',
      padding: 0,
      marginTop: listIndex > 0 ? `${newData.value.lineSpacing * multipleView.value}rpx` : '0'
    }
  }

  const getCuItemStyles = (listIndex : number) => {
    return {
      backgroundColor: 'transparent',
      padding: 0,
      marginTop: listIndex > 0 ? `${newData.value.lineSpacing * multipleView.value}rpx` : '0'
    }
  }

  const jumpPage = (page : string | undefined) : void => {
    if (page) {
      uni.navigateTo({
        url: page
      })
    }
  }
</script>

<style scoped lang="scss">
  /* 导航 */
  .nav_bt_img {
    width: 74rpx !important;
    height: 74rpx !important;
    margin: 0 auto;
  }

  @media screen and (min-width: 549px) {
    .nav_bt_img {
      width: 46rpx !important;
      height: 46rpx !important;
    }
  }

  .navButton {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .cu-list+.cu-list {
    margin-top: 0;
  }

  .screen-swiper {
    min-height: 30rpx;
  }

  .text-center {
    text-align: center;
  }

  .cu-list.grid.no-border {
    background-color: #1cbbb4;
    flex-direction: row;
  }
</style>