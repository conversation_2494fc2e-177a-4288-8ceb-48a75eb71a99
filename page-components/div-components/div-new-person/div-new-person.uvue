<template>
  <view class="newPerson-page">
    <view class="main-box" :style="{ border: `2rpx solid ${setData.borderColor}` }">
      <view class="head-outbox">
        <view class="head-box">
          <view>
            <text :style="[titleStyle]">{{ setData.title }}</text>
            <text :style="[subTitleStyle]">{{ setData.subTitle }}</text>
          </view>
          <view class="gz" @click="openRulePopup">规则</view>
        </view>
        <image v-if="setData.titleBg" class="tu" :src="setData.titleBg" />
      </view>
      <view class="body-outbox">
        <view class="core-outbox">
          <view class="core-box" v-for="(u, i) in giveList" :key="i">
            <view class="cell-outbox" v-for="(u2, i2) in u.details" :key="i2">
              <view class="cell-box">
                <view>
                  <view :style="[priceStyle]" v-if="'01' == u2.groupId">
                    {{ u2.amount }}
                  </view>
                  <block v-else>
                    <view :style="[priceStyle]" v-if="'A' != u2.groupId && '04' == u2.useType">
                      {{ discountsConvert(u2.amount) }}折
                    </view>
                    <view class="jine" v-else>
                      <view :style="[symbolStyle]">￥</view>
                      <view :style="[priceStyle]">{{ u2.amount }}</view>
                    </view>
                  </block>
                </view>
                <view :style="[nameStyle]">{{ u2.couponName }}</view>
                <view :style="[nameStyle2]">{{ u.condAmount }}门槛</view>
              </view>
              <image v-if="setData.ticketBg" class="tu" :src="setData.ticketBg" />
              <image v-if="i < setData.progressVal" class="tu" :src="setData.noTicketBg" />
            </view>
            <image v-if="setData.divideBg && i + 1 != giveList.length" class="jiange" :src="setData.divideBg" />
          </view>
        </view>
        <view class="tiao-outbox" :style="[progressStyle]">
          <view v-for="(u, i) in giveList" :key="i" class="tiao-box">
            <view class="tiao" :style="{
                backgroundColor: setData.miniProBgColor,
                width: `${u.width * 2}rpx`,
                visibility: i == setData.progressVal ? 'visible' : 'hidden',
              }"></view>
            <image class="tu" :src="setData.noMarkBg" v-if="i < setData.progressVal" />
            <image class="tu" :src="setData.markBg" v-else />
            <view class="wen" :style="{ opacity: i < setData.progressVal ? 0.7 : 1 }">
              {{ u.name }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  // import { accMul } from "utils/numberUtil.js"
  import api from "@/utils/api.uts"

  // 定义类型接口
  interface CouponDetail {
    groupId : string
    amount : number
    useType : string
    couponName : string
  }

  interface GiveItem {
    details : CouponDetail[]
    condAmount : number
    name : string
    width : number
  }

  interface ComponentData {
    title : string
    subTitle : string
    borderColor : string
    titleBg ?: string
    ticketBg ?: string
    noTicketBg ?: string
    divideBg ?: string
    noMarkBg : string
    markBg : string
    miniProBgColor : string
    titleSize : number
    titleColor : string
    subTitleSize : number
    subTitleColor : string
    symbolSize : number
    symbolColor : string
    priceSize : number
    priceHeight : number
    priceColor : string
    nameSize : number
    nameHeight : number
    nameColor : string
    nameMargin : number
    nameMargin2 : number
    giveWidth : number
    bigProBgColor : string
    progressVal : number
    billno : string
    giveList : GiveItem[]
  }

  interface UserInfo {
    erpCid : string
    [key : string] : any
  }

  interface StyleObject {
    [key : string] : string
  }

  // Props 定义
  interface Props {
    modelValue : ComponentData | null
  }

  const props = withDefaults(defineProps<Props>(), {
    modelValue: null
  })

  // Emits 定义
  const emit = defineEmits<{
    openRulePopup : [value: string]
  }>()

  // 响应式数据
  const setData = ref<ComponentData | null>(null)
  const titleStyle = ref<StyleObject | null>(null)
  const subTitleStyle = ref<StyleObject | null>(null)
  const symbolStyle = ref<StyleObject | null>(null)
  const priceStyle = ref<StyleObject | null>(null)
  const nameStyle = ref<StyleObject | null>(null)
  const nameStyle2 = ref<StyleObject | null>(null)
  const progressStyle = ref<StyleObject | null>(null)
  const giveList = ref<GiveItem[]>([])

  // 过滤器函数
  const discountsConvert = (val : number) : number => {
    // return accMul(val, 10)
    return 0
  }

  // 方法定义
  const initStyle = () => {
    if (!props.modelValue) return

    setData.value = props.modelValue
    const {
      titleSize,
      titleColor,
      subTitleSize,
      subTitleColor,
      symbolSize,
      symbolColor,
      priceSize,
      priceHeight,
      priceColor,
      nameSize,
      nameHeight,
      nameColor,
      nameMargin,
      nameMargin2,
      giveWidth,
      bigProBgColor,
      giveList: giveListData,
    } = setData.value

    titleStyle.value = {
      fontSize: `${titleSize * 2}rpx`,
      color: titleColor,
      marginRight: "10rpx",
    }
    subTitleStyle.value = {
      fontSize: `${subTitleSize * 2}rpx`,
      color: subTitleColor,
    }
    symbolStyle.value = {
      fontSize: `${symbolSize * 2}rpx`,
      color: symbolColor,
    }
    priceStyle.value = {
      fontSize: `${priceSize * 2}rpx`,
      lineHeight: `${priceHeight * 2}rpx`,
      color: priceColor,
    }
    nameStyle.value = {
      fontSize: `${nameSize * 2}rpx`,
      lineHeight: `${nameHeight * 2}rpx`,
      color: nameColor,
      marginTop: `${nameMargin * 2}rpx`,
    }
    nameStyle2.value = {
      fontSize: `${nameSize * 2}rpx`,
      lineHeight: `${nameHeight * 2}rpx`,
      color: nameColor,
      marginTop: `${nameMargin2 * 2}rpx`,
    }
    progressStyle.value = {
      width: `${giveWidth * 2}rpx`,
      backgroundColor: bigProBgColor,
    }
    giveList.value = giveListData
  }

  const getNewPerson = () => {
    if (!setData.value) return

    const userInfo : UserInfo = uni.getStorageSync("user_info")
    const params = {
      channel: "SONGSHU",
      custId: userInfo.erpCid,
      keys: setData.value.billno,
    }

    api.newPersonApi(params).then((res : any) => {
      if (res.data && setData.value) {
        const { tag } = res.data.list[0]
        setData.value.progressVal = tag
      } else if (setData.value) {
        setData.value.progressVal = 0
      }
    })
  }

  const openRulePopup = () => {
    emit("openRulePopup", "")
  }

  // 监听 props 变化
  watch(() => props.modelValue, () => {
    initStyle()
  }, { deep: true, immediate: true })

  // 生命周期
  onMounted(() => {
    getNewPerson()
  })
</script>

<style lang="scss" scoped>
  .newPerson-page {
    position: relative;

    .main-box {
      margin: auto;
      padding: 12rpx;
      width: 710rpx;
      height: 372rpx;
      background: #ffffff;
      opacity: 0.77;
      border-radius: 20rpx;

      .head-outbox {
        position: relative;

        .head-box {
          width: 686rpx;
          height: 80rpx;
          padding: 0 26rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .gz {
            font-size: 28rpx;
            color: #343e81;
          }
        }

        .tu {
          position: absolute;
          z-index: -1;
          top: 0;
          left: 0;
          width: 686rpx;
          height: 80rpx;
        }
      }

      .body-outbox {
        margin-top: 18rpx;
        width: 686rpx;
        overflow-x: auto;
        overflow-y: hidden;

        .core-outbox {
          display: flex;
          align-items: center;

          .core-box {
            display: flex;
            align-items: center;

            .cell-outbox {
              position: relative;
              margin: auto 20rpx;
              width: 180rpx;
              height: 184rpx;
              display: flex;
              align-items: center;

              .cell-box {
                text-align: center;
                width: 180rpx;
                height: 184rpx;
                padding-top: 12rpx;

                .jine {
                  display: flex;
                  align-items: start;
                  justify-content: center;
                }
              }

              .tu {
                position: absolute;
                z-index: -1;
                top: 0;
                left: 0;
                width: 180rpx;
                height: 184rpx;
              }
            }

            .jiange {
              margin: auto 20rpx;
              width: 36rpx;
              height: 126rpx;
            }
          }
        }

        .tiao-outbox {
          height: 24rpx;
          margin-top: 24rpx;
          border-radius: 12rpx;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .tiao-box {
            position: relative;

            .tiao {
              margin: auto 4rpx;
              height: 16rpx;
              border-radius: 8rpx;
            }

            .tu {
              position: absolute;
              width: 128rpx;
              height: 36rpx;
              top: -16rpx;
              left: 50%;
              transform: translateX(-50%);
            }

            .wen {
              position: absolute;
              width: 66rpx;
              height: 36rpx;
              line-height: normal;
              top: -8rpx;
              left: 50%;
              transform: translateX(-50%);
              color: #fff;
              font-size: 22rpx;
            }
          }
        }
      }
    }
  }
</style>