<template>
  <!-- 搜索组件 -->
  <view class="height100" :class="containerClasses" :style="containerStyles">
    <view class="height100" :style="wrapperStyles">
      <view class="search content-s height100">
        <!-- 标题连接 -->
        <view class="height100" v-if="newData.showLeftTitle == 1" :style="leftTitleTextStyles">
          {{ newData.leftTitle }}
        </view>
        <!-- 标题图片 -->
        <view class="height100" v-else-if="newData.showLeftTitle == 2" :style="leftTitleImageContainerStyles">
          <image :style="leftTitleImageStyles" mode="aspectFit" :src="newData.LeftIcon" />
        </view>
        <!-- 搜索框 -->
        <view class="search-field round height100" :style="searchFieldStyles">
          <text v-if="!isStatusBar" class="cuIcon-search" :style="searchIconStyles" />
          <view v-else class="height100" :style="statusBarScanContainerStyles">
            <text @click="scanCode" class="cuIcon-scan font-xxsm pr-xs pl-xs" />
            <image :style="dividerImageStyles" mode="aspectFit" src="https://img.songlei.com/share/scan.png" />
          </view>
          <view @click="navTo('/pages/base/search/index')" class="response height100" :style="searchPlaceholderStyles">
            <swiper class="height100" :style="swiperStyles" vertical autoplay circular :interval="3000">
              <template v-for="(item, index) in newData.texts" :key="index">
                <swiper-item v-if="item != '[object Object]'" @click="placeholderText(item)" :style="swiperItemStyles">
                  <view class="overflow-1 text-df" :style="placeholderTextStyles">
                    {{ item }}
                  </view>
                </swiper-item>
              </template>
            </swiper>
          </view>
          <view class="height100" :style="spacerStyles" />
          <view @click="cameraImages" class="height100" :style="cameraButtonContainerStyles">
            <image v-if="isStatusBar" :style="dividerImageStyles" mode="aspectFit"
              src="https://img.songlei.com/share/scan.png" />
            <text class="cuIcon-camera font-xxsm pr-xs pl-xs" />
            <image v-if="!isStatusBar" :style="dividerImageStyles" mode="aspectFit"
              src="https://img.songlei.com/share/scan.png" />
            <text v-if="!isStatusBar" @click="scanCode" class="cuIcon-scan padding-right-sm padding-left-xs" />
          </view>
        </view>
        <!-- #ifdef APP -->
        <view class="navIcon" :style="messageIconContainerStyles" v-if="newData.showMessage == 1"
          @click="gotoMessageList">
          <view :style="messageIconStyles">
            <text class="cuIcon-message text-xdf" />
            <view :style="messageTextStyles">消息</view>
          </view>
          <view v-if="msgCount" class="total" :style="messageBadgeStyles">
            {{ msgCount > 0 ? (msgCount > 99 ? '99+' : msgCount) : '' }}
          </view>
        </view>
        <!-- #endif -->
      </view>
    </view>
    <!-- 		<uni-popup ref="perpopup" type="center" :mask-click="false">
			<view class="permissions_box">
				当您使用APP时，扫一扫查看相关商品功能需要授权相机权限，获取本地图片需要获取访问设备照片权限，不授权上述权限，不影响APP其他功能使用。
			</view>
		</uni-popup>
		<camera-model :showPopup.sync="showPopup" /> -->
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
  import api from '@/utils/api'
  // import { scanH5ToMaPage } from '@/utils/util.uts'
  // import CameraModel from '@/components/camera-model/index'
  import { useSystemStore } from '@/stores/system'

  // 接口定义
  interface SearchData {
    background : string
    bgImage ?: string
    color : string
    placeholder : string
    radius : number
    textColor : string
    textPosition : string
    texts : any[]
    borderSize ?: number
    borderColor ?: string
    searchHeight ?: number
    marginBottomSpacing ?: number
    marginTopSpacing ?: number
    marginLeftSpacing ?: number
    marginRightSpacing ?: number
    paddingBottomSpacing ?: number
    paddingTopSpacing ?: number
    paddingLeftSpacing ?: number
    paddingRightSpacing ?: number
    showLeftTitle ?: number
    leftTitleWidth ?: number
    leftTitleColor ?: string
    leftTitleSize ?: number
    leftTitle ?: string
    LeftIcon ?: string
    showMessage ?: number
    messageColor ?: string
    messageNumColor ?: string
    messageDotColor ?: string
  }

  // Props 定义
  const props = withDefaults(defineProps<{
    value ?: SearchData | null
    isStatusBar ?: boolean
    isBack ?: boolean
  }>(), {
    value: () => ({
      background: '#efeff4',
      color: '#ffffff',
      placeholder: '请输入关键字',
      radius: 38,
      textColor: '#999999',
      textPosition: 'center',
      texts: []
    }),
    isStatusBar: false,
    isBack: false
  })

  // Store
  const systemStore = useSystemStore()
  const { HeightBar, CustomBar, windowWidth, menuWidth, leftMenuWidth, multipleView } = systemStore


  // 全局应用实例
  const getAppGlobalData = () => {
    return { theme: 'default' }
  }

  // #ifdef APP-PLUS
  let mpaasScanModule : any = null
  try {
    mpaasScanModule = uni.requireNativePlugin('Mpaas-Scan-Module')
  } catch (e) {
    console.log('Mpaas-Scan-Module not available')
  }
  // #endif

  // 响应式数据
  const theme = ref(getAppGlobalData().theme)
  const newData = ref<SearchData>({ ...props.value } as SearchData)
  const searchText = ref('')
  const msgCount = ref(0)
  const CanBack = ref(true)
  const showPopup = ref(false)
  const perpopup = ref()

  // 基础计算属性
  const computedHeight = computed(() => {
    return props.isStatusBar ? `${HeightBar}px` : 'auto'
  })

  const typeHeight = computed(() => {
    let height = HeightBar
    // #ifdef APP
    height = +height - +(newData.value.borderSize || 0) * 2 - 4
    // #endif
    return height
  })

  // 样式计算属性
  const containerClasses = computed(() => {
    return props.isStatusBar ? '' : (newData.value.background && newData.value.background.indexOf('bg-') != -1 ? newData.value.background : '')
  })

  const containerStyles = computed(() => ({
    marginBottom: `${props.isStatusBar ? 0 : Number(newData.value.marginBottomSpacing || 0) * multipleView}rpx`,
    marginTop: `${props.isStatusBar ? 0 : (newData.value.marginTopSpacing || 0) * multipleView}rpx`,
    marginLeft: `${(newData.value.marginLeftSpacing || 0) * multipleView}rpx`,
    marginRight: `${(newData.value.marginRightSpacing || 0) * multipleView}rpx`,
    height: computedHeight.value,
    width: props.isStatusBar
      ? CanBack.value && props.isBack
        ? `${windowWidth - menuWidth - leftMenuWidth - 17}px`
        : !CanBack.value && props.isBack
          ? `${windowWidth - menuWidth - leftMenuWidth / 2 - 17}px`
          : `${windowWidth - menuWidth - 10}px`
      : '100%'
  }))

  const wrapperStyles = computed(() => ({
    backgroundColor: props.isStatusBar ? '' : newData.value.background,
    backgroundImage: props.isStatusBar ? '' : `url(${newData.value.bgImage || ''})`,
    backgroundSize: 'cover',
    overflow: 'hidden',
    paddingBottom: `${props.isStatusBar ? 0 : (newData.value.paddingBottomSpacing || 0) * multipleView}rpx`,
    paddingTop: `${props.isStatusBar ? 0 : (newData.value.paddingTopSpacing || 0) * multipleView}rpx`,
    paddingLeft: `${(newData.value.paddingLeftSpacing || 0) * multipleView}rpx`,
    paddingRight: `${(newData.value.paddingRightSpacing || 0) * multipleView}rpx`
  }))

  const leftTitleTextStyles = computed(() => ({
    minWidth: `${(newData.value.leftTitleWidth || 0) * 2}rpx`,
    color: newData.value.leftTitleColor || '#333',
    fontSize: `${(newData.value.leftTitleSize || 14) * multipleView}rpx`
  }))

  const leftTitleImageContainerStyles = computed(() => ({
    display: 'flex',
    alignItems: 'center'
  }))

  const leftTitleImageStyles = computed(() => ({
    maxHeight: computedHeight.value,
    width: `${(newData.value.leftTitleWidth || 0) * multipleView}rpx`
  }))

  const searchFieldStyles = computed(() => ({
    backgroundColor: newData.value.color,
    borderRadius: `${(newData.value.radius || 0) * 2}rpx`,
    color: newData.value.textColor,
    height: `${(props.isStatusBar ? typeHeight.value : (newData.value.searchHeight || 60)) * multipleView}rpx`,
    maxHeight: '100%',
    border: `${(newData.value.borderSize || 1) * 2}rpx solid ${newData.value.borderColor || '#e5e5e5'}`
  }))

  const searchIconStyles = computed(() => ({
    padding: '0rpx 16rpx 0rpx 20rpx'
  }))

  const statusBarScanContainerStyles = computed(() => ({
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
  }))

  const dividerImageStyles = computed(() => ({
    maxHeight: '48rpx',
    width: '4rpx'
  }))

  const searchPlaceholderStyles = computed(() => ({
    color: newData.value.textColor,
    textAlign: newData.value.textPosition == 'center' ? 'center' : 'left',
    marginLeft: newData.value.textPosition == 'center' ? '-25px' : '8rpx',
    flex: '1'
  }))

  const swiperStyles = computed(() => ({
    maxHeight: computedHeight.value,
  }))

  const swiperItemStyles = computed(() => ({
    display: 'flex',
    height: computedHeight.value,
    'justify-content': 'center',
  }))

  const placeholderTextStyles = computed(() => ({
    color: '#888888'
  }))

  const spacerStyles = computed(() => ({
    display: 'flex',
    alignItems: 'center'
  }))

  const cameraButtonContainerStyles = computed(() => ({
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
  }))

  const messageIconContainerStyles = computed(() => ({
    paddingLeft: '20rpx'
  }))

  const messageIconStyles = computed(() => ({
    color: newData.value.messageColor || '#ffffff'
  }))

  const messageTextStyles = computed(() => ({
    fontSize: '18rpx'
  }))

  const messageBadgeStyles = computed(() => ({
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '32rpx',
    height: '32rpx',
    color: newData.value.messageNumColor || '#ffffff',
    backgroundColor: newData.value.messageDotColor || '#ff0000'
  }))

  // 监听 props 变化
  watch(() => props.value, (newVal) => {
    if (newVal) {
      newData.value = { ...newVal }
    }
  }, { deep: true, immediate: true })

  // 方法定义
  const scanCode = () => {
    // #ifdef MP-WEIXIN
    uni.scanCode({
      scanType: ['barCode', 'qrCode'],
      success: (res : any) => {
        if (res.result) {
          const code = res.result
          console.log('code', code)
          if (code) {
            if (code.indexOf('https://') > -1) {
              util.scanH5ToMaPage(code, api.qrcodequery)
              return
            }
            api.goodsGetCode(code)
              .then((res : any) => {
                console.log('res', res)
                let goodsList = res.data
                if (res.ok) {
                  if (goodsList) {
                    let data = JSON.stringify(goodsList)
                    if (goodsList.length == '1') {
                      uni.navigateTo({
                        url: `/pages/goods/goods-detail/index?id=${goodsList[0].id}`
                      })
                      return
                    }
                    uni.navigateTo({
                      url: `/pages/goods/goods-code-list/index?goodsList=${encodeURIComponent(data)}`
                    })
                  } else {
                    uni.showToast({
                      title: '该商品不存在',
                      duration: 2000
                    })
                  }
                }
              })
              .catch((res : any) => {
                console.log('=res=', res)
              })
          }
        } else {
          console.log('请重新扫描')
          return false
        }
      },
      fail: (res : any) => {
        console.log('未识别到二维码')
      }
    })
    // #endif

    // #ifdef APP-PLUS
    var platform = uni.getSystemInfoSync().platform
    if (platform == 'android') {
      plus.android.checkPermission(
        'android.permission.CAMERA',
        (granted : any) => {
          if (granted.checkResult == -1) {
            perpopup.value?.open('top')
            plus.android.requestPermissions(['android.permission.CAMERA', 'android.permission.WRITE_EXTERNAL_STORAGE'], (e : any) => {
              if (e.granted.length > 0) {
                perpopup.value?.close()
                appScan()
              }
              if (e.deniedAlways.length > 0) {
                uni.showModal({
                  title: '提示',
                  content: '扫码和识别本地图片权限被拒绝，是否前往开启权限',
                  success: (res : any) => {
                    if (res.confirm) {
                      var main = plus.android.runtimeMainActivity()
                      var Intent = plus.android.importClass('android.content.Intent')
                      var mIntent = new Intent('android.settings.APPLICATION_SETTINGS')
                      main.startActivity(mIntent)
                      perpopup.value?.close()
                    } else if (res.cancel) {
                      console.log('用户点击取消')
                      perpopup.value?.close()
                    }
                  }
                })
              }
              if (e.deniedPresent.length > 0) {
                plus.android.requestPermissions(['android.permission.CAMERA'])
                perpopup.value?.close()
              }
            })
          } else {
            appScan()
          }
        },
        (error : any) => {
          console.error('Error checking permission:', error.message)
        }
      )
    } else {
      appScan()
    }
    // #endif
  }

  const appScan = () => {
    // #ifdef APP-PLUS
    if (!mpaasScanModule) {
      uni.showToast({
        title: '扫码模块不可用',
        icon: 'none'
      })
      return
    }

    mpaasScanModule.mpaasScan(
      {
        scanType: ['qrCode', 'barCode'],
        hideAlbum: false
      },
      (res : any) => {
        if (res.resp_code == 1000 && res.resp_message == 'success') {
          const code = res.resp_result
          if (code) {
            if (code.indexOf('https://') > -1) {
              util.scanH5ToMaPage(code, api.qrcodequery)
              return
            }
            api.goodsGetCode(code)
              .then((res : any) => {
                console.log('res', res)
                let goodsList = res.data
                if (res.ok) {
                  if (goodsList) {
                    let data = JSON.stringify(goodsList)
                    if (goodsList.length == '1') {
                      uni.navigateTo({
                        url: `/pages/goods/goods-detail/index?id=${goodsList[0].id}`
                      })
                      return
                    }
                    uni.navigateTo({
                      url: `/pages/goods/goods-code-list/index?goodsList=${encodeURIComponent(data)}`
                    })
                  } else {
                    uni.showToast({
                      title: '该商品不存在',
                      icon: 'none',
                      duration: 2000
                    })
                  }
                }
              })
              .catch((res : any) => {
                console.log('=res=', res)
              })
          } else {
            uni.showToast({
              title: '未识别到内容',
              icon: 'none',
              duration: 2000
            })
          }
        }
      }
    )
    // #endif
  }

  const gotoMessageList = () => {
    uni.navigateTo({
      url: '/pages/message/list/index'
    })
  }

  const placeholderText = (item : any) => {
    let searchText = item
    uni.setStorageSync('searchPlaceholderText', searchText)
  }

  const getHitSearchList = () => {
    let hitList : string[] = []
    const searchHistory = uni.getStorageSync('searchHistory') || []

    if (searchHistory && searchHistory.length) {
      if (typeof searchHistory[0]?.name === 'string' && searchHistory[0].name !== '[object Object]') {
        hitList.push(searchHistory[0].name)
      }
      if (typeof searchHistory[1]?.name === 'string' && searchHistory[1].name !== '[object Object]') {
        hitList.push(searchHistory[1].name)
      }
    }

    if (props.value?.texts && props.value.texts.length) {
      props.value.texts.forEach((item : any) => {
        if (typeof item.name === 'string' && item.name !== '[object Object]') {
          hitList.push(item.name)
        }
      })
    }

    api.HitSearchList({
      hit: 10
    })
      .then((res : any) => {
        let addHitList = res.data.result
        let searchPlaceholderRequestid = res.data.request_id
        uni.setStorageSync('searchPlaceholderRequestid', searchPlaceholderRequestid)
        if (addHitList && addHitList.length) {
          addHitList.forEach((item : any) => {
            if (typeof item.name === 'string' && item.name !== '[object Object]') {
              hitList.push(item.name)
            }
          })
        }
        newData.value.texts = Array.from(new Set(hitList))
      })
      .catch((e : any) => {
        console.error(e)
        newData.value.texts = Array.from(new Set(hitList))
      })
  }

  const navTo = (url : string) => {
    uni.navigateTo({
      url
    })
  }

  const cameraImages = () => {
    showPopup.value = true
  }

  const cancelBtn = () => {
    showPopup.value = false
  }

  // 生命周期
  onMounted(() => {
    getHitSearchList()

    // #ifdef MP-WEIXIN || MP-QQ
    let pages = getCurrentPages()
    if (pages.length <= 1 || pages[pages.length - 1].route == 'pages/login/index') {
      CanBack.value = false
    } else {
      CanBack.value = true
    }
    // #endif

    console.log('获取高度数据', HeightBar)
  })

  onUnmounted(() => {
    uni.$off('msg_unread')
  })

  // 暴露给父组件的方法和数据
  defineExpose({
    scanCode,
    cameraImages,
    cancelBtn,
    getHitSearchList
  })
</script>

<style scoped lang="scss">
  .navIcon {
    display: flex;
    justify-content: space-around;
    text-align: center;
    align-content: center;
    font-size: 18px;
    color: #fff;

    .total {
      position: relative;
      right: 22rpx;
      height: 22rpx;
      width: 22rpx;
      text-align: center;
      font-size: 22rpx;
      border-radius: 50%;
      background-color: #f22230;
      color: #ffffff;
    }
  }

  .content-s {
    display: flex;
    position: relative;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
  }

  .search-field {
    font-size: 24rpx;
    color: #333333;
    flex: 1;
    display: flex;
    align-items: center;
    flex-direction: row;
  }

  .height100 {
    height: 100%;
  }

  .permissions_box {
    padding: 200rpx 30rpx 50rpx;
    background-color: #fff;
    color: #000000;
  }

  /* 弹出框 */
  .songlei-popup {
    position: fixed;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    top: 0;
    left: 0;
  }

  .popup-box {
    width: 100vw;
    background-color: #fff;
    border-radius: 30rpx 30rpx 0 0;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .popup-item {
    text-align: center;
    color: #000;
    border-bottom: 1rpx solid #f1f1f1;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>