<template>
  <!-- 搜索组件 -->
  <view class="div-search">
    <cu-custom v-if="isStatusBar" :bg-color="newData.background" :bg-image="newData.bgImage" :is-back="isBack"
      :left-title-props="{
				showLeftTitle: newData.showLeftTitle,
				leftTitleColor: newData.leftTitleColor,
				leftTitleSize: newData.leftTitleSize,
				leftTitle: newData.leftTitle,
				leftTitleWidth: newData.leftTitleWidth
			}">
      <template #backText>返回</template>
      <template #marchContent>
        <search :value="newData" :is-status-bar="isStatusBar" :is-back="isBack"></search>
      </template>
    </cu-custom>

    <search v-else v-model="newData" :isStatusBar="isStatusBar"></search>
  </view>
</template>

<script setup lang="uts">
  import { ref, toRefs } from 'vue'
  import search from './div-search.uvue'

  // 定义接口类型
  interface SearchData {
    background : string
    bgImage ?: string
    showLeftTitle ?: number
    leftTitleColor ?: string
    leftTitleSize ?: number
    leftTitle ?: string
    leftTitleWidth ?: number
  }

  interface Props {
    modelValue : SearchData
    isStatusBar : boolean
    isBack : boolean
  }

  // 接收 props
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      background: ''
    }),
    isStatusBar: false,
    isBack: false
  })
  // 响应式数据
  const { modelValue } = toRefs(props)



  const newData = ref<SearchData>(modelValue.value || { background: '' })
</script>