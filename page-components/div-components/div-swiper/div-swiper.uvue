<template>
  <!-- 轮播图组件 -->
  <view class="bg-white">
    <view v-show="newData.swiperType == 'card-swiper'" :class="'bg-' + theme.backgroundColor"></view>

    <view :style="{
			backgroundImage: `url(${newData.bgImage})`,
			backgroundColor: newData.background,
			backgroundSize: '100% auto',
			backgroundRepeat: 'no-repeat',
			padding: `${newData.paddingTopSpacing * 2}rpx ${newData.paddingRightSpacing * 2}rpx ${newData.paddingBottomSpacing * 2}rpx ${newData.paddingLeftSpacing * 2}rpx`
		}">
      <view style="width: 100%" :style="{
					padding: `${newData.swiperType == 'card-swiper' ? '0 28rpx 0 28rpx' : ''}`,
					overflow: 'hidden',
					transform: 'translateY(0)',
					borderTopLeftRadius: `${newData.swiperType == '3D-swiper' ? '' : (newData.borderRadius == 1 ? (newData.radiusLeftTop * 2) + 'rpx' : '0rpx')}`,
					borderTopRightRadius: `${newData.swiperType == '3D-swiper' ? '' : (newData.borderRadius == 1 ? (newData.radiusRightTop * 2) + 'rpx' : '0rpx')}`,
					borderBottomLeftRadius: `${newData.swiperType == '3D-swiper' ? '' : (newData.borderRadius == 1 ? (newData.radiusLeftBottom * 2) + 'rpx' : '0rpx')}`,
					borderBottomRightRadius: `${newData.swiperType == '3D-swiper' ? '' : (newData.borderRadius == 1 ? (newData.radiusRightBottom * 2) + 'rpx' : '0rpx')}`
				}">
        <view v-if="newData.swiperType !== '3D-swiper'">
          <swiper class="screen-swiper"
            :class="newData.dotStyle == 'square-dot' ? 'square-dot' : (newData.dotStyle == 'round-dot' ? 'round-dot' : '')"
            :indicator-dots="true" :circular="true" :autoplay="true" :interval="newData.interval" duration="500"
            @change="cardSwiper" indicator-color="#cccccc" indicator-active-color="#ffffff" :style="{
							height: `${newData.height * 2}rpx`
						}">
            <swiper-item v-for="(item, index) in newData.swiperList" :key="index" :class="cardCur == index ? 'cur' : ''"
              @tap="jumpPage(item.pageUrl)">
              <image :src="$formatImg750(item.imageUrl)" mode="scaleToFill" :style="{
									height: `${newData.height * 2}rpx`
								}"></image>
            </swiper-item>
          </swiper>
        </view>

        <view v-else>
          <special-banner :setData="newData" @getGroupsGoods="getGroupsGoods" :loadmore="loadmore"></special-banner>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  import { ref, computed, watch, onMounted } from 'vue'
  import { gotoPage } from '../div-base/div-page-urls.uts'
  import specialBanner from './swiper3D.uvue'
  import api from '@/utils/api'
  import { useSystemStore } from '@/stores/system'

  // 定义接口类型
  interface SwiperItem {
    imageUrl : string
    pageUrl ?: string
    [key : string] : any
  }

  interface SwiperData {
    swiperType : string
    height : number
    interval : number
    borderRadius : number
    imageSpacing : number
    swiperList : SwiperItem[]
    dotStyle ?: string
    background ?: string
    bgImage ?: string
    paddingTopSpacing : number
    paddingRightSpacing : number
    paddingBottomSpacing : number
    paddingLeftSpacing : number
    radiusLeftTop ?: number
    radiusRightTop ?: number
    radiusLeftBottom ?: number
    radiusRightBottom ?: number
    goodsFromType ?: number
    groupingList ?: string
    goodsList ?: any[]
    goodsStyle ?: number
    goodsPuantity ?: number
    isSwiper ?: boolean
    [key : string] : any
  }

  interface Theme {
    backgroundColor : string
    [key : string] : any
  }

  interface Props {
    modelValue : SwiperData | null
  }

  interface PageData {
    searchCount : boolean
    current : number
    size : number
    ascs : string
    descs : string
  }

  // 接收 props
  const props = withDefaults(defineProps<Props>(), {
    modelValue: () => ({
      swiperType: 'screen-swiper',
      height: 150,
      interval: 3000,
      borderRadius: 0,
      imageSpacing: 0,
      swiperList: [],
      paddingTopSpacing: 0,
      paddingRightSpacing: 0,
      paddingBottomSpacing: 0,
      paddingLeftSpacing: 0
    })
  })

  // 定义 emits
  const emit = defineEmits<{
    getGroupsGoods : [data: any]
  }>()

  // 使用 store
  const systemStore = useSystemStore()

  // 响应式数据
  const theme = ref<Theme>({ backgroundColor: 'white' })
  const newData = ref<SwiperData>({} as SwiperData)
  const cardCur = ref<number>(0)
  const page = ref<PageData>({
    searchCount: false,
    current: 0,
    size: 10,
    ascs: '',
    descs: ''
  })
  const recordCurrentPage = ref<number>(3)
  const loadmore = ref<boolean>(true)
  const unLoadGoods = ref<any[]>([])

  // 计算属性

  // 监听器
  watch(
    () => props.modelValue,
    (newVal : SwiperData | null, oldVal : SwiperData | null) => {
      if (!newVal) return

      console.log('swiper开始数据')
      const newVals = JSON.parse(JSON.stringify(newVal))

      if (newVals.goodsFromType == 1) {
        // 处理商品名称修改
      } else {
        // 处理分组类型
        if (newVals.goodsFromType == 7) {
          const groupsId : string[] = []
          if (newVals.groupingList && Array.isArray(newVals.groupingList)) {
            newVals.groupingList.forEach((item : any) => {
              groupsId.push(item.id)
            })
            newVals.groupingList = groupsId.join(',')
          }
          console.log('------->请求下一页数据')
          getGroupsGoods()
        }
        newVals.goodsList = []
      }

      // 大于20就分页展示
      if (newVals.goodsList && newVals.goodsList.length > 20) {
        unLoadGoods.value = newVals.goodsList
        newVals.goodsList = []
        const subArray = unLoadGoods.value.splice(0, newVals.isSwiper ? 11 : 10)
        if (subArray && subArray.length > 0) {
          subArray.forEach((item : any) => {
            newVals.goodsList.push(item)
          })
        }
      }

      newData.value = newVals
    },
    { immediate: true, deep: true }
  )

  // 生命周期
  onMounted(() => {
    if (props.modelValue) {
      newData.value = { ...props.modelValue }
    }
  })

  // 方法
  const getGroupsGoods = () : void => {
    console.log('this.newData===>', props.modelValue)
    page.value.current++

    if (props.modelValue && props.modelValue.goodsStyle == 2 && props.modelValue.goodsPuantity) {
      page.value.size = props.modelValue.goodsPuantity
    }

    if (!props.modelValue || !props.modelValue.groupingList) return

    api.goodsGroupsGet(
      Object.assign(page.value, {
        goodsGroupIds: props.modelValue.groupingList
      })
    )
      .then((res : any) => {
        const data = res.data?.records || []

        // 记录第一页数据长度
        if (res.data && res.data.current == '1') {
          recordCurrentPage.value = data.length
        }

        console.log(res, data, data.length, '========商品分组======')

        if (res && res.data && res.data.records && res.data.records.length > 0) {
          newData.value.goodsList = (newData.value.goodsList || []).concat(data)
          if (newData.value.goodsList.length === res.data.total) {
            loadmore.value = false
          }
        }

        emit('getGroupsGoods', { data, loadmore: loadmore.value })
      })
      .catch((error : any) => {
        console.error('获取分组商品失败:', error)
      })
  }

  const cardSwiper = (e : any) : void => {
    cardCur.value = e.detail.current
  }

  const jumpPage = (page : string | undefined) : void => {
    if (page) {
      gotoPage(page)
    }
  }
</script>

<style scoped lang="scss">
  .screen-swiper {
    min-height: 90upx !important;
  }
</style>