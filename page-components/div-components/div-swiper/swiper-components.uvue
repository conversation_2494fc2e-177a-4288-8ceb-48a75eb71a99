<template>
  <view class="con-part2-con">
    <swiper class="swiper-tall" :style="{height: `${42+setData.height*2}rpx`}"
      :indicator-dots="setData.dotStyle == '' ? false : true " :indicator-color="'#cccccc'"
      :indicator-active-color="'#ffffff'" :autoplay="true" :interval="setData.interval?setData.interval:3000"
      :duration="setData.duration?setData.duration:1000" :circular="true"
      :previous-margin="(190-setData.leftRightpadding)+'rpx'" :next-margin="(190-setData.leftRightpadding)+'rpx'"
      current='0' @change="swiperChange">
      <swiper-item class="con-part2-con-container" v-for="(item, i) in dataList" :key="i">
        <view
          :class="['slide-image', curIndex === i?'active':(setData.shadow=='0'?'':'bg-shadow'), 'swiper-slide-item']"
          :style="{background:'url('+ item.pic +') center no-repeat',backgroundSize:'100%'}"
          @tap="getBannerDetail(item.id)">
          <!-- 角标配置 -->
          <view class="card-icon" v-if="setData" :style="{
                    top: `${setData.imgTop*2}rpx`,
                    left: `${setData.imgLeft*2}rpx`,
                  }">
            <view class="card-icon">
              <image v-if="item.estimatedPriceVo" class="card-icon-image" mode="widthFix" :src="setData.imageUrls" />
              <view
                v-if="item.estimatedPriceVo&&(item.estimatedPriceVo.totalDiscountPrice&&item.estimatedPriceVo.totalDiscountPrice!='0'&&item.estimatedPriceVo.totalDiscountPrice!='0.0')&setData.ShowPrice==1"
                class="card-price-overlay">
                <view class="text-bold">
                  <format-price :color="'#FFFFFF'" signFontSize="20rpx" smallFontSize="24rpx"
                    :priceFontSize="`${setData.topPriceSize*2}rpx`"
                    :price="Number(item.estimatedPriceVo.totalDiscountPrice)" />
                </view>
              </view>
            </view>
          </view>
          <view class="part2-con-img" :style="{
                    borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
                    borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
                    borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
                    borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
                  }">
            <image :src="item.picUrls[0]" class="img"></image>
          </view>
          <view class="part2-con-txt2">
            <!-- 商品信息 -->
            <view class="goods-info-wrapper">
              <view class="goods-name">{{item.name}}</view>
              <!-- 积分商品 -->
              <view v-if="item.spuType == 3 || item.spuType == 5" class="points-section">
                <text class="text-gray text-xs">积分：</text>
                <text class="text-red text-lg text-bold">{{item.goodsPoints ? item.goodsPoints : 0}}</text>

              </view>
              <!-- 付费商品 -->
              <view v-else class="price-section">
                <view>
                  <view class=" text-gray text-sm">
                    原价
                  </view>
                  <view class="original-price-value">
                    <format-price v-if="item.estimatedPriceVo" styleProps="line-height: 36rpx;" :color="'#AAAAAA'"
                      signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx"
                      :price="Number(item.estimatedPriceVo.originalPrice)" />
                  </view>
                </view>

                <view>
                  <view class=" text-sm text-red">
                    预估到手价
                  </view>
                  <view class="estimated-price-value">
                    <format-price v-if="item.estimatedPriceVo" styleProps="line-height: 36rpx;" :color="'#FF0000'"
                      signFontSize="20rpx" smallFontSize="24rpx" priceFontSize="34rpx"
                      :price="Number(item.estimatedPriceVo.estimatedPrice)" />
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>


</template>

<script setup lang="uts">
  import { ref, computed } from 'vue'
  // import formatPrice from "@/components/format-price/index.uvue"
  import { gotoPage } from "../div-base/div-page-urls.js"

  // 类型定义
  interface EstimatedPriceVo {
    originalPrice : number
    estimatedPrice : number
    totalDiscountPrice ?: string
  }

  interface GoodsItem {
    id : string | number
    name : string
    pic ?: string
    picUrls : string[]
    spuType : number
    goodsPoints ?: number
    estimatedPriceVo ?: EstimatedPriceVo
  }

  interface SetData {
    height : number
    dotStyle : string
    interval ?: number
    duration ?: number
    leftRightpadding : number
    shadow : string
    imgTop : number
    imgLeft : number
    imageUrls ?: string
    ShowPrice ?: number
    borderRadius : number
    radiusLeftTop : number
    radiusRightTop : number
    radiusLeftBottom : number
    radiusRightBottom : number
    topPriceSize : number
    goodsList : GoodsItem[]
    goodsStyle ?: string
    goodsPuantity ?: boolean
  }

  // Props定义
  const props = defineProps<{
    list : any[]
    setData : SetData
    loadmore ?: boolean
  }>()

  // Emits定义
  const emit = defineEmits<{
    getGroupsGoods : []
  }>()

  // 响应式数据
  const curIndex = ref<number>(0)

  // 计算属性
  const listLen = computed(() : number => {
    return props.setData.swiperList?.length || 0
  })

  const dataList = computed(() : GoodsItem[] => {
    if (!props.setData.goodsList) {
      return []
    }
    console.log("this.setData.goodsList", props.setData.goodsList)
    return props.setData.goodsList
  })

  const dataListLen = computed(() : number => {
    return props.setData.goodsList?.length || 0
  })

  // 方法
  const jumpPage = (page : string) : void => {
    if (page) {
      gotoPage(page)
    }
  }

  const partSwiperChange = (event : any) : void => {
    // partcurrentIndex = event.detail.current
  }

  // 滑动到最后一页，请求下一页数据
  const reachEnd = () : void => {
    emit('getGroupsGoods')
  }

  const swiperChange = (e : any) : void => {
    console.log("e.detail.current>>>>", e.detail)
    const currentIndex = e.detail.current
    curIndex.value = e.mp.detail.current

    if (props.loadmore) {
      if (currentIndex === props.setData.goodsList.length - 1) {
        console.log("请求下一页数据")
        // 滑动到最后一页，请求下一页数据
        if (props.setData.goodsStyle == "2" && props.setData.goodsPuantity) {
          return
        }
        reachEnd()
      }
    }
  }

  // 跳转到详情页
  const getBannerDetail = (id : string | number) : void => {
    uni.navigateTo({
      url: `/pages/goods/goods-detail/index?id=${id}`
    })
  }
</script>

<style lang="scss" scoped>
  .con-part2-con {
    width: 100%;
    // height: 390rpx;

    .swiper-tall {
      display: flex;
      align-items: center;
      height: 100%;

      .con-part2-con-container {
        // display: flex;
        // align-items: center;
        // width: 50% !important;

        .card-icon {
          width: 106rpx;
          height: 93rpx;
          position: absolute;
          top: 0rpx;
          left: 0rpx;
          z-index: 21;
        }

        .bg-shadow {
          width: 100%;
          height: 100%;
          background-color: #000;
          opacity: 0.5;
        }

        .slide-image {
          height: 386rpx;
          width: 386rpx;
          z-index: 1;
          margin: 0 auto;
          padding-top: 12rpx;

          .part2-con-title {
            text-align: center;
            font-size: 22rpx;
            font-weight: 400;
            color: #3596F1;
          }

          .part2-con-img {
            margin-top: 13rpx;
            width: 356rpx;
            height: 356rpx;
            margin: 0 auto;

            .img {
              width: 100%;
              height: 100%;
            }
          }

          .part2-con-txt1-img {
            display: block;
            margin: 10rpx auto 0rpx;
            height: 28rpx;
            width: 170rpx;
          }

          .part2-con-txt2 {
            text-align: left;
            font-weight: 500;
            width: 374rpx;
            padding: 10rpx;
            padding-top: 0px;
            margin: 0 auto;
          }

          .transform-txt {
            transform: none !important;
            padding-bottom: 10rpx;
          }
        }

        .active {
          transform: scale(1.14);
          transition: all 0.3s ease-in 0.1s;
          z-index: 20;
        }
      }
    }
  }

  .swiper-slide-item {
    position: relative;
  }

  .card-icon-image {
    width: 100rpx !important;
    height: 100rpx !important;
  }

  .card-price-overlay {
    position: absolute;
    bottom: 16rpx;
    left: 0rpx;
    text-align: center;
  }

  .goods-info-wrapper {
    background-color: #FFFFFF;
  }

  .goods-name {
    padding-left: 24rpx;
    padding-right: 24rpx;
  }

  .points-section {
    margin-top: 16rpx;
    margin-left: 24rpx;
  }

  .price-section {
    display: flex;
    padding-left: 24rpx;
    padding-right: 24rpx;
    position: relative;
    justify-content: space-around;
  }

  .original-price-value {
    color: #AAAAAA;
  }

  .estimated-price-value {
    font-weight: bold;
    color: #FF0000;
  }
</style>