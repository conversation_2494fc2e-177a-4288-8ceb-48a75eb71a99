<template>
  <view class="banner-container">
    <!-- 图片式展示 -->
    <swiper v-if="setData.isShow=='1'"
      :class="setData.dotStyle == 'square-dot' ? 'square-dot':(setData.dotStyle == 'round-dot' ? 'round-dot': '')"
      :style="{height: `${42+setData.height*2}rpx`}" :indicator-dots="setData.dotStyle == '' ? false : true "
      :indicator-color="'#cccccc'" :indicator-active-color="'#ffffff'" :autoplay="true"
      :interval="setData.interval?setData.interval:3000" :duration="1000" :circular="true"
      :previous-margin="(190-setData.leftRightpadding)+'rpx'" :next-margin="(190-setData.leftRightpadding)+'rpx'"
      @change="swiperChange">
      <swiper-item v-for="(item, i) in setData.swiperList" :key="i">
        <view class="image-container" v-if="curIndex===0">
          <view v-if="i===listLen-1" class="item-left" :style="{
              // width: '90%',
              // right:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              top:'5px',
              left: `${setData.leftRightpadding<='0'?(58+Math.abs(setData.leftRightpadding*2)):(setData.leftRightpadding*2+38)}rpx`,
            }">
            <div v-if="setData.shadow!='0'" style=" z-index: 9999" :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }">
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
          <view v-else-if="i===1" class="item-right" :style="{
              // width: '90%',
              // left:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              right: `${setData.leftRightpadding<='0'?(setData.leftRightpadding=='0'?setData.leftRightpadding:(-(setData.leftRightpadding*2-50))):(setData.leftRightpadding*2+38)}rpx`,
              top:'5px',
            }">
            <div v-if="setData.shadow!='0'" style=" z-index: 9999" :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }">
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
          <view v-else class="item-center" :style="{
            width: `${setData.mainWidth}%`,
              textAlign: `center`,
            }">
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
        </view>

        <view class="image-container" v-else-if="curIndex===listLen-1">
          <view v-if="i===0" class="item-right" :style="{
              // width: '90%',
              // left:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              right: `${setData.leftRightpadding<='0'?(setData.leftRightpadding=='0'?setData.leftRightpadding:(-(setData.leftRightpadding*2-50))):(setData.leftRightpadding*2+38)}rpx`,
              top:'5px',
            }">
            <div v-if="setData.shadow!='0'" style=" z-index: 9999" :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }">
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
          <view v-else-if="i===listLen-2" class="item-left" :style="{
              // width: '90%',
              // right:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              top:'5px',
              left: `${setData.leftRightpadding<='0'?(58+Math.abs(setData.leftRightpadding*2)):(setData.leftRightpadding*2+38)}rpx`,
            }">
            <div v-if="setData.shadow!='0'" style=" z-index: 9999" :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }">
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
          <view v-else class="item-center" :style="{
              width: `${setData.mainWidth}%`,
              textAlign: `center`,
            }">
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
        </view>

        <view class="image-container" v-else>
          <view v-if="i===curIndex-1" class="item-left" :style="{
              // width: '90%',
              // right:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              top:'5px',
              left: `${setData.leftRightpadding<='0'?(58+Math.abs(setData.leftRightpadding*2)):(setData.leftRightpadding*2+38)}rpx`,
            }">
            <div v-if="setData.shadow!='0'" style=" z-index: 9999" :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }">
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
          <view v-else-if="i===curIndex+1" class="item-right" :style="{
              // width: '90%',
              // left:'10px',
              height: `${setData.height*2}rpx`,
              position: 'relative',
              right: `${setData.leftRightpadding<='0'?(setData.leftRightpadding=='0'?setData.leftRightpadding:(-(setData.leftRightpadding*2-50))):(setData.leftRightpadding*2+38)}rpx`,
              top:'5px',
            }">
            <div v-if="setData.shadow!='0'" style=" z-index: 9999" :style="{
                position: 'absolute',
                width: `100%`,
                height: `100%`,
                backgroundColor:`#000`,
                opacity: `.5`,
              }">
            </div>
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
          <view v-else class="item-center" :style="{
              width: `${setData.mainWidth}%`,
              textAlign: `center`,
            }">
            <image :src="item.imageUrl" class="slide-image" :style="{
              transform: curIndex===i ? 'scale(1,1.13)' : 'scale(1,1)',
              transitionDuration: '.3s',
              transitionTimingFunction: 'ease',
              height: `${setData.height*2}rpx`,
              borderTopLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftTop*2)+'rpx':'0rpx'}`,
              borderTopRightRadius:`${setData.borderRadius == 1 ? (setData.radiusRightTop*2)+'rpx' : '0rpx'}`,
              borderBottomLeftRadius:`${setData.borderRadius == 1 ? (setData.radiusLeftBottom*2)+'rpx':'0rpx'}`,
              borderBottomRightRadius:`${setData.borderRadius == 1 ?(setData.radiusRightBottom*2)+'rpx':'0rpx'}`,
            }" @tap="jumpPage(item.pageUrl)" />
          </view>
        </view>
      </swiper-item>
    </swiper>
    <!-- 商品式展示 -->
    <view v-else>
      <swiper-components :setData="setData" :list="[]" @getGroupsGoods="reachEnd"></swiper-components>
    </view>

  </view>
</template>

<script setup lang="uts">
  import { ref, computed } from 'vue'
  // import formatPrice from "@/components/format-price/index.uvue"
  import swiperComponents from "./swiper-components.uvue"
  import { gotoPage } from "../div-base/div-page-urls.js"

  // 类型定义
  interface SwiperItem {
    imageUrl : string
    pageUrl : string
  }

  interface EstimatedPriceVo {
    originalPrice : number
    estimatedPrice : number
    totalDiscountPrice ?: string
  }

  interface GoodsItem {
    id : string | number
    name : string
    picUrls : string[]
    spuType : number
    goodsPoints ?: number
    estimatedPriceVo ?: EstimatedPriceVo
    surplus ?: number
  }

  interface SetData {
    isShow : string
    dotStyle : string
    height : number
    interval ?: number
    leftRightpadding : number
    shadow : string
    borderRadius : number
    radiusLeftTop : number
    radiusRightTop : number
    radiusLeftBottom : number
    radiusRightBottom : number
    mainWidth : number
    swiperList : SwiperItem[]
    goodsFromType ?: string
    goodsList : GoodsItem[]
    goodsStyle ?: string
    goodsPuantity ?: boolean
  }

  // Props定义
  const props = defineProps<{
    setData : SetData
    scaleX ?: string
    scaleY ?: string
    loadmore ?: boolean
  }>()

  // Emits定义
  const emit = defineEmits<{
    getGroupsGoods : []
  }>()

  // 响应式数据
  const curIndex = ref<number>(0)
  const descIndex = ref<number>(0)

  // 计算属性
  const listLen = computed(() : number => {
    return props.setData.swiperList?.length || 0
  })

  const dataList = computed(() : GoodsItem[] => {
    console.log("this.setData.goodsFromType", props.setData.goodsFromType)

    props.setData.goodsList.forEach((ele, index) => {
      let surplus = ''
      if (ele.estimatedPriceVo) {
        if (ele.estimatedPriceVo.originalPrice != ele.estimatedPriceVo.estimatedPrice) {
          surplus = ele.estimatedPriceVo.originalPrice - ele.estimatedPriceVo.estimatedPrice
          // Vue 3中没有$set，直接赋值即可
          props.setData.goodsList[index].surplus = surplus
          console.log("surplus", surplus)
        }
      }
    })
    return props.setData.goodsList
  })

  const dataListLen = computed(() : number => {
    return props.setData.goodsList?.length || 0
  })

  // 方法
  const jumpPage = (page : string) : void => {
    if (page) {
      gotoPage(page)
    }
  }

  // 滑动到最后一页，请求下一页数据
  const reachEnd = () : void => {
    emit('getGroupsGoods')
  }

  const swiperChange = (e : any) : void => {
    console.log("e.detail.current>>>>", e.detail)
    const currentIndex = e.detail.current
    curIndex.value = e.mp.detail.current

    if (props.loadmore) {
      if (currentIndex === props.setData.goodsList.length - 1) {
        console.log("请求下一页数据")
        // 滑动到最后一页，请求下一页数据
        if (props.setData.goodsStyle == "2" && props.setData.goodsPuantity) {
          return
        }
        // reachEnd()
      }
    }
  }

  const getBannerDetail = (id : string | number) : void => {
    uni.navigateTo({
      url: `/pages/goods/goods-detail/index?id=${id}`
    })
  }
</script>

<style lang="scss" scoped>
  .banner-container {
    width: 100vw;

    .active {
      transform: scale(1);
      transition: all 0.2s ease-in 0s;
      z-index: 20;
    }

    .image {
      height: 386rpx;
      width: 386rpx;
    }

    // height: 90%;

    // swiper-item{
    //   height: 90% !important;
    // }
    .swiper-shadow {
      border-left: 10rpx solid #ccc;
      box-shadow: -10rpx 0 10rpx -10rpx rgba(0, 0, 0, 0.3);
      box-shadow: 0 30rpx 60rpx rgba(0, 0, 0, .3);
    }

    .card-icon {
      width: 106rpx;
      height: 93rpx;
      position: absolute;
      top: 0rpx;
      left: 0rpx;
    }

    .swiper-shadow {
      border-right: 10rpx solid #ccc;
      box-shadow: 10rpx 0 10rpx -10rpx rgba(0, 0, 0, 0.3);
      box-shadow: 0 30rpx 60rpx rgba(0, 0, 0, .3);
    }

    .image-container {
      box-sizing: border-box;
      width: 100%;
      // height: 100%;
      display: flex;

      .slide-image {
        width: 317rpx;
        height: 156rpx;
        z-index: 200;
      }
    }

    .item-left {
      justify-content: flex-end;
      margin: 14rpx 0rpx 0 0;
    }

    .item-right {
      justify-content: flex-start;
      margin: 14rpx 0 0 0rpx;
    }

    .item-center {
      justify-content: center;
      margin: 19rpx 0 0 0;
    }

    .desc-wrap {
      box-sizing: border-box;
      width: 100%;
      height: 135rpx;
      padding: 24rpx 66rpx 0;

      .title {
        width: 100%;
        height: 42rpx;
        line-height: 42rpx;
        color: #222222;
        font-size: 30rpx;
        font-weight: 600;
        // text-align: left;
      }

      .desc {
        margin-top: 4rpx;
        width: 100%;
        height: 51rpx;
        line-height: 51rpx;
        color: #282828;
        font-size: 24rpx;
      }
    }

    @keyframes descAnimation {
      0% {
        opacity: 1;
      }

      25% {
        opacity: .5;
      }

      50% {
        opacity: 0;
      }

      75% {
        opacity: .5;
      }

      100% {
        opacity: 1;
      }
    }

    @-webkit-keyframes descAnimation {
      0% {
        opacity: 1;
      }

      25% {
        opacity: .5;
      }

      50% {
        opacity: 0;
      }

      75% {
        opacity: .5;
      }

      100% {
        opacity: 1;
      }
    }

    .hideAndShowDesc {
      animation: descAnimation .3s ease 1;
      -webkit-animation: descAnimation .3s ease 1;
    }
  }
</style>