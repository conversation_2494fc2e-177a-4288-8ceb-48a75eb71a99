/* 首页全局共用样式 */
.wrapper {
  margin-top: -20rpx;
}

.more {
  margin-top: 85rpx;
}

/* 轮播 */
.banner-image {
  width: 96% !important;
  border-radius: 10rpx !important;
  position: absolute;
  left: 15rpx;
}

.banner {
  min-height: 100%;
  margin-top: -120rpx;
  width: 100%;
  height: 210rpx;
}

/* 店铺 */
.store-swiper {
  margin-top: -30rpx;
  height: 366rpx;
  padding-bottom: 64rpx;
}

.shop-selection {
  margin-left: 0rpx !important;
  color: #666666;
}

.shop-more {
  margin-right: 0rpx !important;
  color: #666666;
}

.shop-detail {
  margin-top: -30rpx !important;
}

.shop-image {
  width: 200rpx;
  height: 200rpx !important;

  image {
    height: 200rpx !important;
  }
}

.shop-box {
  height: 200rpx !important;
  width: 200rpx !important;
  margin-right: 0rpx !important;
}

.shop-information {
  position: absolute;
  top: 140rpx;
  left: 50rpx;
}

.enter-bg {
  width: 100rpx;
  height: 40rpx;
  opacity: 0.3;
}

.shop-name {
  position: absolute;
  top: 0;
  line-height: 200rpx;
  padding: 0 10rpx 0 5rpx;
  width: 200rpx;
}

.goods-name {
  padding: 0 10rpx !important;
  color: #333333 !important;
}