<template>
  <view class="component-renderer">
    <template v-for="(component, index) in visibleComponents" :key="`${component.id}-${index}`">
      <!-- 头部组件 -->
      <div-head v-if="component.componentName === 'headdivComponent'" v-model="component.data"
        :is-status-bar="index === 0" :is-back="isBack" />

      <!-- 搜索组件 -->
      <div-search v-else-if="component.componentName === 'searchComponent'" v-model="component.data"
        :is-status-bar="index === 0" :is-back="isBack" />

      <!-- 商品分类组件 -->
      <div-goods-category v-else-if="component.componentName === 'goodsCategoryComponent'" v-model="component.data"
        :is-status-bar="index === 0" :page-select-poi="pageSelectPoi"
        @getpage="(e: any) => handleGetPage(e, component.id)" />

      <!-- 广告组件 -->
      <div-advert v-else-if="component.componentName === 'advertComponent'" v-model="component.data"
        :is-status-bar="index === 0" />

      <!-- 文章列表组件 -->
      <div-article-list v-else-if="component.componentName === 'articleListComponent'" v-model="component.data" />

      <!-- 直播列表组件 -->
      <div-living-list v-else-if="component.componentName === 'livingListComponent'" v-model="component.data" />

      <!-- 图片列表组件 -->
      <div-image-list v-else-if="component.componentName === 'imageListComponent'" v-model="component.data" />

      <!-- 轮播组件 -->
      <div-swiper v-else-if="component.componentName === 'swiperComponent'" v-model="component.data" />

      <!-- 导航按钮组件 -->
      <div-nav-button v-else-if="component.componentName === 'navButtonComponent'" v-model="component.data" />

      <!-- 商品组件 - 需要 showHeavy2 条件 -->
      <div-goods v-else-if="shouldShowComponent(component.componentName, 'goodsComponent')" v-model="component.data" />

      <!-- 商品行组件 - 需要 showHeavy1 且非筛选状态 -->
      <div-goods-row v-else-if="shouldShowComponent(component.componentName, 'goodsRowComponent')"
        v-model="component.data" />

      <!-- 激活组件 -->
      <div-activate v-else-if="component.componentName === 'activateComponent'" v-model="component.data" />

      <!-- 商品分组组件 - 需要 showHeavy2 条件 -->
      <div-goods-groups v-else-if="shouldShowComponent(component.componentName, 'goodsGroupingComponent')"
        v-model="component.data" :ref="setGoodsGroupsRef" />

      <!-- 新人组件 -->
      <div-new-person v-else-if="component.componentName === 'newPersonComponent'" v-model="component.data"
        @open-rule-popup="handleOpenRulePopup" />
    </template>
  </view>
</template>

<script setup lang="ts">
  import { ref, computed, watch, nextTick } from 'vue'

  // 组件导入
  import divSearch from './div-components/div-search/index.uvue'
  import divHead from './div-components/div-head/div-head.uvue'
  import divImageList from './div-components/div-image-list/div-image-list.uvue'
  import divAdvert from './div-components/div-advert/index.uvue'
  import divSwiper from './div-components/div-swiper/div-swiper.uvue'
  import divNavButton from './div-components/div-nav-button/div-nav-button.uvue'
  import divGoods from './div-components/div-goods/index.uvue'
  import divGoodsCategory from './div-components/div-goods-category/index.uvue'
  import divGoodsGroups from './div-components/div-goods-groups/div-goods-groups.uvue'
  import divActivate from '@/page-components/activation-coupon/index.uvue'
  import divNewPerson from './div-components/div-new-person/div-new-person.uvue'
  import divGoodsRow from './div-components/div-goods-row/div-goods-row.uvue'
  import divLivingList from './div-components/div-living-list/div-living-list.uvue'
  import divArticleList from './div-components/div-article-list/div-article-list.uvue'

  // 类型定义
  interface ComponentItem {
    id : string
    componentName : string
    data : any
    [key : string] : any
  }

  interface Props {
    componentsList ?: ComponentItem[]
    isBack ?: boolean
    showScreen ?: boolean
    showMinutes ?: number
    pageSelectPoi ?: string
  }

  // Props 定义
  const props = withDefaults(defineProps<Props>(), {
    componentsList: () => [],
    isBack: false,
    showScreen: false,
    showMinutes: 0,
    pageSelectPoi: ''
  })

  // Emits 定义
  const emit = defineEmits<{
    getpage : [data: any]
    openRulePopup : [info: any]
  }>()

  // 响应式数据
  const showHeavy1 = ref(true)
  const showHeavy2 = ref(true)
  const componentsLists = ref<ComponentItem[]>([])
  const goodsGroupsRef = ref()

  // 组件显示条件配置
  const COMPONENT_CONDITIONS = {
    goodsComponent: () => showHeavy2.value,
    goodsRowComponent: () => showHeavy1.value && !props.showScreen,
    goodsGroupingComponent: () => showHeavy2.value
  } as const

  // 计算属性
  const visibleComponents = computed(() => {

    return componentsLists.value.filter(component => {
      const condition = COMPONENT_CONDITIONS[component.componentName as keyof typeof COMPONENT_CONDITIONS]
      return condition ? condition() : true
    })
  })

  // 工具函数
  const shouldShowComponent = (currentName : string, targetName : string) : boolean => {
    if (currentName !== targetName) return false

    const condition = COMPONENT_CONDITIONS[targetName as keyof typeof COMPONENT_CONDITIONS]
    return condition ? condition() : true
  }

  // 分批加载组件 - 性能优化
  const loadComponentsInBatches = (components : ComponentItem[]) => {
    if (!components || components.length === 0) {
      componentsLists.value = []
      return
    }
    const BATCH_SIZE = 10
    const CRITICAL_COMPONENTS = ['headdivComponent', 'searchComponent', 'swiperComponent']

    // 优先加载关键组件
    const criticalComponents = components.filter(comp =>
      CRITICAL_COMPONENTS.includes(comp.componentName)
    )
    const otherComponents = components.filter(comp =>
      !CRITICAL_COMPONENTS.includes(comp.componentName)
    )
    // 首先加载关键组件
    componentsLists.value = criticalComponents
    // 分批加载其他组件
    if (otherComponents.length > 0) {
      if (otherComponents.length <= BATCH_SIZE) {
        nextTick(() => {
          componentsLists.value = [...criticalComponents, ...otherComponents]
        })
      } else {
        // 大量组件时分批处理
        const batches = Math.ceil(otherComponents.length / BATCH_SIZE)
        let loadedComponents = [...criticalComponents]
        for (let i = 0; i < batches; i++) {
          setTimeout(() => {
            const start = i * BATCH_SIZE
            const end = Math.min(start + BATCH_SIZE, otherComponents.length)
            const batch = otherComponents.slice(start, end)
            loadedComponents = [...loadedComponents, ...batch]
            componentsLists.value = [...loadedComponents]
          }, i * 50) // 每批间隔50ms
        }
      }
    }
  }

  // 重型组件显示控制
  const controlHeavyComponents = (showScreen : boolean) => {
    if (!showScreen) return
    const delay = (props.showMinutes || 0) * 1000
    // 隐藏重型组件
    showHeavy1.value = false
    showHeavy2.value = false

    // 分阶段显示重型组件
    if (delay > 0) {
      setTimeout(() => {
        showHeavy1.value = true
      }, delay)

      setTimeout(() => {
        showHeavy2.value = true
      }, delay + 200)
    } else {
      // 无延迟时立即显示
      nextTick(() => {
        showHeavy1.value = true
        showHeavy2.value = true
      })
    }
  }

  // 事件处理函数
  const handleGetPage = (data : any, componentId : string) => {
    emit('getpage', {
      ...data,
      componentId
    })
  }

  const handleOpenRulePopup = (info : any) => {
    emit('openRulePopup', info)
  }

  // ref 设置函数
  const setGoodsGroupsRef = (el : any) => {
    goodsGroupsRef.value = el
  }

  // 下拉刷新处理
  const handlePullDownRefresh = () => {
    if (goodsGroupsRef.value?.pullDownRefresh) {
      goodsGroupsRef.value.pullDownRefresh()
    }
  }

  // 触底加载处理
  const handleReachBottom = () => {
    if (goodsGroupsRef.value) {
      console.log('调用商品分组触底加载', goodsGroupsRef.value.TabCur)

      // 可以在这里添加更多触底逻辑
      if (goodsGroupsRef.value.loadMore) {
        goodsGroupsRef.value.loadMore()
      }
    }
  }

  // 组件控制方法
  const showAllHeavyComponents = () => {
    showHeavy1.value = true
    showHeavy2.value = true
  }

  const hideAllHeavyComponents = () => {
    showHeavy1.value = false
    showHeavy2.value = false
  }

  const reloadAllComponents = () => {
    if (props.componentsList) {
      loadComponentsInBatches([...props.componentsList])
    }
  }

  // 获取组件统计信息
  const getComponentStats = () => {
    const total = componentsLists.value.length
    const visible = visibleComponents.value.length
    const heavy = componentsLists.value.filter(comp =>
      ['goodsComponent', 'goodsRowComponent', 'goodsGroupingComponent'].includes(comp.componentName)
    ).length
    return { total, visible, heavy }
  }

  // 监听器
  watch(
    () => props.showScreen,
    controlHeavyComponents,
    { immediate: true }
  )

  watch(
    () => props.componentsList,
    (newComponents) => {
      if (newComponents && Array.isArray(newComponents)) {
        loadComponentsInBatches([...newComponents])
      }
    },
    { immediate: true, deep: true }
  )

  // 监听重型组件状态变化
  watch(
    [showHeavy1, showHeavy2],
    ([heavy1, heavy2]) => {
      console.log(`重型组件状态: Heavy1=${heavy1}, Heavy2=${heavy2}`)
    }
  )

  // 暴露方法给父组件
  defineExpose({
    pullDownRefresh: handlePullDownRefresh,
    reachBottom: handleReachBottom,
    showHeavyComponents: showAllHeavyComponents,
    hideHeavyComponents: hideAllHeavyComponents,
    reloadComponents: reloadAllComponents,
    getStats: getComponentStats,
    // 获取特定组件引用
    getGoodsGroupsRef: () => goodsGroupsRef.value,
    // 手动控制重型组件
    setHeavyComponentsState: (heavy1 : boolean, heavy2 : boolean) => {
      showHeavy1.value = heavy1
      showHeavy2.value = heavy2
    }
  })
</script>

<style scoped lang="scss">
  .component-renderer {
    width: 100%;
    min-height: 100vh;
  }

  // 性能优化相关样式
  .component-renderer {
    // 启用硬件加速
    transform: translateZ(0);
    // 为长列表优化
    contain: layout style paint;
  }

  // 响应式适配
  @media screen and (max-width: 750px) {
    .component-renderer {
      font-size: 28rpx;
    }
  }

  // 暗色模式支持
  @media (prefers-color-scheme: dark) {
    .component-renderer {
      background-color: #1a1a1a;
    }
  }
</style>