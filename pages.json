{"pages": [{"path": "pages/home/<USER>", "style": {"enablePullDownRefresh": true, "navigationBarTitleText": "松鼠美妆", "onReachBottomDistance": 400}}, {"path": "pages/second-tab/index", "style": {"enablePullDownRefresh": true, "navigationBarTitleText": "松鼠好物", "onReachBottomDistance": 400}}, {"path": "pages/shopping-cart/index", "style": {"navigationBarTitleText": "购物车", "componentPlaceholder": {"shopcart": "view"}}}, {"path": "pages/tab-personal/index", "style": {"navigationBarTitleText": "个人中心"}}, {"path": "pages/third-tab/index", "style": {"enablePullDownRefresh": true, "navigationBarTitleText": "逛逛"}}], "subPackages": [{"root": "pages/goods", "pages": [{"path": "goods-detail/index", "style": {"enablePullDownRefresh": true, "navigationBarTitleText": "商品详情"}}]}, {"root": "pages/public", "pages": [{"path": "webview/webview", "style": {"navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}, {"path": "activity/webview", "style": {"navigationBarTitleText": "", "navigationBarBackgroundColor": "#ffffff", "navigationBarTextStyle": "black"}}]}], "tabBar": {"color": "#666666", "selectedColor": "#cb0101", "borderStyle": "black", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/home/<USER>", "text": "松鼠美妆", "iconPath": "/static/tab-icons/1-001.jpg", "selectedIconPath": "/static/tab-icons/1-002.jpg"}, {"pagePath": "pages/second-tab/index", "text": "松鼠好物", "iconPath": "/static/tab-icons/2-001.jpg", "selectedIconPath": "/static/tab-icons/2-002.jpg"}, {"pagePath": "pages/third-tab/index", "text": "逛逛", "iconPath": "/static/tab-icons/3-001.jpg", "selectedIconPath": "/static/tab-icons/3-002.jpg"}, {"pagePath": "pages/shopping-cart/index", "text": "购物车", "iconPath": "/static/tab-icons/4-001.jpg", "selectedIconPath": "/static/tab-icons/4-002.jpg"}, {"pagePath": "pages/tab-personal/index", "text": "我的", "iconPath": "/static/tab-icons/5-001.jpg", "selectedIconPath": "/static/tab-icons/5-002.png"}]}, "globalStyle": {"navigationStyle": "custom", "navigationBarTextStyle": "black", "maxWidth": 375, "dynamicRpx": true}, "uniIdRouter": {}}