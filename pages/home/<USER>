<template>
	<view class="container">
		<!-- 朋友圈分享打开的单页面 -->
		<openScreenCom   v-if="scene != 1154" />
		<share-single-page v-if="scene == 1154" />
		<page-components v-else :components-list="componentsList"></page-components>
		<redPackage></redPackage>
	</view>

</template>

<script setup lang="uts">
	import PageComponents from "@/page-components/index.uvue";
	import shareSinglePage from "@/components/share-single-page/index.uvue";
	import redPackage from "@/components/red-package/index.uvue";
	import openScreenCom from "./components/openScreen.uvue";
	import { pagedevisePage } from "@/page-components/div-components/api/index.uts";
	import { setTabBar } from "@/utils/tabbar.uts";
	import { ref } from 'vue'
	import { initPage } from "@/utils/login.uts"

	const pageData = ref({ pageComponent: {} })
	const componentsList = ref<ConfigPageData.ComponentsList>([]);
	const scene = ref<number>(0)


	onMounted(async () => {
		// #ifdef MP-WEIXIN
		let getLaunchOptions = uni.getLaunchOptionsSync();
		scene.value = getLaunchOptions.scene;
		//场景值等于 1154 分享单页模式
		if (scene.value && scene.value == 1154) return;
		// #endif
		setTabBar();
		// 测试代码
		initPage().then(res=>{
			console.error("=====initPage=========", res)
		})


	})

	onPullDownRefresh(() => {
		// 下拉刷新逻辑
		setTimeout(() => {
			uni.stopPullDownRefresh()
		}, 1000)
	})



	onReachBottom(() => {
		// 上拉加载更多逻辑
	})
</script>

<style scoped>
	.container {
		width: 100%;
	}

	.home-page {
		padding: 20px;
	}

	.title {
		font-size: 24px;
		text-align: center;
	}
</style>
