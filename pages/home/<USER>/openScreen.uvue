<template>
  <view v-if="refState.showAd" class="open-screen-container">
    <!-- 广告图片 -->
    <image 
      :src="refState.adImageUrl" 
      mode="aspectFill" 
      class="ad-image"
      @load="onImageLoad"
      @error="onImageError"
    />
    
    <!-- 跳过按钮 -->
    <view class="skip-button" @tap="skipAd">
      <text class="skip-text">{{ refState.skipText }}</text>
    </view>
    
    <!-- 关闭按钮 -->
    <view class="close-button" @tap="closeAd">
      <text class="close-text">×</text>
    </view>
  </view>
</template>

<script setup lang="uts">
import { reactive, onMounted, onUnmounted } from 'vue'
import { advertisementinfo } from '@/api/common.uts'
import { isShowAdvert } from "@/utils/util.uts"

interface AdData {
  imgUrl: string
  linkUrl?: string
  bgColor?: string
  intervalTime?: number,
  intervalNumber?: number
  localImagePath?: string // 本地图片路径
}

interface Props {
  // 广告类型
  adType?: string
  // 显示时长（秒）
  duration?: number
  // 是否自动关闭
  autoClose?: boolean
}

interface Emits {
  (e: 'close'): void
}

// 定义 props
const props = withDefaults(defineProps<Props>(), {
  adType: 'OPEN_ADV',
  duration: 3,
  autoClose: true,
})

// 定义 emits
const emit = defineEmits<Emits>()

// 响应式状态
const refState = reactive({
  showAd: false,
  adImageUrl: '',
  skipText: '跳过',
  countdown: 3,
  timer: null as any,
  countdownTimer: null as any
})

// 获取本地图片目录（平台适配）
const getAdImageDir = (): string => {
  // #ifdef MP-WEIXIN
  return `${wx.env.USER_DATA_PATH}/ad_images/`
  // #endif
  // #ifdef APP-PLUS
  return `${uni.env.USER_DATA_PATH}/ad_images/`
  // #endif
  return ''
}

// 初始化广告
const initAd = async () => {
  try {
    // 先从缓存获取
    const cachedAd = getCachedAd()
    if (cachedAd && cachedAd.imgUrl && isShowAdvert(cachedAd.intervalTime, cachedAd.intervalNumber, "screen")) {
      console.log('使用缓存的广告数据')
      // 优先使用本地图片
      const localImagePath = await getLocalImagePath(cachedAd.imgUrl)
      if (localImagePath) {
        cachedAd.localImagePath = localImagePath
        showAdWithData(cachedAd)
        return
      }
    } 
    await fetchAdFromApi()
  } catch (error) {
    console.error('初始化广告失败:', error)
  }
}

// 从API获取广告数据
const fetchAdFromApi = async () => {
  try {
    const res = await advertisementinfo({
      bigClass: props.adType
    })
    if (res.data && res.data.imgUrl) {
      console.log('获取到广告数据:', res.data)
      // 下载并缓存图片
      const localImagePath = await downloadAndCacheImage(res.data.imgUrl)
      if (localImagePath) {
        res.data.localImagePath = localImagePath
      }
      // 缓存广告数据
      cacheAdData(res.data)
      // 显示广告
      showAdWithData(res.data)
    } else {
      console.log('没有广告数据')
    }
  } catch (error) {
    console.error('获取广告数据失败:', error)
  }
}

// 显示广告
const showAdWithData = (adData: AdData) => {
  // 优先使用本地图片路径
  refState.adImageUrl = adData.localImagePath || adData.imgUrl
  refState.showAd = true
  
  // 设置倒计时
  refState.countdown = adData.duration || props.duration
  refState.skipText = `跳过(${refState.countdown}s)`
  
  // 开始倒计时
  startCountdown()
  
  // 自动关闭
  if (props.autoClose) {
    refState.timer = setTimeout(() => {
      closeAd()
    }, refState.countdown * 1000)
  }
}

// 下载并缓存图片（平台适配）
const downloadAndCacheImage = async (imageUrl: string): Promise<string | null> => {
  try {
    // 生成本地文件名
    const fileName = generateImageFileName(imageUrl)
    const localPath = `${getAdImageDir()}${fileName}`
    
    // #ifdef MP-WEIXIN || APP-PLUS
    // 检查本地文件是否已存在
    const fileExists = await checkFileExists(localPath)
    if (fileExists) {
      console.log('本地图片已存在:', localPath)
      return localPath
    }
    
    // 下载图片
    console.log('开始下载图片:', imageUrl)
    const downloadRes = await uni.downloadFile({
      url: imageUrl,
      timeout: 10000
    })
    
    if (downloadRes.statusCode === 200 && downloadRes.tempFilePath) {
      // 确保目录存在
      await ensureDirectoryExists(getAdImageDir())
      
      // 保存到本地
      const saveRes = await uni.saveFile({
        tempFilePath: downloadRes.tempFilePath
      })
      
      if (saveRes.savedFilePath) {
        console.log('图片下载并缓存成功:', saveRes.savedFilePath)
        return saveRes.savedFilePath
      }
    }
    
    console.error('图片下载失败:', downloadRes)
    return null
    // #endif
  } catch (error) {
    console.error('下载图片失败:', error)
    return null
  }
}

// 获取本地图片路径
const getLocalImagePath = async (imageUrl: string): Promise<string | null> => {
  try {
    const fileName = generateImageFileName(imageUrl)
    const localPath = `${getAdImageDir()}${fileName}`
    
    // #ifdef MP-WEIXIN || APP-PLUS
    // 检查本地文件是否已存在
    const fileExists = await checkFileExists(localPath)
    if (fileExists) {
      console.log('找到本地图片:', localPath)
      return localPath
    }
    
    // #endif
    return null
  } catch (error) {
    console.error('检查本地图片失败:', error)
    return null
  }
}

// 生成图片文件名
const generateImageFileName = (imageUrl: string): string => {
  // 使用URL的hash作为文件名，避免特殊字符
  let hash = 0
  for (let i = 0; i < imageUrl.length; i++) {
    const char = imageUrl.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  // 获取文件扩展名
  const extension = imageUrl.split('.').pop() || 'jpg'
  return `ad_${Math.abs(hash)}.${extension}`
}

// 检查文件是否存在（平台适配）
const checkFileExists = (filePath: string): Promise<boolean> => {
  return new Promise((resolve) => {
    // #ifdef MP-WEIXIN || APP-PLUS
    uni.getFileInfo({
      filePath: filePath,
      success: () => {
        resolve(true)
      },
      fail: () => {
        resolve(false)
      }
    })
    // #endif
    // #ifndef MP-WEIXIN && !APP-PLUS
    resolve(false)
    // #endif
  })
}

// 确保目录存在（平台适配）
const ensureDirectoryExists = async (dirPath: string): Promise<void> => {
  // #ifdef MP-WEIXIN || APP-PLUS
  return new Promise((resolve, reject) => {
    uni.mkdir({
      dirPath: dirPath,
      recursive: true,
      success: () => {
        resolve()
      },
      fail: (error) => {
        // 如果目录已存在，也不算错误
        if (error.errMsg && error.errMsg.includes('already exists')) {
          resolve()
        } else {
          reject(error)
        }
      }
    })
  // #endif
  // #ifndef MP-WEIXIN && !APP-PLUS
  return Promise.resolve()
  // #endif
}

// 清理过期的本地图片
const cleanExpiredLocalImages = async () => {
  try {
    const adDir = getAdImageDir()
    
    // #ifdef MP-WEIXIN || APP-PLUS
    // 获取目录下的所有文件
    const files = await uni.readdir({
      dirPath: adDir
    })
    
    if (files.files && files.files.length > 0) {
      const now = Date.now()
      const expireTime = 7 * 24 * 60 * 60 * 1000 // 7天
      
      for (const fileName of files.files) {
        const filePath = `${adDir}${fileName}`
        const fileInfo = await uni.getFileInfo({
          filePath: filePath
        })
        
        // 检查文件是否过期
        if (now - fileInfo.createTime > expireTime) {
          await uni.unlink({
            filePath: filePath
          })
          console.log('删除过期图片:', fileName)
        }
      }
    }
    // #endif
  } catch (error) {
    console.error('清理过期图片失败:', error)
  }
}

// 开始倒计时
const startCountdown = () => {
  refState.countdownTimer = setInterval(() => {
    refState.countdown--
    refState.skipText = `跳过(${refState.countdown}s)`
    
    if (refState.countdown <= 0) {
      clearCountdownTimer()
    }
  }, 1000)
}

// 清除倒计时定时器
const clearCountdownTimer = () => {
  if (refState.countdownTimer) {
    clearInterval(refState.countdownTimer)
    refState.countdownTimer = null
  }
}

// 清除所有定时器
const clearTimers = () => {
  if (refState.timer) {
    clearTimeout(refState.timer)
    refState.timer = null
  }
  clearCountdownTimer()
}

// 缓存广告数据
const cacheAdData = (adData: AdData) => {
  try {
    const cacheData = {
      ...adData,
      cacheTime: Date.now()
    }
    uni.setStorageSync('open_screen_ad', cacheData)
    console.log('广告数据已缓存')
  } catch (error) {
    console.error('缓存广告数据失败:', error)
  }
}

// 获取缓存的广告数据
const getCachedAd = (): AdData | null => {
  try {
    const cached = uni.getStorageSync('open_screen_ad')
    if (cached) {
      // 检查缓存是否过期（24小时）
      const now = Date.now()
      const cacheTime = cached.cacheTime || 0
      const expireTime = 24 * 60 * 60 * 1000 // 24小时
      
      if (now - cacheTime < expireTime) {
        return cached
      } else {
        // 清除过期缓存
        uni.removeStorageSync('open_screen_ad')
        console.log('广告缓存已过期')
      }
    }
    return null
  } catch (error) {
    console.error('获取缓存广告数据失败:', error)
    return null
  }
}

// 图片加载成功
const onImageLoad = () => {
  console.log('广告图片加载成功')
}

// 图片加载失败
const onImageError = () => {
  console.error('广告图片加载失败')
  closeAd()
}

// 跳过广告
const skipAd = () => {
  closeAd()
}

// 关闭广告
const closeAd = () => {
  clearTimers()
  refState.showAd = false
  emit('close')
}

// 生命周期
onMounted(() => {
  initAd()
  // 清理过期的本地图片
  cleanExpiredLocalImages()
})

onUnmounted(() => {
  clearTimers()
})
</script>

<style lang="scss" scoped>
.open-screen-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background-color: #000;
}

.ad-image {
  width: 100%;
  height: 100%;
}

.skip-button {
  position: absolute;
  top: 60px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 20px;
  z-index: 10000;
}

.skip-text {
  color: #fff;
  font-size: 14px;
}

.close-button {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10001;
}

.close-text {
  color: #fff;
  font-size: 24px;
  font-weight: bold;
}
</style>