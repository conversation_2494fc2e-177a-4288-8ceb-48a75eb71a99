<template>
  <view>
    <!-- #ifndef MP -->
    <cu-custom :bgColor="'bg-' + theme.backgroundColor" :isBack="true" :hideMarchContent="true">
      <block slot="content">{{ title }}</block>
    </cu-custom>
    <!-- #endif -->
    <view class="margin-top-xl">
      <web-view :src="url" :webview-styles="webviewStyles" />
    </view>
    <share-single-page v-if="scene === 1154" />
  </view>
</template>

<script setup>
  import {backLoginPage} from "utils/util"
  import __config from 'config/env'
  import shareSinglePage from "@/components/share-single-page/index.uvue"
  import api from 'utils/api'
  import { useGlobalDataStore } from '@/stores/globalData.uts'
  const { theme } = useGlobalDataStore()
  // 响应式数据
  const url = ref("")
  const title = ref("浏览")
  const actid = ref("")
  const scene = ref('')


  // 非响应式配置对象
  const webviewStyles = {
    progress: {
      color: "#FF3333",
    },
  }

  const shareObj = reactive({
    title: '松鼠美淘大转盘',
    imageUrl: ''
  })

  // 有数统计使用
  const page_title = ref("H5页面")

  // 分享到朋友圈
  onShareTimeline((res) => {
    console.log(res)
  })

  // 分享给朋友
  onShareAppMessage((res) => {
    if (res.from === 'menu') {
      console.log("===onShareAppMessage===1===", url.value)
      const paras = url.value.split("=", 10)[1]
      console.log("===onShareAppMessage===2===", paras)
      const id = paras.split("&")[0]

      api.getActShare(id).then(res => {
        console.log('res===', res.data)
        uni.showToast({
          title: res.data,
          icon: 'none'
        })
        const tempFromUrl = url.value
        url.value = ""
        setTimeout(() => {
          // url.value = "https://shopapi.songlei.com/slshop-h5/agreement/user.html"
          url.value = tempFromUrl + '&times=' + (new Date().getTime())
        }, 100)
      }).catch(e => {
        console.log("===onShareAppMessage===4===", 4)
      })
    }

    return {
      title: shareObj.title,
      imageUrl: shareObj.imageUrl,
      success(res) {
        console.log(res, '分享成功111111')
      },
      fail(res) {
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        })
      }
    }
  })

  // 页面加载
  onLoad((option) => {
    //#ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket: true,
      // 设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
      menus: ["shareAppMessage", "shareTimeline"]
    })

    const getLaunchOptions = uni.getLaunchOptionsSync()
    scene.value = getLaunchOptions.scene
    //#endif

    uni.setNavigationBarColor({
      frontColor: "#000000", // 必写项
      backgroundColor: "#ffffff", // 必写项
      // animation: { // 可选项
      //     duration: 400,
      //     timingFunc: 'easeIn'
      // }
    })

    let fromUrl = ""
    console.log("===", option.scene)

    const { scene: optionScene, type, id } = option

    if (optionScene) {
      // 接受二维码中参数
      const scenes = decodeURIComponent(option.scene).split("&")
      console.log("scenes===", scenes)
      fromUrl = scenes[0]
    } else {
      if (type === 'nineGrid') {
        fromUrl = "nineGrid?id=" + id
      } else {
        fromUrl = decodeURIComponent(option.url)
      }
    }

    console.log("fromUrl===", fromUrl)

    if (fromUrl && fromUrl.indexOf("nineGrid") !== -1) {
      if (fromUrl.startsWith("nineGrid")) {
        console.log("fromUrl=1111==", fromUrl, __config.nineGridUrl)
        fromUrl = fromUrl.replace('nineGrid', __config.nineGridUrl)
      }
      const clientType = "MA" // 客户端类型小程序
      const appId = uni.getAccountInfoSync().miniProgram.appId // 小程序appId
      const thirdSession = uni.getStorageSync("third_session") || ""
      if (!thirdSession) {
        backLoginPage()
        return
      }
      console.log("fromUrl========222222==========", fromUrl)
      fromUrl += `&client-type=${clientType}&app-id=${appId}&third-session=${thirdSession}`

      const paras = fromUrl.split("=", 10)[1]
      const id = paras.split("&")[0]

      api.getActInfo(id).then(res => {
        // shareObj.title = res.data.actInfo.name
        shareObj.imageUrl = res.data.actInfo.shareImg
      })
    }

    console.log("===fromUrl========", fromUrl)
    url.value = fromUrl

    if (option.title) {
      title.value = option.title
      uni.setNavigationBarTitle({
        title: option.title,
      })
      page_title.value = option.title
    }
  })
</script>