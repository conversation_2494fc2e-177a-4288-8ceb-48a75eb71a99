<template>
  <view>
    <view class="margin-top-xl">
      <web-view :src="url" :webview-styles="webviewStyles" :update-title="false" :fullscreen="false" />
    </view>
    <share-single-page v-if="scene === 1154" />
  </view>
</template>

<script setup>
  import {backLoginPage} from "utils/util"
  import __config from 'config/env'
  import shareSinglePage from "@/components/share-single-page/index.uvue"
  import api from 'utils/api'
  import { gotoPage } from '@/page-components/div-components/div-base/div-page-urls.uts'

  // 响应式数据
  const url = ref("")
  const title = ref("浏览")
  const actid = ref("")
  const scene = ref('')
  const goToUrl = ref('')
  const onShowTimes = ref(0)
  const fromUrl = ref('')

  // 非响应式配置对象
  const webviewStyles = {
    progress: {
      color: "#FF3333",
    },
  }
  const shareObj = reactive({
    title: '松鼠美淘大转盘',
    imageUrl: ''
  })

  // 有数统计使用
  const page_title = ref("H5页面")
  // 分享到朋友圈
  onShareTimeline((res) => {
    console.log(res)
  })
  // 分享给朋友
  onShareAppMessage((res) => {
    if (res.from === 'menu') {
      const paras = url.value.split("=", 10)[1]
      const id = paras.split("&")[0]
      api.getActShare(id).then(res => {
        console.log('res===', res.data)
        uni.showToast({
          title: res.data,
          icon: 'none'
        })
        const tempFromUrl = url.value
        url.value = ""
        setTimeout(() => {
          url.value = tempFromUrl + '&times=' + (new Date().getTime())
        }, 100)
      }).catch(e => {
        console.log("===onShareAppMessage===4===", 4)
      })
    }
    return {
      title: shareObj.title,
      imageUrl: shareObj.imageUrl,
      success(res) {
      },
      fail(res) {
        uni.showToast({
          title: '分享失败',
          icon: 'none'
        })
      }
    }
  })

  // 页面加载
  onLoad((option) => {
    //#ifdef MP-WEIXIN
    wx.showShareMenu({
      withShareTicket: true,
      // 设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
      menus: ["shareAppMessage", "shareTimeline"]
    })

    const getLaunchOptions = uni.getLaunchOptionsSync()
    scene.value = getLaunchOptions.scene
    //#endif

    uni.setNavigationBarColor({
      frontColor: "#000000", // 必写项
      backgroundColor: "#ffffff", // 必写项
    })

    console.log("===", option.scene)

    const { scene: optionScene, type, id } = option

    if (optionScene) {
      // 接受二维码中参数
      const scenes = decodeURIComponent(option.scene).split("&")
      console.log("scenes===", scenes)
      fromUrl.value = scenes[0]
    } else {
      if (type === 'nineGrid') {
        fromUrl.value = "nineGrid?id=" + id
      } else {
        fromUrl.value = decodeURIComponent(option.url)
      }
    }

    console.log("fromUrl=111==", fromUrl.value)

    if (fromUrl.value && fromUrl.value.indexOf("nineGrid") !== -1) {
      console.log("fromUrl=222==", fromUrl.value)
      const pages = getCurrentPages()
      const pagesLength = pages.length // 当前页面栈的个数

      // 如果是直接进来到大转盘页面，大转盘是第一个页面
      if (pagesLength === 1) {
        uni.navigateTo({
          url: "/pages/public/activity/webview?url=" + encodeURIComponent(fromUrl.value)
        })

        const paras = fromUrl.value.split("=", 10)[1]
        const id = paras.split("&")[0]

        // 获取大转盘id
        api.getActInfo(id).then(res => {
          // shareObj.title = res.data.actInfo.name
          goToUrl.value = res.data.actInfo.goToUrl
        })
        return
      }

      // 如果大转盘不是第一个页面
      if (fromUrl.value.startsWith("nineGrid")) {
        fromUrl.value = fromUrl.value.replace('nineGrid', __config.nineGridUrl)
      }

      const clientType = "MA" // 客户端类型小程序
      const appId = uni.getAccountInfoSync().miniProgram.appId // 小程序appId
      const thirdSession = uni.getStorageSync("third_session") || ""

      if (!thirdSession) {
        backLoginPage()
        return
      }

      fromUrl.value += `&client-type=${clientType}&app-id=${appId}&third-session=${thirdSession}`

      const paras = fromUrl.value.split("=", 10)[1]
      const id = paras.split("&")[0]

      api.getActInfo(id).then(res => {
        // shareObj.title = res.data.actInfo.name
        shareObj.imageUrl = res.data.actInfo.shareImg
        goToUrl.value = res.data.actInfo.goToUrl
      })
    }
    console.log("====fromUrl.value============", fromUrl.value)
    url.value = fromUrl.value

    if (option.title) {
      page_title.value = option.title
    }
  })

  // 页面显示
  onShow(() => {
    onShowTimes.value++
    const pages = getCurrentPages()
    const pagesLength = pages.length // 当前页面栈的个数

    // 如果是跳转到大转盘又返回到该页面的，就跳转到活动页面
    if (onShowTimes.value >= 2) {
      // 属于大转盘的承接页面
      if (fromUrl.value && fromUrl.value.indexOf("nineGrid") !== -1) {
        if (goToUrl.value) {
          // 监听当前页面栈的个数内容
          gotoPage(goToUrl.value, pagesLength, true)
        } else {
          if (pagesLength > 1) {
            goToUrl.value = ""
            uni.navigateBack({
              delta: 1
            })
          } else {
            uni.reLaunch({
              url: "/pages/home/<USER>"
            })
          }
        }
      }
    }
  })

  // 页面卸载
  onUnload(() => {
    if (fromUrl.value && fromUrl.value.indexOf("nineGrid") !== -1 && goToUrl.value) {
      uni.redirectTo({
        url: goToUrl.value
      })
    }
  })
</script>