<template>
  <view class="second-tab-page">
    <share-single-page v-if="scene == 1154" />
    <page-components v-else :components-list="componentsList"></page-components>
  </view>
</template>

<script setup lang="uts">
  import PageComponents from "@/page-components/index.uvue"
  import shareSinglePage from "@/components/share-single-page/index.uvue";
	import { pagedevisePage } from "@/page-components/div-components/api/index.uts";
  const pagePathId = ref('')
  const pageState = ref('')
  const scene = ref<number>(0)

  const componentsList = ref<ConfigPageData.ComponentsList>([]);
  onLoad(option => {
    // #ifdef MP-WEIXIN
    let getLaunchOptions = uni.getLaunchOptionsSync();
    scene.value = getLaunchOptions.scene;
    //场景值等于 1154 分享单页模式
    if (scene.value && scene.value == 1154) return;
    // #endif
    const tabBar = uni.getStorageSync('tabBar');
    /* 根据缓存中的tabbar pagePathId 字段判断 当前页面是展示微页面还是类别 */
    if (tabBar && tabBar[1] && tabBar[1].pagePathId > 0) {
      if (tabBar[1].pagePathId) {
        pagePathId.value = tabBar[1].pagePathId;
        pageState.value = 'micro';
      } else {
        pageState.value = 'home';
      }
    }
  })

  onMounted(async () => {
    const { data } = await pagedevisePage(pagePathId.value)
    const { componentsList: list } = data?.pageComponent || {}
    componentsList.value = list
  })

  onPullDownRefresh(() => {
    // 下拉刷新逻辑
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  })
  onReachBottom(() => { })
</script>

<style scoped>
  .second-tab-page {
    width: 100%;
    height: 100%;
  }

  .title {
    margin-top: 200rpx;
    display: flex;
    font-size: 24px;
    text-align: center;
  }
</style>