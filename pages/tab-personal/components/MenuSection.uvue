<template>
  <view class="cu-list menu card-menu all-orders bg-white mlr-xs ptb-xxsm" style="margin-top: 0 !important">
    <view v-for="(item, index) in menuList" :key="index">
      <view class="flex align-center">
        <view style="width: 138rpx" v-for="(menuItem, menuIndex) in menuList[index]" :key="menuIndex">
          <view
            @click="handleCheckLogin(menuItem.url)"
            v-if="
              (menuItem && menuItem.url && menuItem.label != '我的分销') ||
              (menuItem && menuItem.url && menuItem.label == '我的分销' && userInfo && userInfo.distributionUser && userInfo.distributionUser.userId > 0)
            "
            class="ptb-xxsm flex flex-direction align-center"
          >
            <image :src="menuItem.icon" class="size-60"></image>
            <view class="mt-s font-xmd">{{ menuItem.label }}</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  // 定义接口类型
  interface MenuItem {
    icon: string
    label: string
    url: string
  }

  interface UserInfo {
    distributionUser?: {
      userId: number
    }
    [key: string]: any
  }

  // Props 定义
  const props = defineProps<{
    menuList: MenuItem[][]
    userInfo: UserInfo
  }>()

  // Emits 定义
  const emit = defineEmits<{
    checkLogin: [url: string]
  }>()

  // 方法
  const handleCheckLogin = (url: string) => {
    emit('checkLogin', url)
  }
</script>

<style scoped lang="scss">
.all-orders {
  border-radius: 20rpx !important;
  margin: auto !important;
  margin-top: 15rpx !important;
}
</style>
