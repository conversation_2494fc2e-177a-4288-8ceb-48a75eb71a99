<template>
  <view class="cu-list menu card-menu all-orders margin-reduce bg-white">
    <!-- 我的订单 -->
    <view class="flex align-center justify-between bg-white mt-xxmd ml-xxxsm mr-xxs">
      <view class="action font-xxxmd text-bold text-black">我的订单</view>
      <view class="action text-88 font-md" @click="handleCheckLogin('/pages/order/order-list/index')" hover-class="none">
        全部
        <text class="cuIcon-right text-88"></text>
      </view>
    </view>
    <view class="bg-white mt-xsm mb-xsm mlr-xsm">
      <!-- 待付款、待发货、待收货、待评价、退换/售后 -->
      <view class="cu-list grid col-5 no-border" style="padding: 0rpx">
        <!-- 待付款 -->
        <view class="cu-item">
          <view @click="handleCheckLogin('/pages/order/order-list/index?status=0')" hover-class="none">
            <view class="text-black">
              <image :src="$imgUrl('live/user-center/icon-pay.png')" class="size-64"></image>
              <view v-if="orderCountAll[0] > 0" class="icon-num font-xxxxsm size-lg">
                {{ orderCountAll[0] }}
              </view>
            </view>
            <text class="user-text-xl font-md text-black">待付款</text>
          </view>
        </view>
        <!-- 待发货 -->
        <view class="cu-item">
          <view @click="handleCheckLogin('/pages/order/order-list/index?status=1')">
            <view class="text-black" style="margin-top: 0">
              <image :src="$imgUrl('live/user-center/icon-send.png')" class="size-64"></image>
              <view v-if="orderCountAll[1] > 0" class="icon-num font-xxxxsm size-lg">
                {{ orderCountAll[1] }}
              </view>
            </view>
            <text class="user-text-xl font-md text-black">待发货</text>
          </view>
        </view>
        <!-- 待收货 -->
        <view class="cu-item">
          <view @click="handleCheckLogin('/pages/order/order-list/index?status=2')">
            <view class="text-black" style="margin-top: 0">
              <image :src="$imgUrl('live/user-center/icon-deliver.png')" class="size-64"></image>
              <view v-if="orderCountAll[2] > 0" class="icon-num font-xxxxsm size-lg">
                {{ orderCountAll[2] }}
              </view>
            </view>
            <text class="user-text-xl font-md text-black">待收货</text>
          </view>
        </view>
        <!-- 待评价 -->
        <view class="cu-item">
          <view @click="handleCheckLogin('/pages/order/order-list/index?status=4')">
            <view class="text-black" style="margin-top: 0">
              <image :src="$imgUrl('live/user-center/icon-message.png')" class="size-64"></image>
              <view v-if="orderCountAll[3] > 0" class="icon-num  font-xxxxsm size-lg">
                {{ orderCountAll[3] }}
              </view>
            </view>
            <text class="user-text-xl font-md text-black">待评价</text>
          </view>
        </view>
        <!-- 退换/售后 -->
        <view class="cu-item">
          <view @click="handleCheckLogin('/pages/order/order-refunds/index')">
            <view class="text-black" style="margin-top: 0">
              <image :src="$imgUrl('live/user-center/icon-recharge.png')" class="size-64"></image>
              <view v-if="orderCountAll[4] > 0" class="icon-num  font-xxxxsm size-lg">
                {{ orderCountAll[4] }}
              </view>
            </view>
            <text class="user-text-xl font-md text-black">退换/售后</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  // Props 定义
  const props = defineProps<{
    orderCountAll: number[]
  }>()

  // Emits 定义
  const emit = defineEmits<{
    checkLogin: [url: string]
  }>()

  // 方法
  const handleCheckLogin = (url: string) => {
    emit('checkLogin', url)
  }
</script>

<style scoped lang="scss">
.icon-num {
  right: 20%;
  top: 8rpx;
  left: auto;
  background-color: red;
  border-radius: 50%;
  color: #ffffff;
  vertical-align: middle;
  position: absolute;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  black-space: nowrap;
  line-height: 1;
}

.user-text-xl {
  color: #000000 !important;
  margin: 0rpx !important;
}

.all-orders {
  border-radius: 20rpx !important;
  margin: auto !important;
  margin-top: 15rpx !important;
}

.margin-reduce {
  margin-top: -14rpx !important;
}
</style>
