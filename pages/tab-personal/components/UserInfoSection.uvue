<template>
  <view class="pb-xxxmd" style="background: linear-gradient(180deg, #fff4e9 0%, #fff1dc 26%, #ffe1c1 80%, #f7f7f7 100%)">
    <view class="flex justify-between mlr-xsm align-center">
      <navigator
        url="/pages/user/user-info/index"
        hover-class="none"
        v-if="userInfo && (userInfo.nickName || userInfo.headimgUrl)"
        class="flex align-center personal-information"
      >
        <image class="size-110 head" :src="userInfo.headimgUrl || $imgUrl('live/user-center/default_pic.png')"></image>
        <view class="content ml-md flex flex-direction">
          <view class="font-xxl text-bold">
            {{ userInfo.nickName || '' }}
          </view>
          <view class="text-88 font-md" v-if="userInfo.phone">手机号：{{ userInfo.phone | phoneEncryption }}</view>
        </view>
      </navigator>
      <navigator url="/pages/login/index" hover-class="none" v-else style="display: flex; align-items: center" class="personal-information">
        <image class="size-110 head" :src="$imgUrl('live/user-center/default_pic.png')"></image>
        <view class="font-xxl" style="padding-left: 20rpx; font-weight: 900">去登录</view>
      </navigator>

      <view
        @click="handleCheckLogin('/pages/user/user-address/list/index')"
        v-if="userInfo && (userInfo.nickName || userInfo.headimgUrl)"
        class="ml-md flex flex-direction align-center"
      >
        <image :src="$imgUrl('live/user-center/gray_address.png')" class="icon-settings" />
        <text class="text-88 font-md mt-sss">地址</text>
      </view>

      <view @click="handleCusServiceClick" class="ml-md flex flex-direction align-center">
        <image :src="$imgUrl('live/user-center/kefu.png')" class="icon-settings" />
        <text class="text-88 font-md mt-sss">客服</text>
      </view>

      <navigator
        url="/pages/user/user-info/index"
        hover-class="none"
        v-if="userInfo && (userInfo.nickName || userInfo.headimgUrl)"
        class="ml-md flex flex-direction align-center"
      >
        <image :src="$imgUrl('live/user-center/settings.png')" class="icon-settings" />
        <text class="text-88 font-md mt-sss">设置</text>
      </navigator>
    </view>
  </view>
</template>

<script setup lang="uts">
  // 定义接口类型
  interface UserInfo {
    nickName?: string
    headimgUrl?: string
    phone?: string
    erpCustTypename?: string
    [key: string]: any
  }

  // Props 定义
  const props = defineProps<{
    userInfo: UserInfo
  }>()

  // Emits 定义
  const emit = defineEmits<{
    checkLogin: [url: string]
    handleCusServiceClick: []
  }>()

  // 方法
  const handleCheckLogin = (url: string) => {
    emit('checkLogin', url)
  }

  const handleCusServiceClick = () => {
    emit('handleCusServiceClick')
  }
</script>

<style scoped lang="scss">
.icon-settings {
  width: 40rpx;
  height: 40rpx;
}

.personal-information {
  flex: 1;
  display: flex;
}

.head {
  border-radius: 50%;
}
</style>
