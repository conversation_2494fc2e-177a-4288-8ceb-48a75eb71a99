<template>
  <view class="mt-xsm mlr-xsm flex justify-between" v-if="userInfo && userInfo.erpCustTypename">
    <view style="flex: 1; border-radius: 10rpx" class="bg-white">
      <view class="flex justify-between more-tab" style="background: #fff7f7; border-radius: 17rpx">
        <view class="reduce-bg flex font-xxxxsm text-white padding align-center">
          <text>为您节省</text>
          <price-handle v-if="recommendCoupon" color="#ffffff" :value="recommendCoupon.discount" signFont="20rpx" bigFont="36rpx" smallFont="20rpx"></price-handle>
        </view>
        <view class="flex align-center justify-end" style="flex: 1" @click="handleCheckLogin('/pages/coupon/coupon-centre/index')">
          <text class="font-xmd text-center" style="color: #f32a20; flex: 1">领券中心</text>
          <image :src="$imgUrl('live/user-center/arrow-right.png')" class="icon-arrow margin-lr-xs" />
        </view>
      </view>
      <view class="flex m-xxsm justify-around">
        <view style="flex: 1" class="flex flex-direction align-center" @click="handleCheckLogin('/pages/signrecord/signrecord-info/index')">
          <view class="text-88 font-md">积分</view>
          <view class="pay-num font-xxl mt-xxxs">
            {{ userInfo.pointsCurrent || '0' }}
          </view>
        </view>

        <view style="flex: 1" class="flex flex-direction align-center" @click="handleCheckLogin('/pages/coupon/coupon-user-list/index')">
          <text class="text-88 font-md">优惠劵</text>
          <view class="pay-num font-xxl mt-xxxs">
            {{ userInfo.couponNum ? userInfo.couponNum : 0 }}
            <text class="font-md">张</text>
          </view>
        </view>

        <view style="flex: 1" class="flex flex-direction align-center" @tap="handleIsGo">
          <text class="text-88 font-md">零钱</text>
          <view class="pay-num mt-xxxs">
            <format-price :styleProps="priceStyle" color="#000" :price="availableBalance" priceFontSize="36rpx" />
          </view>
        </view>

        <view style="flex: 1" class="flex flex-direction align-center" @click="handleCheckLogin('/pages/gift/gift-card/index')">
          <text class="text-88 font-md">礼品卡</text>
          <view class="pay-num font-xxl mt-xxxs">
            {{ num }}
            <text class="font-md">张</text>
          </view>
        </view>
      </view>
      <!-- 券类型 1线上券 2线下券 -->
      <view
        v-if="recommendCoupon"
        @click="
          handleCheckLogin(
            recommendCoupon.type == 2
              ? '/pages/coupon/coupon-offline-detail-plus/index?id=' + recommendCoupon.couponId
              : recommendCoupon.type == 1?  '/pages/goods/goods-list/index?couponId=' + recommendCoupon.couponId :  '/pages/coupon/coupon-detail/index?id=' + recommendCoupon.couponId+'&toUse=true&couponUser=null'
          )
        "
        class="flex justify-between align-center plr-xxmd pt-xsm pb-xxxsm m-sss"
        style="background: #fff7f7; border-radius: 14rpx"
      >
        <image :src="$imgUrl('live/user-center/ic_coupon.png')" class="coupon-image margin-right-xs" />
        <view class="flex flex-direction padding-left-xs" style="flex: 1">
          <view class="text-black font-xmd overflow-1">
            {{ recommendCoupon.couponName }}
          </view>
          <view class="font-md text-red overflow-1">{{ recommendCoupon.usageDesc }}</view>
        </view>
        <view class="use-btn flex justify-center align-center font-md">{{recommendCoupon.type == 3? '去领取': '去使用'}}</view>
      </view>
      <!-- 占位置使用-->
      <view v-else class="size-54"></view>
    </view>

    <!-- 一码付内容 -->
    <view
      style="border-radius: 10rpx"
      @click="handleCheckLogin('/pages/user/user-qrcode/index')"
      class="bg-white ml-xxxs flex flex-direction justify-between align-center"
    >
      <view class="vip-bg more-tab font-xxmd text-white text-center" :class="'member-bg-' + userInfo.erpCustType" style="padding-right: 0">{{userInfo.erpCustTypename || 'VIP会员'}}</view>
      <image :src="memberLevel[userInfo.erpCustType]" class="size-64 mtb-xxsm" />
      <view class="flex flex-direction justify-between align-center plr-xxmd pt-xsm pb-xxxsm m-sss" :class="'member-color-'+userInfo.erpCustType">
        <view class="text-black font-xmd">卡号 | 一码付</view>
        <!-- <view class="font-md">{{ userInfo.erpCustTypename }}</view> -->
      </view>
    </view>
  </view>
</template>

<script setup lang="uts">
  import formatPrice from '@/components/format-price/index.uvue'
  import priceHandle from '@/components/price-handle/index.uvue'

  // 定义接口类型
  interface UserInfo {
    erpCustTypename?: string
    pointsCurrent?: number
    couponNum?: number
    erpCustType?: number
    [key: string]: any
  }

  interface RecommendCoupon {
    discount?: number
    type?: number
    couponId?: string
    couponName?: string
    usageDesc?: string
  }

  // Props 定义
  const props = defineProps<{
    userInfo: UserInfo
    recommendCoupon: RecommendCoupon | null
    availableBalance: string | number
    num: number
    memberLevel: Record<number, string>
    priceStyle: string
  }>()

  // Emits 定义
  const emit = defineEmits<{
    checkLogin: [url: string]
    isGo: []
  }>()

  // 方法
  const handleCheckLogin = (url: string) => {
    emit('checkLogin', url)
  }

  const handleIsGo = () => {
    emit('isGo')
  }
</script>

<style scoped lang="scss">
.pay-num {
  font-weight: bold;
  color: #000;
  line-height: 1;
}

.more-tab {
  height: 56rpx;
  line-height: 56rpx;
}

.reduce-bg,
.vip-bg {
  background-image: url(https://img.songlei.com/live/user-center/more-tab.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding-right: 60rpx;
}

.vip-bg {
  background: linear-gradient(to right, #e0ba9b, #d2a28a);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border-radius: 10rpx;
  width: 100%;
}

.icon-arrow {
  width: 10rpx;
  height: 16rpx;
}

.coupon-image {
  width: 68rpx;
  height: 68rpx;
}

.use-btn {
  width: 112rpx;
  height: 48rpx;
  background: #f32a20;
  border-radius: 24rpx;
  color: #ffffff;
}

.member-bg-101 {
  background: linear-gradient(126deg, #ccac92 0%, #ff678b 0%, #fc3f6b 100%);
}

.member-bg-102 {
  background: linear-gradient(to right, #ccac92, #d2a28a);
}

.member-bg-103 {
  background: linear-gradient(126deg, #ccac92 0%, #a4a4a4 0%, #888888 100%);
}

.member-bg-108 {
  background: linear-gradient(126deg, #403e3e 0%, #191617 100%);
}

.member-color-101 {
  color: #FF678A;
  background: #FFF7F7;
  border-radius: 14rpx;
}

.member-color-102 {
  color: #A4A4A4;
  background: #F3F3F3;
  border-radius: 14rpx;
}

.member-color-103 {
  color: #BE896E;
  background: #FFF7F7;
  border-radius: 14rpx;
}

.member-color-104 {
  color: #000000;
  background: #F2F2F2;
  border-radius: 14rpx;
}
</style>
