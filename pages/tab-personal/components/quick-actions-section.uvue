<template>
  <!-- 快递、收藏、足迹 -->
  <view class="cu-list menu card-menu g-white pt-xxxxsm pl-md all-orders bg-white">
    <scroll-view scroll-x="true" style="overflow: hidden; width: 100%">
      <view class="cu-list flex no-border" style="padding: 0rpx">
        <!-- 红包雨活动 -->
        <view v-if="redPacketInfo && redPacketInfo.isPop" class="cu-item line-before collect-tab mr-xxsm" @click="toPage('/pages/home/<USER>')">
          <view class="text-black font-xxmd text-bold text-left">红包雨活动</view>
          <view class="text-left font-md mt-xxs" style="color: #999999">今日剩余{{ redPacketInfo.dayDrawNum }}次机会</view>
          <view class="mt-xxxsm">
            <image src="http://img.songlei.com/live/home/<USER>" mode="aspectFill" class="row-img row-tab-img size-198 margin-top-sm" />
            <view class="mt-xsm font-md text-black">参与人数</view>
            <view class="mb-xsm mt-s font-md text-cut" style="color: #999999">{{redPacketInfo.drawPeopleNum}}+</view>
          </view>
        </view>
        <view class="cu-item line-before collect-tab mr-xxsm" @click="handleCheckLogin('/pages/order/order-list/index')">
          <view class="text-black font-xxmd text-bold text-left">快递</view>
          <view v-if="rollList.logisticsList && rollList.logisticsList.length">
            <view class="text-left font-md mt-xxs" style="color: #999999">{{ rollList.logisticsList.length }}个快递更新</view>
            <swiper class="mt-xxxsm tab-swiper" circular autoplay vertical :interval="3000" :duration="500" easing-function="linear">
              <swiper-item
                v-for="item in rollList.logisticsList"
                :key="item.id"
                style="position: relative"
                @click.stop="handleCheckLogin(`/pages/order/order-detail/index?id=${item.orderIds}`)"
              >
                <image :src="item.picUrl || $imgUrl('live/img/no_pic.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
                <view class="mt-xsm font-md text-black">{{ item.statusDesc }}</view>
                <view class="mb-xsm mt-s font-md text-cut" style="color: #999999">
                  {{ item.routeInfo }}
                </view>
              </swiper-item>
            </swiper>
          </view>
          <view v-else>
            <view class="text-left font-md mt-xxs" style="color: #999999">您的快递</view>
            <view class="mt-xxxsm">
              <image src="http://img.songlei.com/rank/express-nodata.png" mode="aspectFill" class="row-img row-tab-img size-198 margin-top-sm" />
              <view class="mt-xsm font-md text-black">暂无快递</view>
              <view class="mb-xsm mt-s font-md text-cut" style="color: #999999">快去下单吧</view>
            </view>
          </view>
        </view>
        <view class="cu-item collect-tab mr-xxsm" @click="handleCheckLogin('/pages/user/user-collect/index')">
          <view class="text-black font-xxmd text-bold text-left">收藏</view>
          <view v-if="rollList.collectList && rollList.collectList.length">
            <view class="text-left font-md mt-xxs" style="color: #999999">查看最近收藏</view>
            <swiper class="mt-xxxsm tab-swiper" circular autoplay vertical :interval="3500" :duration="600" easing-function="linear">
              <swiper-item
                v-for="item in rollList.collectList"
                :key="item.id"
                @click.stop="handleCheckLogin(`/pages/goods/goods-detail/index?id=${item.goodsSpu.id}&source_module=${encodeURIComponent('个人中心')}`)"
              >
                <image :src="item.goodsSpu.picUrls[0] || $imgUrl('live/img/no_pic.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
                <view class="mt-xsm font-md text-black">
                  <text class="margin-0" v-if="item.collectNum > 100">有眼光</text>
                  <text class="margin-0" v-else-if="item.collectNum > 10 && item.collectNum < 100">有潜力</text>
                  <text class="margin-0" v-else-if="item.collectNum < 10">有个性</text>
                </view>
                <view class="mb-xsm mt-s font-md tip">{{ item.collectNum }}+人收藏</view>
              </swiper-item>
            </swiper>
          </view>
          <view v-else>
            <view class="text-left font-md mt-xxs" style="color: #999999">查看最近的收藏</view>
            <view class="mt-xxxsm">
              <image :src="$imgUrl('rank/collection-nodata.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
              <view class="mt-xsm font-md text-black">暂无收藏</view>
              <view class="mb-xsm mt-s font-md text-cut" style="color: #999999">快去收藏商品吧</view>
            </view>
          </view>
        </view>
        <view class="cu-item collect-tab mr-xxsm" @click="handleCheckLogin('/pages/user/user-footprint/index')">
          <view class="text-black font-xxmd text-bold text-left">足迹</view>
          <view v-if="rollList.footprintList && rollList.footprintList.length">
            <view class="text-left font-md mt-xxs" style="color: #999999">看过的商品</view>
            <swiper class="mt-xxxsm tab-swiper" circular vertical autoplay :interval="4000" :duration="700" easing-function="linear">
              <swiper-item
                v-for="item in rollList.footprintList"
                :key="item.id"
                @click.stop="handleCheckLogin(`/pages/goods/goods-detail/index?id=${item.goodsSpu.id}`)"
              >
                <image :src="item.goodsSpu.picUrls[0] || $imgUrl('live/img/no_pic.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
                <view class="mtb-xsm font-md overflow-2 text-black">
                  {{ item.goodsSpu.name }}
                </view>
              </swiper-item>
            </swiper>
          </view>
          <view v-else>
            <view class="text-left font-md mt-xxs" style="color: #999999">看过的商品</view>
            <view class="mt-xxxsm">
              <image :src="$imgUrl('rank/footprints-nodata.png')" mode="aspectFit" class="row-img row-tab-img margin-top-sm size-198" />
              <view class="mt-xsm font-md text-black">暂无足迹</view>
              <view class="font-md text-cut mb-xsm mt-s" style="color: #999999">快去逛逛吧</view>
            </view>
          </view>
        </view>
        <view class="cu-item collect-tab pr-md" @tap="handleIsGo">
          <view class="text-black font-xxmd text-bold text-left">我的钱包</view>
          <view>
            <view class="text-left font-md font-md mt-xxs" style="color: #999999">我的资产</view>
            <view class="mt-xxxsm">
              <image :src="$imgUrl('live/user-center/tab_wallet.png')" mode="aspectFill" class="row-img row-tab-img size-198" />
              <view class="mt-xsm font-md text-black">钱包余额</view>
              <view class="font-md text-cut tip mb-xsm mt-s">
                {{ availableBalance == '' || availableBalance == undefined ? '马上开通' : '￥' + availableBalance }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="uts">
  // 定义接口类型
  interface RedPacketInfo {
    isPop?: boolean
    dayDrawNum?: number
    drawPeopleNum?: number
  }

  interface RollList {
    logisticsList?: any[]
    collectList?: any[]
    footprintList?: any[]
  }

  // Props 定义
  const props = defineProps<{
    redPacketInfo: RedPacketInfo
    rollList: RollList
    availableBalance: string | number
  }>()

  // Emits 定义
  const emit = defineEmits<{
    checkLogin: [url: string]
    isGo: []
    toPage: [url: string]
  }>()

  // 方法
  const handleCheckLogin = (url: string) => {
    emit('checkLogin', url)
  }

  const handleIsGo = () => {
    emit('isGo')
  }

  const toPage = (url: string) => {
    emit('toPage', url)
  }
</script>

<style scoped lang="scss">
.collect-tab {
  width: 198rpx;
  min-width: 198rpx;
  .row-tab-img {
    display: block;
    border-radius: 15rpx;
  }
  .tab-swiper {
    height: 300rpx;
  }
}

.all-orders {
  border-radius: 20rpx !important;
  margin: auto !important;
  margin-top: 15rpx !important;
}
</style>
