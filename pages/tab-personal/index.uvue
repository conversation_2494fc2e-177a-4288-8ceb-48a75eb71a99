<template>
	<view>
		<cu-custom bgColor="#FFF4E9" :hideMarchContent="true"></cu-custom>
		<view v-if="scene != 1154" style="padding-bottom: 110rpx">
			<user-info-section
				:user-info="userInfo"
				@check-login="checkLogin"
				@handle-cus-service-click="handleCusServiceClick"
			/>
			<wallet-section
				:user-info="userInfo"
				:recommend-coupon="recommendCoupon"
				:available-balance="availableBalance"
				:num="num"
				:member-level="memberLevel"
				:price-style="priceStyle"
				@check-login="checkLogin"
				@is-go="isGo"
			/>
			</view>
			<view class="mlr-xsm">
				<order-section
					:order-count-all="orderCountAll"
					@check-login="checkLogin"
				/>
			</view>

			<quick-actions-section
				:red-packet-info="redPacketInfo"
				:roll-list="rollList"
				:available-balance="availableBalance"
				@check-login="checkLogin"
				@is-go="isGo"
				@to-page="toPage"
			/>

				<!-- 广告配置 -->
				<advert styleProps="height: 196rpx;border-radius: 20rpx !important;margin: auto !important;margin-top: 15rpx !important;" searchKey="USER_CENTER" />

				<view class="mine" style="margin-bottom: 15rpx !important; margin-top: 10rpx !important">
					<official-account></official-account>
				</view>

			<menu-section
				:menu-list="menuList"
				:user-info="userInfo"
				@check-login="checkLogin"
			/>
			</view>
			<recommendComponents canLoad />
		</view>
		<share-single-page v-if="scene == 1154" />
	</view>
</template>

<script setup lang="uts">
import api from '@/utils/api.uts'
import util from '@/utils/util.uts'
import __config from '@/config/env'
import shareSinglePage from '@/components/share-single-page/index.uvue'
import recommendComponents from '@/components/recommend-components/index'
import { getPaypayCode } from '@/api/gift'
import { getWxTemplate } from '@/api/message.js'
import { getRoll } from '@/api/logisticsMy.js'
import { getHomePop } from '@/components/div-components/div-red-packet-dialog/api/redPacketApi'
import { useGlobalDataStore } from '@/stores/globalData'
import { useSystemStore } from '@/stores/system'
import { initPage } from '@/utils/login.uts'
import { navigateUtil } from '@/static/mixins/navigateUtil.js'

// Import business components
import userInfoSection from './components/user-info-section.uvue'
import orderSection from './components/order-section.uvue'
import quickActionsSection from './components/quick-actions-section.uvue'
import menuSection from './components/menu-section.uvue'
import walletSection from './components/wallet-section.uvue'

// Store instances
const globalDataStore = useGlobalDataStore()
const systemStore = useSystemStore()

// Computed properties from stores
const CustomBar = computed(() => systemStore.CustomBar)
const StatusBar = computed(() => systemStore.StatusBar)
// Define interfaces
interface UserInfo {
	nickName?: string
	headimgUrl?: string
	phone?: string
	erpCustTypename?: string
	pointsCurrent?: number
	couponNum?: number
	erpCustType?: number
	distributionUser?: {
		userId: number
	}
	[key: string]: any
}

interface MenuItem {
	icon: string
	label: string
	url: string
}

interface RedPacketInfo {
	isPop?: boolean
	dayDrawNum?: number
	drawPeopleNum?: number
}

interface RollList {
	logisticsList?: any[]
	collectList?: any[]
	footprintList?: any[]
}

interface RecommendCoupon {
	discount?: number
	type?: number
	couponId?: string
	couponName?: string
	usageDesc?: string
}

// Reactive data
const theme = ref(globalDataStore.theme)
const userInfo = ref<UserInfo>({})
const distributionConfig = ref({
	enable: '1'
})
const shareUser = ref({})
const orderCountAll = ref<number[]>([])
const showPrivacyPolicy = ref(__config.showPrivacyPolicy)
const privacyPolicyUrl = ref(__config.privacyPolicyUrl)
const protocolUrl = ref(__config.protocolUrl)
const canIUseGetUserProfile = ref(false)

const isWeiXinBrowser = ref(util.isWeiXinBrowser())
const scene = ref<string | number>('')
const page_title = ref('个人中心')
const availableBalance = ref<string | number>('')
const logisticsList = ref<any[]>([])
const AuthorInformation = ref(false)

const background = ref(['color1', 'color2', 'color3'])
const autoplay = ref(true)
const interval = ref(4000)
const duration = ref(500)
const priceStyle = ref('display: flex; justify-content: center; font-weight: bold; align-items: baseline;')
const cusServicePageUrl = ref('')
const menuList = ref<MenuItem[][]>([
	[
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/parking.png' : 'https://img.songlei.com/live/user-center/parking.png',
			label: '停车场',
			url: '/pages/parkinglot/parking-home/index'
		},
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/signrecord.png' : 'https://img.songlei.com/live/user-center/signrecord.png',
			label: '积分签到',
			url: '/pages/signrecord/signrecord-info/index'
		},
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/bargain.png' : 'https://img.songlei.com/live/user-center/bargain.png',
			label: '我的砍价',
			url: '/pages/bargain/bargain-user-list/index'
		},
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/mycardbag.png' : 'https://img.songlei.com/live/user-center/mycardbag.png',
			label: '我的卡包',
			url: '/pages/user/mycardbag/mycardbag'
		},
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/coupon.png' : 'https://img.songlei.com/live/user-center/coupon.png',
			label: '会员权益',
			url: '/pages/micro-page/index?id=1624942147010723842'
		}
	],
	[
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/fpzx.png' : 'https://img.songlei.com/live/user-center/fpzx.png',
			label: '发票中心',
			url: '/pages/invoice/invoiceCenter/index'
		},
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/appraises.png' : 'https://img.songlei.com/live/user-center/appraises.png',
			label: '我的评价',
			url: '/pages/user/user-appraises/index'
		},
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/huiyuanhuodong.png' : 'https://img.songlei.com/live/user-center/huiyuanhuodong.png',
			label: '会员活动',
			url: '/pages/memberactivity/list/index'
		},
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/pack.png' : 'https://img.songlei.com/live/user-center/pack.png',
			label: '存包柜',
			url: '/pages/storagelocker/order?origins=my'
		},
		{
			icon: 'https://img.songlei.com/live/user-center/my-prize.png',
			label: '我的奖品',
			url: '/pages/myPrize/index'
		}
	],
	[
		{
			icon: uni.getStorageSync('imgUrl') ? uni.getStorageSync('imgUrl') + 'live/user-center/distribution.png' : 'https://img.songlei.com/live/user-center/distribution.png',
			label: '我的分销',
			url: '/pages/distribution/distribution-center/index'
		}
	]
])

const num = ref(0) // 礼品卡数量
const rollList = ref<RollList>({}) // 快递，收藏，足迹数据
const recommendCoupon = ref<RecommendCoupon | null>(null)
const customerGrade = ref({})

// 会员级别
const memberLevel = ref({
	101: 'https://img.songlei.com/live/appraises/qr-101.png',
	102: 'https://img.songlei.com/live/appraises/qr-102.png',
	103: 'https://img.songlei.com/live/appraises/qr-103.png',
	108: 'https://img.songlei.com/live/appraises/qr-108.png'
})

const redPacketInfo = ref<RedPacketInfo>({}) // 红包雨

// Additional reactive data that might be needed
const advertInfo = ref<any>(null)
const recommendList = ref<any[]>([])

// Computed properties
const isUserLoggedIn = computed(() => util.isUserLogin())

// Methods
const shoppingCartCount = () => {
	// Replace app.shoppingCartCount() with store-based implementation
	// This would typically update a global cart count
	console.log('Shopping cart count updated')
}

const userInfoGet = async () => {
	try {
		const res = await api.userInfoGet()
		userInfo.value = res.data || {}
	} catch (error) {
		console.error('获取用户信息失败:', error)
	}

	// 分销设置
	try {
		const res = await api.distributionConfig()
		if (res.data) {
			distributionConfig.value = res.data
		}
	} catch (error) {
		console.error('获取分销配置失败:', error)
	}
}

const orderCountAllFun = async () => {
	try {
		const res = await api.orderCountAll()
		orderCountAll.value = res.data
	} catch (error) {
		console.error('获取订单统计失败:', error)
	}
}

const getPaypayCodeFun = async () => {
	try {
		const res = await getPaypayCode()
		if (res.code == 0 && res.data) {
			num.value = res.data.num || 0
		}
	} catch (error) {
		console.error('获取礼品卡数量失败:', error)
	}
}

const isAccount = async () => {
	try {
		const res = await api.isAccount()
		const Account = res.data
		if (Account != null && Account.length != '0') {
			// 获取余额
			getAccountQuery()
		}
		uni.setStorageSync('Account', Account)
	} catch (error) {
		console.error('检查账户状态失败:', error)
	}
}

const getAccountQuery = async () => {
	try {
		const res = await api.getAccountQuery()
		if (res.data) {
			availableBalance.value = res.data.available_balance
		}
	} catch (error) {
		console.error('获取账户余额失败:', error)
	}
}

const getRollList = async () => {
	try {
		const res = await getRoll()
		if (res.code == 0) {
			rollList.value = res.data || {}
		}
	} catch (error) {
		console.error('获取快递收藏足迹数据失败:', error)
	}
}

const getRecommendCoupon = async () => {
	try {
		const res = await api.getRecommendCoupon()
		if (res.code == 0) {
			recommendCoupon.value = res.data
		}
	} catch (error) {
		console.error('获取推荐优惠券失败:', error)
	}
}

const getRedPacket = async () => {
	try {
		const res = await getHomePop()
		if (res.code == 0) {
			redPacketInfo.value = res.data || {}
		}
	} catch (error) {
		console.error('获取红包雨信息失败:', error)
	}
}

const getCustomerServiceInfo = async () => {
	try {
		const res = await api.getCustomerServiceInfo()
		if (res.code == 0 && res.data) {
			cusServicePageUrl.value = res.data.pageUrl || ''
		}
	} catch (error) {
		console.error('获取客服信息失败:', error)
	}
}

const advertisement = async (type: string) => {
	try {
		const res = await api.advertisement(type)
		if (res.code == 0 && res.data) {
			if (type === 'SHARE_USER_CENTER') {
				shareUser.value = res.data
			}
		}
	} catch (error) {
		console.error('获取广告信息失败:', error)
	}
}

const userInfoUpdateByMp = async (params: any) => {
	try {
		const res = await api.userInfoUpdateByMp(params)
		if (res.code == 0) {
			console.log('用户信息更新成功')
		}
	} catch (error) {
		console.error('更新用户信息失败:', error)
	}
}

// Event handlers
const checkLogin = (url: string) => {
	initPage().then(() => {
		if (util.isUserLogin()) {
			uni.navigateTo({ url })
		} else {
			uni.navigateTo({ url: '/pages/login/index' })
		}
	})
}

const handleCusServiceClick = () => {
	if (cusServicePageUrl.value) {
		uni.navigateTo({
			url: `/pages/web-view/index?url=${encodeURIComponent(cusServicePageUrl.value)}`
		})
	} else {
		uni.showToast({
			title: '客服暂不可用',
			icon: 'none'
		})
	}
}

const isGo = () => {
	checkLogin('/pages/user/user-wallet/index')
}

const toPage = (url: string) => {
	uni.navigateTo({ url })
}

// Lifecycle hooks
onLoad((option: any) => {
	// #ifdef MP-WEIXIN
	const getLaunchOptions = uni.getLaunchOptionsSync()
	scene.value = getLaunchOptions.scene
	// 场景值等于 1154 分享单页模式
	if (scene.value && scene.value == 1154) return
	// #endif
	// 广告
	advertisement('USER_CENTER')
	// 分享朋友
	advertisement('SHARE_USER_CENTER')
	// 获取客服链接
	getCustomerServiceInfo()

	// #ifdef MP-WEIXIN
	if (uni.getUserProfile) {
		canIUseGetUserProfile.value = true
	}
	// #endif
	// #ifdef H5
	const code = option.code
	const state = option.state
	// 授权code获取用户信息
	if (code && state == 'snsapi_userinfo_update') {
		// 有code
		userInfoUpdateByMp({
			jsCode: code,
			scope: state
		})
	}
	// #endif
})

onShow(() => {
	shoppingCartCount()
	if (scene.value && scene.value == 1154) return

	if (uni.getStorageSync('WALLET_CLOSE') == 'close') {
		uni.removeStorageSync('USER_WALLET_INFO')
		uni.removeStorageSync('Authentication')
		uni.removeStorageSync('WALLET_CLOSE')
	}

	initPage().then(() => {
		userInfoGet()
		orderCountAllFun()
		getPaypayCodeFun()
		// 开通
		isAccount()
		console.log('用户登录查看数据', uni.getStorageSync('user_info'))

		if (util.isUserLogin()) {
			// 同步数据至erp
			api.erpOpenIdBind()
			// 获取快递、收藏、足迹数据
			getRollList()
			getRecommendCoupon()
			// 获取红包雨参与次数人数
			getRedPacket()
		}
	})

	// Version check logic would need to be adapted for UTS
	// if (version.getversion() == '1') {
	// 	AuthorInformation.value = true
	// } else {
	// 	AuthorInformation.value = false
	// }
})

// 分享朋友
onShareAppMessage(() => {
	const shareUserData = shareUser.value
	const title = shareUserData.name
	const linkUrl = shareUserData.linkUrl
	const imageUrl = shareUserData.imgUrl + '-jpg_w360_q90' || ''
	const userInfoData = uni.getStorageSync('user_info')
	const userCode = userInfoData ? '&type=1&sharer_user_code=' + userInfoData.userCode : ''
	const path = `${linkUrl}?userId=` + userInfo.value.id + userCode

	return {
		title: title ? title : null,
		path: linkUrl ? path : null,
		imageUrl: imageUrl ? imageUrl : null,
		success: function (res: any) {
			uni.showToast({
				title: '分享成功'
			})
		},
		fail: function (res: any) {
			// 转发失败
			uni.showToast({
				title: '分享失败',
				icon: 'none'
			})
		}
	}
})

// Additional methods that were in the old methods section
const payCode = () => {
	uni.showToast({
		icon: 'none',
		title: '功能研发中...敬请期待!',
		duration: 2000
	})
}

const isGoNext = () => {
	let url = '/pages/wallet/personal-information/index'
	if (uni.getStorageSync('Account') != null && uni.getStorageSync('Account').length != 0) {
		url = '/pages/wallet/my-balance/index'
	}
	if (!util.isUserLogin()) {
		uni.navigateTo({
			url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
		})
		return
	} else {
		uni.navigateTo({
			url
		})
	}
}

// Update the isGo method to use the new composition API
const isGoUpdated = () => {
	getWxTemplate({
		type: 6
	}).then((res: any) => {
		// #ifdef MP
		uni.requestSubscribeMessage({
			tmplIds: res.data,
			complete: () => {
				isGoNext()
			}
		})
		// #endif
		// #ifndef MP
		isGoNext()
		// #endif
	})
}

// Update handleCusServiceClick to use composition API
const handleCusServiceClickUpdated = () => {
	// #ifdef MP
	// @ts-ignore
	wx.openCustomerServiceChat({
		extInfo: {
			url: cusServicePageUrl.value
		},
		corpId: __config.chatId,
		success(res: any) {}
	})
	// #endif
	// #ifdef APP
	uni.share({
		provider: 'weixin',
		scene: 'WXSceneSession',
		openCustomerServiceChat: true,
		corpid: __config.chatId,
		customerUrl: cusServicePageUrl.value,
		fail(err: any) {
			console.log('打开客服错误', err)
		}
	})
	// #endif
}

// 钱包相关方法
const toWallet = () => {
	if (uni.getStorageSync('Account') != null && uni.getStorageSync('Account').length == 0) {
		// 填写个人信息
		uni.navigateTo({
			url: '/pages/wallet/personal-information/index'
		})
		return
	} else {
		// 如果开通和认证了就去钱包页面
		uni.navigateTo({
			url: '/pages/wallet/wallet-pages/index'
		})
	}
}

// Additional user profile methods for MP-WEIXIN
// #ifdef MP-WEIXIN
const agreeGetUser = (e: any) => {
	if (e.detail.errMsg == 'getUserInfo:ok') {
		initPage(true).then(() => {
			api.userInfoUpdateByMa(e.detail).then((res: any) => {
				userInfo.value = res.data
				uni.setStorageSync('user_info', userInfo.value)
				userInfoGet()
			})
		})
	}
}

const getUserProfile = (e: any) => {
	// @ts-ignore
	wx.getUserProfile({
		desc: '用于完善会员资料',
		success: (detail: any) => {
			initPage(true).then(() => {
				api.userInfoUpdateByMa(detail).then((res: any) => {
					userInfo.value = res.data
					uni.setStorageSync('user_info', userInfo.value)
					userInfoGet()
				})
			})
		}
	})
}
// #endif

// #ifdef H5
const updateUserInfo = () => {
	if (util.isWeiXinBrowser()) {
		// 微信公众号H5，页面授权获取用户详情信息
		const appId = globalDataStore.appId
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		let redirectUri = location.href
		const componentAppId_str = globalDataStore.componentAppId ? '&component_appid=' + globalDataStore.componentAppId : ''
		redirectUri = encodeURIComponent(redirectUri)
		const wx_url =
			'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
			appId +
			'&redirect_uri=' +
			redirectUri +
			componentAppId_str +
			'&response_type=code&scope=snsapi_userinfo&state=snsapi_userinfo_update#wechat_redirect'
		location.href = wx_url
	}
}
// #endif

const handleReCoupon = (coupon: any) => {
	if (coupon.type == 1) {
		checkLogin('/pages/coupon/coupon-offline-detail-plus/index?id=' + coupon.couponId)
	} else {
		checkLogin('/pages/coupon/coupon-detail/index?id=' + coupon.couponId)
	}
}

// Update checkLogin to use composition API (already defined above, but ensuring it handles all cases)
const checkLoginUpdated = (url: string) => {
	if (!url) {
		uni.showToast({
			title: '开发中，敬请期待',
			icon: 'none',
			duration: 2000
		})
		return
	}
	if (!util.isUserLogin()) {
		uni.navigateTo({
			url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
		})
		return
	} else {
		if (
			'/pages/signrecord/signrecord-info/index' === url ||
			'/pages/coupon/coupon-user-list/index' === url ||
			'/pages/gift/gift-card/index' === url ||
			'/pages/distribution/distribution-center/index' === url
		) {
			let type = 3
			switch (url) {
				case '/pages/signrecord/signrecord-info/index':
					type = 3
					break
				case '/pages/coupon/coupon-user-list/index':
					type = 4
					break
				case '/pages/gift/gift-card/index':
					type = 5
					break
				case '/pages/distribution/distribution-center/index':
					type = 7
					break
			}
			getWxTemplate({ type }).then((res: any) => {
				// #ifdef MP
				uni.requestSubscribeMessage({
					tmplIds: res.data,
					complete: () => {
						uni.navigateTo({ url })
					}
				})
				// #endif
				// #ifndef MP
				uni.navigateTo({ url })
				// #endif
			})
		return
	}
	uni.navigateTo({ url })
}
</script>

<style scoped lang="scss">
.icon-settings {
	width: 40rpx;
	height: 40rpx;
}

.collect-layout {
	background-color: #f3f3f3;
	border-top-left-radius: 28rpx;
	border-top-right-radius: 28rpx;
}

.collect-item {
	height: 70rpx;
}

.order-item {
	font-size: 64rpx;
	margin-top: 0;
}

.icon-num {
	right: 20%;
	top: 8rpx;
	left: auto;
	background-color: red;
	border-radius: 50%;
	color: #ffffff;
	vertical-align: middle;
	position: absolute;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	black-space: nowrap;
	line-height: 1;
}

.pay-num {
	font-weight: bold;
	color: #000;
	line-height: 1;
}

.result-img {
	margin: 0 auto;
	margin-top: 15rpx;
	margin-bottom: 15rpx;
	overflow: hidden;
}

.img {
	/* height: 296rpx; */
	width: 100%;
	height: auto;
	border-radius: 20rpx;
	border: 1px solid red;
}

.user-text-phone {
	font-size: 26rpx !important;
}

.user-text-xl {
	color: #000000 !important;
	margin: 0rpx !important;
}

.user-font-md {
	color: #000000;
}

.user-padding {
	padding: 10rpx;
	margin-bottom: 12rpx;
}

.user-btn-sm {
	font-size: 22rpx;
	height: 36rpx;
	margin-left: 10rpx;
}

.user-line {
	margin: 14rpx auto;
	width: 699rpx;
	border-bottom: 2rpx dashed #eee;
}

.personal-information {
	flex: 1;
	display: flex;
}

.head {
	border-radius: 50%;
}

.all-orders {
	border-radius: 20rpx !important;
	margin: auto !important;
	margin-top: 15rpx !important;
}

.mine {
	margin: auto !important;
	border-radius: 10rpx !important;
}

.font-24 {
	font-size: 25rpx;
}

.more-tab {
	height: 56rpx;
	line-height: 56rpx;
}

.reduce-bg,
.vip-bg {
	background-image: url(https://img.songlei.com/live/user-center/more-tab.png);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	padding-right: 60rpx;
}

.vip-bg {
	background: linear-gradient(to right, #e0ba9b, #d2a28a);
	background-repeat: no-repeat;
	background-size: 100% 100%;
	border-radius: 10rpx;
	width: 100%;
}

.icon-arrow {
	width: 10rpx;
	height: 16rpx;
}

.coupon-image {
	width: 68rpx;
	height: 68rpx;
}

.use-btn {
	width: 112rpx;
	height: 48rpx;
	background: #f32a20;
	border-radius: 24rpx;
	color: #ffffff;
}

.order-margin {
	margin-top: -10rpx;
}

.collect-tab {
	width: 198rpx;
	min-width: 198rpx;
	.row-tab-img {
		display: block;
		border-radius: 15rpx;
	}
	.tab-swiper {
		height: 300rpx;
	}
}

.margin-reduce {
	margin-top: -14rpx !important;
}

.member-bg-101 {
	background: linear-gradient(126deg, #ccac92 0%, #ff678b 0%, #fc3f6b 100%);
}

.member-bg-102 {
	background: linear-gradient(to right, #ccac92, #d2a28a);
}

.member-bg-103 {
	background: linear-gradient(126deg, #ccac92 0%, #a4a4a4 0%, #888888 100%);
}

.member-bg-108 {
	background: linear-gradient(126deg, #403e3e 0%, #191617 100%);
}

.member-color-101 {
	color: #FF678A;
	background: #FFF7F7;
	border-radius: 14rpx;
}

.member-color-102 {
	color: #A4A4A4;
	background: #F3F3F3;
	border-radius: 14rpx;
}

.member-color-103 {
	color: #BE896E;
	background: #FFF7F7;
	border-radius: 14rpx;
}

.member-color-104 {
	color: #000000;
	background: #F2F2F2;
	border-radius: 14rpx;
}
</style>
