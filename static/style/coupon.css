.coupon-label1 {
  text-align: center;
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  width: 86rpx;
  height: 30rpx;
  line-height: 30rpx;
  background: #cfab89;
  border-radius: 15rpx;
  color: #ffffff;
  font-size: 18rpx;
  font-family: PingFang SC;
  font-weight: 400;
}

.coupon-line1 {
  width: 164rpx;
  height: 1px;
  margin-bottom: 5rpx;
  margin-left: 20rpx;
  background: #ffffff;
}

.t1-r {
  background: radial-gradient(circle at top right, transparent 5px, #f37b1d 0) top right,
    radial-gradient(circle at bottom right, transparent 5px, #f37b1d 0) bottom right;
  background-size: 100% 60%;
  background-repeat: no-repeat;
}

.discount-padding-sm {
  padding: 10rpx 18rpx;
}

.t1-l {
  background: radial-gradient(circle at top left, transparent 5px, #f37b1d 0) top left,
    radial-gradient(circle at bottom left, transparent 5px, #f37b1d 0) bottom left;
  background-size: 100% 60%;
  background-repeat: no-repeat;
  border-left: 2rpx dashed rgba(255, 255, 255, 0.3);

}

.discount-img {
  width: 244rpx;
  height: 190rpx;
  position: absolute;
  z-index: -1;
  left: 5rpx;
  bottom: 0rpx;
}

.Invalid-img {
  position: absolute;
  top: 40rpx;
  right: 0rpx;
  width: 130rpx;
  height: 95rpx;
}

.toUse {
  position: absolute;
  top: 60rpx;
  right: 0rpx;
}

.store {
  margin-top: -20rpx;
}

.number {
  font-size: 68rpx;
}

.discount-num {
  position: relative;
  left: 55rpx;
}

.discount {
  margin: auto;
  margin-left: -50rpx;
  padding: -50rpx;
  margin-top: 100rpx;
}

.t2-r {
  background: radial-gradient(circle at top right, transparent 5px, 0) top right,
    radial-gradient(circle at bottom right, transparent 5px, 0) bottom right;
  background-size: 100% 60%;
  background-repeat: no-repeat;
  width: 244rpx;
}

.t2-l {
  background: radial-gradient(circle at top left, transparent 5px, 0) top left,
    radial-gradient(circle at bottom left, transparent 5px, 0) bottom left;
  background-size: 100% 60%;
  background-repeat: no-repeat;
  border-left: 2rpx dashed rgba(255, 255, 255, 0.3);
  box-shadow: 1px 1px 3px 0px rgba(0, 0, 0, 0.11);
}