@keyframes cuIcon-spin {
	0% {
		-webkit-transform: rotate(0);
		transform: rotate(0);
	}

	100% {
		-webkit-transform: rotate(359deg);
		transform: rotate(359deg);
	}
}

.cuIconfont-spin {
	-webkit-animation: cuIcon-spin 2s infinite linear;
	animation: cuIcon-spin 2s infinite linear;
	display: inline-block;
}

.cuIconfont-pulse {
	-webkit-animation: cuIcon-spin 1s infinite steps(8);
	animation: cuIcon-spin 1s infinite steps(8);
	display: inline-block;
}

[class*="cuIcon-"] {
	font-family: "cuIcon";
	font-size: inherit;
	font-style: normal;
}

@font-face {
  font-family: "iconfont"; /* Project id 3640762 */
  src: url('https://at.alicdn.com/t/c/font_3640762_8jk3z6p76zh.eot?t=1662715778773'); /* IE9 */
  src: url('https://at.alicdn.com/t/c/font_3640762_8jk3z6p76zh.eot?t=1662715778773#iefix') format('embedded-opentype'), /* IE6-IE8 */
       url('https://at.alicdn.com/t/c/font_3640762_8jk3z6p76zh.woff2?t=1662715778773') format('woff2'),
       url('https://at.alicdn.com/t/c/font_3640762_8jk3z6p76zh.woff?t=1662715778773') format('woff'),
       url('https://at.alicdn.com/t/c/font_3640762_8jk3z6p76zh.ttf?t=1662715778773') format('truetype'),
       url('https://at.alicdn.com/t/c/font_3640762_8jk3z6p76zh.svg?t=1662715778773#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-a-tuceng-22x2:before {
  content: "\e604";
}

@font-face {
	font-family: "cuIcon";
	src: url('https://at.alicdn.com/t/font_533566_yfq2d9wdij.eot?t=1545239985831');
	/* IE9*/
	src: url('https://at.alicdn.com/t/font_533566_yfq2d9wdij.eot?t=1545239985831#iefix') format('embedded-opentype'),
		/* IE6-IE8 */
		url('https://at.alicdn.com/t/font_533566_yfq2d9wdij.ttf?t=1545239985831') format('truetype'),
		/* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
		url('https://at.alicdn.com/t/font_533566_yfq2d9wdij.svg?t=1545239985831#cuIconfont') format('svg');
	/* iOS 4.1- */
}

.cuIcon-appreciate:before {
	content: "\e644";
}

.cuIcon-check:before {
	content: "\e645";
}

.cuIcon-close:before {
	content: "\e646";
}

.cuIcon-edit:before {
	content: "\e649";
}

.cuIcon-emoji:before {
	content: "\e64a";
}

.cuIcon-favorfill:before {
	content: "\e64b";
}

.cuIcon-favor:before {
	content: "\e64c";
}

.cuIcon-loading:before {
	content: "\e64f";
}

.cuIcon-locationfill:before {
	content: "\e650";
}

.cuIcon-location:before {
	content: "\e651";
}

.cuIcon-phone:before {
	content: "\e652";
}

.cuIcon-roundcheckfill:before {
	content: "\e656";
}

.cuIcon-roundcheck:before {
	content: "\e657";
}

.cuIcon-roundclosefill:before {
	content: "\e658";
}

.cuIcon-roundclose:before {
	content: "\e659";
}

.cuIcon-roundrightfill:before {
	content: "\e65a";
}

.cuIcon-roundright:before {
	content: "\e65b";
}

.cuIcon-search:before {
	content: "\e65c";
}

.cuIcon-taxi:before {
	content: "\e65d";
}

.cuIcon-timefill:before {
	content: "\e65e";
}

.cuIcon-time:before {
	content: "\e65f";
}

.cuIcon-unfold:before {
	content: "\e661";
}

.cuIcon-warnfill:before {
	content: "\e662";
}

.cuIcon-warn:before {
	content: "\e663";
}

.cuIcon-camerafill:before {
	content: "\e664";
}

.cuIcon-camera:before {
	content: "\e665";
}

.cuIcon-commentfill:before {
	content: "\e666";
}

.cuIcon-comment:before {
	content: "\e667";
}

.cuIcon-likefill:before {
	content: "\e668";
}

.cuIcon-like:before {
	content: "\e669";
}

.cuIcon-notificationfill:before {
	content: "\e66a";
}

.cuIcon-notification:before {
	content: "\e66b";
}

.cuIcon-order:before {
	content: "\e66c";
}

.cuIcon-samefill:before {
	content: "\e66d";
}

.cuIcon-same:before {
	content: "\e66e";
}

.cuIcon-deliver:before {
	content: "\e671";
}

.cuIcon-evaluate:before {
	content: "\e672";
}

.cuIcon-pay:before {
	content: "\e673";
}

.cuIcon-send:before {
	content: "\e675";
}

.cuIcon-shop:before {
	content: "\e676";
}

.cuIcon-ticket:before {
	content: "\e677";
}

.cuIcon-back:before {
	content: "\e679";
}

.cuIcon-cascades:before {
	content: "\e67c";
}

.cuIcon-discover:before {
	content: "\e67e";
}

.cuIcon-list:before {
	content: "\e682";
}

.cuIcon-more:before {
	content: "\e684";
}

.cuIcon-scan:before {
	content: "\e689";
}

.cuIcon-settings:before {
	content: "\e68a";
}

.cuIcon-questionfill:before {
	content: "\e690";
}

.cuIcon-question:before {
	content: "\e691";
}

.cuIcon-shopfill:before {
	content: "\e697";
}

.cuIcon-form:before {
	content: "\e699";
}

.cuIcon-pic:before {
	content: "\e69b";
}

.cuIcon-filter:before {
	content: "\e69c";
}

.cuIcon-footprint:before {
	content: "\e69d";
}

.cuIcon-top:before {
	content: "\e69e";
}

.cuIcon-pulldown:before {
	content: "\e69f";
}

.cuIcon-pullup:before {
	content: "\e6a0";
}

.cuIcon-right:before {
	content: "\e6a3";
}

.cuIcon-refresh:before {
	content: "\e6a4";
}

.cuIcon-moreandroid:before {
	content: "\e6a5";
}

.cuIcon-deletefill:before {
	content: "\e6a6";
}

.cuIcon-refund:before {
	content: "\e6ac";
}

.cuIcon-cart:before {
	content: "\e6af";
}

.cuIcon-qrcode:before {
	content: "\e6b0";
}

.cuIcon-remind:before {
	content: "\e6b2";
}

.cuIcon-delete:before {
	content: "\e6b4";
}

.cuIcon-profile:before {
	content: "\e6b7";
}

.cuIcon-home:before {
	content: "\e6b8";
}

.cuIcon-cartfill:before {
	content: "\e6b9";
}

.cuIcon-discoverfill:before {
	content: "\e6ba";
}

.cuIcon-homefill:before {
	content: "\e6bb";
}

.cuIcon-message:before {
	content: "\e6bc";
}

.cuIcon-addressbook:before {
	content: "\e6bd";
}

.cuIcon-link:before {
	content: "\e6bf";
}

.cuIcon-lock:before {
	content: "\e6c0";
}

.cuIcon-unlock:before {
	content: "\e6c2";
}

.cuIcon-vip:before {
	content: "\e6c3";
}

.cuIcon-weibo:before {
	content: "\e6c4";
}

.cuIcon-activity:before {
	content: "\e6c5";
}

.cuIcon-friendaddfill:before {
	content: "\e6c9";
}

.cuIcon-friendadd:before {
	content: "\e6ca";
}

.cuIcon-friendfamous:before {
	content: "\e6cb";
}

.cuIcon-friend:before {
	content: "\e6cc";
}

.cuIcon-goods:before {
	content: "\e6cd";
}

.cuIcon-selection:before {
	content: "\e6ce";
}

.cuIcon-explore:before {
	content: "\e6d2";
}

.cuIcon-present:before {
	content: "\e6d3";
}

.cuIcon-squarecheckfill:before {
	content: "\e6d4";
}

.cuIcon-square:before {
	content: "\e6d5";
}

.cuIcon-squarecheck:before {
	content: "\e6d6";
}

.cuIcon-round:before {
	content: "\e6d7";
}

.cuIcon-roundaddfill:before {
	content: "\e6d8";
}

.cuIcon-roundadd:before {
	content: "\e6d9";
}

.cuIcon-add:before {
	content: "\e6da";
}

.cuIcon-notificationforbidfill:before {
	content: "\e6db";
}

.cuIcon-explorefill:before {
	content: "\e6dd";
}

.cuIcon-fold:before {
	content: "\e6de";
}

.cuIcon-game:before {
	content: "\e6df";
}

.cuIcon-redpacket:before {
	content: "\e6e0";
}

.cuIcon-selectionfill:before {
	content: "\e6e1";
}

.cuIcon-similar:before {
	content: "\e6e2";
}

.cuIcon-appreciatefill:before {
	content: "\e6e3";
}

.cuIcon-infofill:before {
	content: "\e6e4";
}

.cuIcon-info:before {
	content: "\e6e5";
}

.cuIcon-forwardfill:before {
	content: "\e6ea";
}

.cuIcon-forward:before {
	content: "\e6eb";
}

.cuIcon-rechargefill:before {
	content: "\e6ec";
}

.cuIcon-recharge:before {
	content: "\e6ed";
}

.cuIcon-vipcard:before {
	content: "\e6ee";
}

.cuIcon-voice:before {
	content: "\e6ef";
}

.cuIcon-voicefill:before {
	content: "\e6f0";
}

.cuIcon-friendfavor:before {
	content: "\e6f1";
}

.cuIcon-wifi:before {
	content: "\e6f2";
}

.cuIcon-share:before {
	content: "\e6f3";
}

.cuIcon-wefill:before {
	content: "\e6f4";
}

.cuIcon-we:before {
	content: "\e6f5";
}

.cuIcon-lightauto:before {
	content: "\e6f6";
}

.cuIcon-lightforbid:before {
	content: "\e6f7";
}

.cuIcon-lightfill:before {
	content: "\e6f8";
}

.cuIcon-camerarotate:before {
	content: "\e6f9";
}

.cuIcon-light:before {
	content: "\e6fa";
}

.cuIcon-barcode:before {
	content: "\e6fb";
}

.cuIcon-flashlightclose:before {
	content: "\e6fc";
}

.cuIcon-flashlightopen:before {
	content: "\e6fd";
}

.cuIcon-searchlist:before {
	content: "\e6fe";
}

.cuIcon-service:before {
	content: "\e6ff";
}

.cuIcon-sort:before {
	content: "\e700";
}

.cuIcon-down:before {
	content: "\e703";
}

.cuIcon-mobile:before {
	content: "\e704";
}

.cuIcon-mobilefill:before {
	content: "\e705";
}

.cuIcon-copy:before {
	content: "\e706";
}

.cuIcon-countdownfill:before {
	content: "\e707";
}

.cuIcon-countdown:before {
	content: "\e708";
}

.cuIcon-noticefill:before {
	content: "\e709";
}

.cuIcon-notice:before {
	content: "\e70a";
}

.cuIcon-upstagefill:before {
	content: "\e70e";
}

.cuIcon-upstage:before {
	content: "\e70f";
}

.cuIcon-babyfill:before {
	content: "\e710";
}

.cuIcon-baby:before {
	content: "\e711";
}

.cuIcon-brandfill:before {
	content: "\e712";
}

.cuIcon-brand:before {
	content: "\e713";
}

.cuIcon-choicenessfill:before {
	content: "\e714";
}

.cuIcon-choiceness:before {
	content: "\e715";
}

.cuIcon-clothesfill:before {
	content: "\e716";
}

.cuIcon-clothes:before {
	content: "\e717";
}

.cuIcon-creativefill:before {
	content: "\e718";
}

.cuIcon-creative:before {
	content: "\e719";
}

.cuIcon-female:before {
	content: "\e71a";
}

.cuIcon-keyboard:before {
	content: "\e71b";
}

.cuIcon-male:before {
	content: "\e71c";
}

.cuIcon-newfill:before {
	content: "\e71d";
}

.cuIcon-new:before {
	content: "\e71e";
}

.cuIcon-pullleft:before {
	content: "\e71f";
}

.cuIcon-pullright:before {
	content: "\e720";
}

.cuIcon-rankfill:before {
	content: "\e721";
}

.cuIcon-rank:before {
	content: "\e722";
}

.cuIcon-bad:before {
	content: "\e723";
}

.cuIcon-cameraadd:before {
	content: "\e724";
}

.cuIcon-focus:before {
	content: "\e725";
}

.cuIcon-friendfill:before {
	content: "\e726";
}

.cuIcon-cameraaddfill:before {
	content: "\e727";
}

.cuIcon-apps:before {
	content: "\e729";
}

.cuIcon-paintfill:before {
	content: "\e72a";
}

.cuIcon-paint:before {
	content: "\e72b";
}

.cuIcon-picfill:before {
	content: "\e72c";
}

.cuIcon-refresharrow:before {
	content: "\e72d";
}

.cuIcon-colorlens:before {
	content: "\e6e6";
}

.cuIcon-markfill:before {
	content: "\e730";
}

.cuIcon-mark:before {
	content: "\e731";
}

.cuIcon-presentfill:before {
	content: "\e732";
}

.cuIcon-repeal:before {
	content: "\e733";
}

.cuIcon-album:before {
	content: "\e734";
}

.cuIcon-peoplefill:before {
	content: "\e735";
}

.cuIcon-people:before {
	content: "\e736";
}

.cuIcon-servicefill:before {
	content: "\e737";
}

.cuIcon-repair:before {
	content: "\e738";
}

.cuIcon-file:before {
	content: "\e739";
}

.cuIcon-repairfill:before {
	content: "\e73a";
}

.cuIcon-taoxiaopu:before {
	content: "\e73b";
}

.cuIcon-weixin:before {
	content: "\e612";
}

.cuIcon-attentionfill:before {
	content: "\e73c";
}

.cuIcon-attention:before {
	content: "\e73d";
}

.cuIcon-commandfill:before {
	content: "\e73e";
}

.cuIcon-command:before {
	content: "\e73f";
}

.cuIcon-communityfill:before {
	content: "\e740";
}

.cuIcon-community:before {
	content: "\e741";
}

.cuIcon-read:before {
	content: "\e742";
}

.cuIcon-calendar:before {
	content: "\e74a";
}

.cuIcon-cut:before {
	content: "\e74b";
}

.cuIcon-magic:before {
	content: "\e74c";
}

.cuIcon-backwardfill:before {
	content: "\e74d";
}

.cuIcon-playfill:before {
	content: "\e74f";
}

.cuIcon-stop:before {
	content: "\e750";
}

.cuIcon-tagfill:before {
	content: "\e751";
}

.cuIcon-tag:before {
	content: "\e752";
}

.cuIcon-group:before {
	content: "\e753";
}

.cuIcon-all:before {
	content: "\e755";
}

.cuIcon-backdelete:before {
	content: "\e756";
}

.cuIcon-hotfill:before {
	content: "\e757";
}

.cuIcon-hot:before {
	content: "\e758";
}

.cuIcon-post:before {
	content: "\e759";
}

.cuIcon-radiobox:before {
	content: "\e75b";
}

.cuIcon-rounddown:before {
	content: "\e75c";
}

.cuIcon-upload:before {
	content: "\e75d";
}

.cuIcon-writefill:before {
	content: "\e760";
}

.cuIcon-write:before {
	content: "\e761";
}

.cuIcon-radioboxfill:before {
	content: "\e763";
}

.cuIcon-punch:before {
	content: "\e764";
}

.cuIcon-shake:before {
	content: "\e765";
}

.cuIcon-move:before {
	content: "\e768";
}

.cuIcon-safe:before {
	content: "\e769";
}

.cuIcon-activityfill:before {
	content: "\e775";
}

.cuIcon-crownfill:before {
	content: "\e776";
}

.cuIcon-crown:before {
	content: "\e777";
}

.cuIcon-goodsfill:before {
	content: "\e778";
}

.cuIcon-messagefill:before {
	content: "\e779";
}

.cuIcon-profilefill:before {
	content: "\e77a";
}

.cuIcon-sound:before {
	content: "\e77b";
}

.cuIcon-sponsorfill:before {
	content: "\e77c";
}

.cuIcon-sponsor:before {
	content: "\e77d";
}

.cuIcon-upblock:before {
	content: "\e77e";
}

.cuIcon-weblock:before {
	content: "\e77f";
}

.cuIcon-weunblock:before {
	content: "\e780";
}

.cuIcon-my:before {
	content: "\e78b";
}

.cuIcon-myfill:before {
	content: "\e78c";
}

.cuIcon-emojifill:before {
	content: "\e78d";
}

.cuIcon-emojiflashfill:before {
	content: "\e78e";
}

.cuIcon-flashbuyfill:before {
	content: "\e78f";
}

.cuIcon-text:before {
	content: "\e791";
}

.cuIcon-goodsfavor:before {
	content: "\e794";
}

.cuIcon-musicfill:before {
	content: "\e795";
}

.cuIcon-musicforbidfill:before {
	content: "\e796";
}

.cuIcon-card:before {
	content: "\e624";
}

.cuIcon-triangledownfill:before {
	content: "\e79b";
}

.cuIcon-triangleupfill:before {
	content: "\e79c";
}

.cuIcon-roundleftfill-copy:before {
	content: "\e79e";
}

.cuIcon-font:before {
	content: "\e76a";
}

.cuIcon-title:before {
	content: "\e82f";
}

.cuIcon-recordfill:before {
	content: "\e7a4";
}

.cuIcon-record:before {
	content: "\e7a6";
}

.cuIcon-cardboardfill:before {
	content: "\e7a9";
}

.cuIcon-cardboard:before {
	content: "\e7aa";
}

.cuIcon-formfill:before {
	content: "\e7ab";
}

.cuIcon-coin:before {
	content: "\e7ac";
}

.cuIcon-cardboardforbid:before {
	content: "\e7af";
}

.cuIcon-circlefill:before {
	content: "\e7b0";
}

.cuIcon-circle:before {
	content: "\e7b1";
}

.cuIcon-attentionforbid:before {
	content: "\e7b2";
}

.cuIcon-attentionforbidfill:before {
	content: "\e7b3";
}

.cuIcon-attentionfavorfill:before {
	content: "\e7b4";
}

.cuIcon-attentionfavor:before {
	content: "\e7b5";
}

.cuIcon-titles:before {
	content: "\e701";
}

.cuIcon-icloading:before {
	content: "\e67a";
}

.cuIcon-full:before {
	content: "\e7bc";
}

.cuIcon-mail:before {
	content: "\e7bd";
}

.cuIcon-peoplelist:before {
	content: "\e7be";
}

.cuIcon-goodsnewfill:before {
	content: "\e7bf";
}

.cuIcon-goodsnew:before {
	content: "\e7c0";
}

.cuIcon-medalfill:before {
	content: "\e7c1";
}

.cuIcon-medal:before {
	content: "\e7c2";
}

.cuIcon-newsfill:before {
	content: "\e7c3";
}

.cuIcon-newshotfill:before {
	content: "\e7c4";
}

.cuIcon-newshot:before {
	content: "\e7c5";
}

.cuIcon-news:before {
	content: "\e7c6";
}

.cuIcon-videofill:before {
	content: "\e7c7";
}

.cuIcon-video:before {
	content: "\e7c8";
}

.cuIcon-exit:before {
	content: "\e7cb";
}

.cuIcon-skinfill:before {
	content: "\e7cc";
}

.cuIcon-skin:before {
	content: "\e7cd";
}

.cuIcon-moneybagfill:before {
	content: "\e7ce";
}

.cuIcon-usefullfill:before {
	content: "\e7cf";
}

.cuIcon-usefull:before {
	content: "\e7d0";
}

.cuIcon-moneybag:before {
	content: "\e7d1";
}

.cuIcon-redpacket_fill:before {
	content: "\e7d3";
}

.cuIcon-subscription:before {
	content: "\e7d4";
}

.cuIcon-loading1:before {
	content: "\e633";
}

.cuIcon-github:before {
	content: "\e692";
}

.cuIcon-global:before {
	content: "\e7eb";
}

.cuIcon-settingsfill:before {
	content: "\e6ab";
}

.cuIcon-back_android:before {
	content: "\e7ed";
}

.cuIcon-expressman:before {
	content: "\e7ef";
}

.cuIcon-evaluate_fill:before {
	content: "\e7f0";
}

.cuIcon-group_fill:before {
	content: "\e7f5";
}

.cuIcon-play_forward_fill:before {
	content: "\e7f6";
}

.cuIcon-deliver_fill:before {
	content: "\e7f7";
}

.cuIcon-notice_forbid_fill:before {
	content: "\e7f8";
}

.cuIcon-fork:before {
	content: "\e60c";
}

.cuIcon-pick:before {
	content: "\e7fa";
}

.cuIcon-wenzi:before {
	content: "\e6a7";
}

.cuIcon-ellipse:before {
	content: "\e600";
}

.cuIcon-qr_code:before {
	content: "\e61b";
}

.cuIcon-dianhua:before {
	content: "\e64d";
}

.cuIcon-cuIcon:before {
	content: "\e602";
}

.cuIcon-loading2:before {
	content: "\e7f1";
}

.cuIcon-btn:before {
	content: "\e601";
}
