// 定义屏幕尺寸的变量
$breakpoint-small: 310px;
$breakpoint-medium: 550px;
$breakpoint-large: 992px;
$breakpoint-extra-large: 1200px;

$base-size: (
	0: 0,
	sss: 2,
	ss: 4,
	s: 6,
	xs: 8,
	xxs: 10,
	xxxs: 12,
	sm: 14,
	xsm: 16,
	xxsm: 18,
	xxxsm: 20,
	xxxxsm: 22,
	md: 24,
	xmd: 26,
	xxmd: 28,
	xxxmd: 30,
	lg: 32,
	xl: 34,
	xxl: 36,
	xxxl: 38,
	xxxxl: 40,
	xxxxxl: 42,
	48: 48,
	54: 54,
	60: 60,
	64: 64,
	68: 68,
	70: 70,
	75: 75,
	80: 80,
	100: 100,
	104: 104,
	110: 110,
	115: 115,
	172: 172,
	198: 198,
	200: 200,
	220: 220,
	250: 250,
	430: 430,
	858: 858
);

@function adapt-size($value, $multiplier, $unit: 'px') {
	@if $unit =='rpx' {
		@return $value * $multiplier * 1rpx;
	}

	@else if $unit =='px' {
		@return $value * $multiplier * 1px;
	}
}

@mixin size-system($multiplier: 1, $unit: 'px') {
	@each $key, $value in $base-size {
		// 内边距（padding）
		.p-#{$key} {
			padding: adapt-size($value, $multiplier, $unit);
		}

		.pt-#{$key} {
			padding-top: adapt-size($value, $multiplier, $unit);
		}

		.pb-#{$key} {
			padding-bottom: adapt-size($value, $multiplier, $unit);
		}

		.ptb-#{$key} {
			padding-top: adapt-size($value, $multiplier, $unit);
			padding-bottom: adapt-size($value, $multiplier, $unit);
		}

		.pl-#{$key} {
			padding-left: adapt-size($value, $multiplier, $unit);
		}

		.pr-#{$key} {
			padding-right: adapt-size($value, $multiplier, $unit);
		}

		.plr-#{$key} {
			padding-left: adapt-size($value, $multiplier, $unit);
			padding-right: adapt-size($value, $multiplier, $unit);
		}

		// 外边距（margin）
		.m-#{$key} {
			margin: adapt-size($value, $multiplier, $unit);
		}

		.mt-#{$key} {
			margin-top: adapt-size($value, $multiplier, $unit);
		}

		.mb-#{$key} {
			margin-bottom: adapt-size($value, $multiplier, $unit);
		}

		.mtb-#{$key} {
			margin-top: adapt-size($value, $multiplier, $unit);
			margin-bottom: adapt-size($value, $multiplier, $unit);
		}

		.ml-#{$key} {
			margin-left: adapt-size($value, $multiplier, $unit);
		}

		.mr-#{$key} {
			margin-right: adapt-size($value, $multiplier, $unit);
		}

		.mlr-#{$key} {
			margin-left: adapt-size($value, $multiplier, $unit);
			margin-right: adapt-size($value, $multiplier, $unit);
		}

		.font-#{$key} {
			font-size: adapt-size($value, $multiplier, $unit);
		}

		.size-#{$key} {
			width: adapt-size($value, $multiplier, $unit);
			height: adapt-size($value, $multiplier, $unit);
		}

		.height-#{$key} {
			height: adapt-size($value, $multiplier, $unit);
		}

		.lh-#{$key} {
			line-height: adapt-size($value, $multiplier, $unit);
		}

		.width-#{$key} {
			width: adapt-size($value, $multiplier, $unit);
		}
	}
}

@include size-system(0.5, 'px');

// 小屏幕
@media (max-width: $breakpoint-small) {
	@include size-system(0.3, 'px');
}

// 中等屏幕
@media (min-width: $breakpoint-small) and (max-width: $breakpoint-medium) {
	@include size-system(1);
}

// 大屏幕
@media (min-width: $breakpoint-medium) and (max-width: $breakpoint-large) {
	.cu-tag.badge {
		max-width: 18rpx !important;
		height: 28rpx !important;
		font-size: 16rpx;
		line-height: 20rpx;
	}

	.cu-avatar.xl {
		width: 80rpx;
		height: 80rpx;
		font-size: 50rpx;
	}

	.cu-bar .content {
		font-size: 18px;
	}

	@include size-system(0.5, 'px');

	.cu-tag {
		height: 24px;
	}

	.cu-bar.tabbar {
		height: calc(50px + env(safe-area-inset-bottom) / 2);
	}

}

// 超大屏幕
@media (min-width: $breakpoint-large) {}