import { defineStore } from 'pinia'

interface ThemeConfig {
	backgroundColor : string | null;
	themeColor : string | null;
	tabbarBackgroundColor : string | null;
	tabbarColor : string | null;
	tabbarSelectedColor : string | null;
	tabbarBorderStyle : string | null;
	tabbarItem : any[];
	customFiled : any;
}

interface GlobalDataState {
	userInfo : any;
	token : string;
	shoppingCartCount : number;
	tenantId : string | null;
	appId : string | null;
	componentAppId : string | null;
	theme : ThemeConfig;
	tabBar : any[];
	isSinglePage : boolean;
	payMode : boolean;
	isShowingPage : boolean;
	showOpenScreen : boolean;
	gdtVid : string;
	weixinadinfoId : number;
	sf : string;
	nGo : string;
	isDealPaying : boolean;
	orderSubLoading : boolean;
	money : string;
	AuthenticationType : string;
	tabBarHide : number;
	cameraPic : string;
	appState : 'show' | 'hide' | '';
	networkStatus : 'online' | 'offline';
}

export const useGlobalDataStore = defineStore('globalData', {
	state: () : GlobalDataState => ({
		userInfo: null as any,
		token: '',
		shoppingCartCount: 0, //购物车数量
		tenantId: null as string | null, //租户Id
		appId: null as string | null, //公众号appId
		componentAppId: null as string | null, //第三方平台appid
		theme: {
			//主题定义
			backgroundColor: null as string | null, //背景颜色,支持渐变色
			themeColor: null as string | null, //主题颜色
			tabbarBackgroundColor: null as string | null, //tabbar背景颜色
			tabbarColor: null as string | null, //tabBar上的文字默认颜色
			tabbarSelectedColor: null as string | null, //tabBar上的文字选中时的颜色
			tabbarBorderStyle: null as string | null, //tabBar上边框的颜色
			tabbarItem: [] as any[], //tabBar明细设置
			customFiled: null as any //顶部tabBar
		},
		tabBar: [] as any[],
		isSinglePage: false, //分享单页场景值
		payMode: false, //视频号过来的场景值
		isShowingPage: false,
		showOpenScreen: false,
		//腾讯广告 clickid
		gdtVid: '',
		//来源广告的广告id
		weixinadinfoId: 0,
		//统计进入小程序的来源
		sf: '',
		nGo: '',
		//因为支付和下单都是组件，把防抖变量放在组件内控制是不起作用的，需要全局控制
		//是否正在调起支付
		isDealPaying: false,
		orderSubLoading: false,
		money: '',
		AuthenticationType: '',
		tabBarHide: 0,
		// 拍照图片
		cameraPic: '',
		appState: '' as 'show' | 'hide' | '', //show 展示在前端   hide 在后台运行
		networkStatus: 'online' as 'online' | 'offline'
	}),

	getters: {
		isAuthenticated: (state) => !!state.token && state.isLogin,
		hasShoppingCart: (state) => state.shoppingCartCount > 0,
		isAppInForeground: (state) => state.appState === 'show'
	},

	actions: {

		//import { useGlobalDataStore } from '@/stores/globalData'
		//const globalDataStore = useGlobalDataStore()
		// 使用安全更新方法
		// globalDataStore.safeUpdate({
		// token: 'new-token',
		//     userInfo: { id: 123 },
		//   isLogin: true
		// })

		// 使用通用更新方法
		//  globalDataStore.updateValue('token', 'new-token')
		//  globalDataStore.updateValue('userInfo', { id: 123 })
		// 安全的更新方法，使用 $patch 避免代理问题


		safeUpdate(updates : Partial<GlobalDataState>) {
			try {
				this.$patch(updates)
			} catch (error) {
				console.error('安全更新失败:', error)
				// 逐个更新
				Object.keys(updates).forEach(key => {
					try {
						(this as any)[key] = (updates as any)[key]
					} catch (e) {
						console.error(`更新属性 ${key} 失败:`, e)
					}
				})
			}

			// 如果是userInfo，同时存储到本地storage
			if (updates.userInfo != undefined) {
				try {
					uni.setStorageSync('user_info', updates.userInfo)
					console.log('用户信息已同步到本地存储')
				} catch (storageError) {
					console.error('存储用户信息到本地失败:', storageError)
				}
			}

			// 如果是token，同时存储到本地storage
			if (updates.token !== undefined) {
				try {
					uni.setStorageSync('third_session', updates.token)
					console.log('Token已同步到本地存储')
				} catch (storageError) {
					console.error('存储Token到本地失败:', storageError)
				}
			}
		},

		// 通用方法：根据变量名修改指定变量的值
		updateValue(key : string, value : any) {
			try {
				this.safeUpdate({ [key]: value })
			} catch (error) {
				console.error(`更新变量 ${key} 失败:`, error)
			}
		}
		
	})