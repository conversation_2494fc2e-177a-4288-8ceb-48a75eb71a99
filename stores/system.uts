import { defineStore } from 'pinia'

interface SystemState {
  windowWidth: number;
  pixelRatio: number;
  CustomBar: number;
  HeightBar: number;
  StatusBar: number;
  menuHeight: number;
  menuWidth: number;
  leftMenuWidth: number;
  leftMenuHeight: number;
  rootFontSize: number;
  isPhone: boolean;
  padWidth: number;
  bigPhone: number;
  smallWidth: number;
  multipleView: number;
  isIOS: boolean;
  customFiled?: any;
}

export const useSystemStore = defineStore('system', {
  state: (): SystemState => ({
    windowWidth: 0, // 屏幕宽度
    pixelRatio: 0, // 密度比
    CustomBar: 0,
    HeightBar: 0,
    StatusBar: 0,
    menuHeight: 0,
    menuWidth: 0,
    leftMenuWidth: 0,
    leftMenuHeight: 0,
    rootFontSize: 16, // 默认值
    isPhone: true,
    padWidth: 550,
    bigPhone: 430,
    smallWidth: 310,
    multipleView: 2,
    isIOS: false,
    customFiled: null
  }),
  actions: {
    updateData(payload : any) {
      Object.assign(this, payload);
    },
    setRootFontSize(size : number) {
      this.rootFontSize = size;
    },

    updateRootFontSize() {
      const systemInfo = uni.getSystemInfoSync();
      const screenWidth = systemInfo.windowWidth;
      const rootFontSize = screenWidth >= 400 ? 1 * 0.7 : 1; // 根据屏幕宽度动态设置
      this.setRootFontSize(rootFontSize);
    },
  },
})