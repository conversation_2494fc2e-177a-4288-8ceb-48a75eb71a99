declare namespace ConfigPageData {
  interface data {
    categoryId : string;
    clientType : string;
    createTime : string;
    enable : string;
    id : string;
    isTop : number;
    pageComponent : PageComponent;
    pageDeviseCategory : null;
    pageName : string;
    pageType : string;
    pushTime : string;
    pushType : null;
    shopId : string;
    state : string;
    tenantId : string;
    updateTime : string;
    [property : string] : any;
  }

  interface PageComponent {
    componentsList : ComponentsList[];
    pageAdvertDialog : PageAdvertDialog;
    togetherComponent : TogetherComponent;
    [property : string] : any;
  }

  interface ComponentsList {
    componentName : string;
    config : { [key : string] : any };
    data : ComponentsListData;
    id : number;
    [property : string] : any;
  }

  interface ComponentsListData {
    advertType ?: string;
    background : string;
    bgImage : string;
    borderColor ?: string;
    borderRadius ?: number | string;
    borderRadiusLeftTop : string;
    borderRadiusRightTop : string;
    borderRadiusStyle ?: number;
    borderSize ?: string;
    canLongPress ?: number;
    color ?: string;
    dotStyle ?: string;
    fixedRight ?: number;
    fontLeftSpace ?: string;
    fontSize ?: string;
    goodsFromType : number;
    goodsList : DataGoodsList[];
    goodsNum : number;
    goodsSpace : string;
    height ?: number | string;
    imageSpacing ?: number;
    imgFontSpacing ?: string;
    indicatorActiveColor ?: string;
    indicatorColor ?: string;
    interval ?: number;
    isSwiper : number;
    itemSpace ?: string;
    LeftIcon ?: string;
    leftTitle ?: string;
    leftTitleColor ?: string;
    leftTitleSize ?: string;
    leftTitleWidth ?: string;
    lineSpacing ?: string;
    marginBottomSpacing : number | string;
    marginLeftSpacing : number | string;
    marginRightSpacing : number | string;
    marginTopSpacing : number | string;
    navButtons ?: DataNavButton[];
    newGoodsList : NewGoodsList[];
    nextMargin ?: string;
    paddingBottomSpacing : number | string;
    paddingLeftSpacing : number | string;
    paddingRightSpacing : number | string;
    paddingTopSpacing : number | string;
    pageSpacing : number;
    placeholder ?: string;
    radius ?: string;
    radiusLeftBottom ?: number | string;
    radiusLeftTop ?: number | string;
    radiusRightBottom ?: number | string;
    radiusRightTop ?: number | string;
    rightImageUrl ?: string;
    rightLabel ?: string;
    rightPageUrl ?: string;
    rowNum ?: number;
    searchHeight ?: string;
    showLeftTitle ?: number;
    showMessage ?: string;
    showType : string;
    swiperList ?: DataSwiperList[];
    swiperobj : Swiperobj;
    swipeRowNum ?: number;
    swiperType ?: string;
    textColor ?: string;
    textPosition ?: string;
    texts ?: Text[];
    title : string;
    titleColor : string;
    titleIcon : string;
    titleShow : number;
    [property : string] : any;
  }

  interface DataGoodsList {
    brand : string;
    brandSaleNumSort : boolean;
    categoryFifth : string;
    categoryFirst : string;
    categoryFourth : string;
    categoryId : string;
    categoryName : string;
    categoryParameterValue : string;
    categorySecond : string;
    categoryShopFirst : string;
    categoryShopSecond : string;
    categoryThird : string;
    cityFreightTemplatId : string;
    createTime : string;
    delFlag : string;
    distributionMode : string;
    estimatedPriceVo : EstimatedPriceVo;
    freightTemplatId : string;
    goodsGroupIdList : string[];
    goodsPoints : number;
    id : string;
    inventoryState : string;
    isAutoPlay : string;
    isGift : number;
    isMute : string;
    isPresale : number;
    isShow : string;
    memberLevelLimit : string;
    name : string;
    picUrls : string[];
    picUrlsJson : GoodsListPicUrlsJson[];
    pointsDeductAmount : number;
    pointsDeductScale : number;
    pointsDeductSwitch : string;
    pointsGiveSwitch : string;
    priceDown : number | number;
    priceOriginal : number;
    priceUp : number;
    promotionsInfos : PromotionsInfo[];
    pushErp : boolean;
    rebateAmount : number;
    remainSecond : number;
    saleNum : number;
    sellPoint : string;
    sharePic : string;
    shelf : string;
    shopId : string;
    showDiscount : string;
    sort : number;
    sources : string;
    specType : string;
    spuBigCode : string;
    spuCode : string;
    spuType : number;
    tenantId : string;
    updateTime : string;
    verifyDetail : string;
    verifyStatus : string;
    videoAddress : string;
    [property : string] : any;
  }

  interface EstimatedPriceVo {
    apiCouponNameIdVO : ApiCouponNameIdVO;
    coupon : string;
    discountPrice : string;
    estimatedPrice : string;
    originalPrice : string;
    price : string;
    promotionsDiscountPrice : string;
    totalDiscountPrice : string;
    [property : string] : any;
  }

  interface ApiCouponNameIdVO {
    couponId : string;
    isFullPremiseAmount : boolean;
    name : string;
    premiseAmount : number;
    reduceAmount : number;
    type : string;
    [property : string] : any;
  }

  interface GoodsListPicUrlsJson {
    fileFormat : string;
    fileSize : number | string;
    imageHeight : number | string;
    imageWidth : number | string;
    url : string;
    [property : string] : any;
  }

  interface PromotionsInfo {
    createTime : string;
    delFlag : string;
    discount : number;
    enable : string;
    goodsGroupIds : string;
    id : string;
    name : string;
    rangeType : string;
    shopId : string;
    sort : number;
    updateTime : string;
    validBeginTime : string;
    validEndTime : string;
    version : number;
    [property : string] : any;
  }

  interface DataNavButton {
    id : number;
    imageUrl : string;
    isNewPage : number;
    itemSpace : string;
    itemWidth : number | string;
    navName : string;
    pageUrl : string;
    [property : string] : any;
  }

  interface NewGoodsList {
    $index : number;
    $pointsDeductSwitch : string;
    $pointsGiveSwitch : string;
    $shelf : string;
    $shopId : string;
    $specType : string;
    $verifyStatus : string;
    approvalId : string;
    approvalType : string;
    barCode : string;
    brand : string;
    brandCategoryShopFirst : string;
    brandCategoryShopSecond : string;
    brandSaleNumSort : boolean;
    cabinetGroup : string;
    categoryFifth : string;
    categoryFirst : string;
    categoryFourth : string;
    categoryId : string[];
    categoryName : string;
    categoryParameterValue : string;
    categorySecond : string;
    categoryShopFirst : string;
    categoryShopSecond : string;
    categoryThird : string;
    cityFreightTemplatId : string;
    createTime : string;
    delFlag : string;
    distributionMode : string;
    formatType : string;
    freightTemplatId : string;
    goodsPoints : number;
    id : string;
    isGift : number;
    isPresale : number;
    limitBuyNum : number;
    mainPhoto : string[];
    name : string;
    originPlace : string;
    picUrls : string[];
    picUrlsJson : NewGoodsListPicUrlsJson[];
    pointsDeductAmount : number;
    pointsDeductScale : number;
    pointsDeductSwitch : string;
    pointsGiveSwitch : string;
    price : number;
    priceDown : number;
    priceMembership : number;
    priceOriginal : number;
    priceUp : number;
    pushErp : boolean;
    rebateAmount : number;
    recommendSort : number;
    remainSecond : number;
    saleNum : number;
    sellPoint : string;
    sharePic : string;
    shelf : string;
    shopCode : string;
    shopId : string;
    shopName : string;
    skus : Skus[];
    sort : number;
    sources : string;
    spec : string;
    specType : string;
    spuBigCode : string;
    spuCode : string;
    spuType : number;
    stock : number;
    tenantId : string;
    updateTime : string;
    verifyDetail : string;
    verifyStatus : string;
    videoAddress : string;
    weight : number;
    [property : string] : any;
  }

  interface NewGoodsListPicUrlsJson {
    fileFormat : string;
    fileSize : number | string;
    imageHeight : number | string;
    imageWidth : number | string;
    url : string;
    [property : string] : any;
  }

  interface Skus {
    barCode : string;
    costPrice : number | number;
    createTime : string;
    delFlag : string;
    enable : string;
    firstRebate : number;
    id : string;
    marketPrice : number;
    minBuyNum : number;
    packageNum : number;
    picUrl : string;
    rebateAmount : number;
    salesPrice : number;
    secondRebate : number;
    shopId : string;
    skuCode : string;
    specs : string[];
    spuId : string;
    stock : number;
    tenantId : string;
    updateTime : string;
    version : number;
    volume : number;
    weight : number | number;
    [property : string] : any;
  }

  interface DataSwiperList {
    background : string;
    backgroundRadius : number;
    contentType : string;
    height : string;
    hotZones : HotZone[];
    iconPath : string;
    id : number | number;
    imageUrl : string;
    imgHeight : number;
    imgWidth : number;
    itemTitle : string;
    itemWidth : string;
    pageUrl : string;
    rankingGoodsId : string[];
    selectedIconPath : string;
    width : string;
    [property : string] : any;
  }

  interface HotZone {
    heightPer : number;
    leftPer : number | number;
    pageName : string;
    pageUrl : string;
    popTag : number;
    topPer : number | number;
    widthPer : number | number;
    [property : string] : any;
  }

  interface Swiperobj {
    borderRadius : string;
    goodsList : SwiperobjGoodsList[];
    height : string;
    interval : number;
    type : string;
    [property : string] : any;
  }

  interface SwiperobjGoodsList {
    id : number;
    imageUrl : string;
    imgHeight : number;
    imgWidth : number;
    itemTitle : string;
    pageUrl : string;
    [property : string] : any;
  }

  interface Text {
    name ?: string;
    [property : string] : any;
  }

  interface PageAdvertDialog {
    borderRadius : string;
    bottomSpacing : string;
    canShare : string;
    cusService : CusService;
    height : string;
    imageUrl : string;
    interval : string;
    intervalNumber : string;
    intervalTime : string;
    live : Live;
    lottery : Lottery;
    navButtons : PageAdvertDialogNavButton[];
    newcustomerinfo : Newcustomerinfo;
    pageUrl : string;
    redpackage : Redpackage;
    rightSpacing : string;
    shareImageUrl : string;
    shareShow : string;
    shareTitle : string;
    showAdvertDialog : string;
    showPop : string;
    swiperList : PageAdvertDialogSwiperList[];
    topSpacing : string;
    width : string;
    [property : string] : any;
  }

  interface CusService {
    bottomSpacing : string;
    height : string;
    imageUrl : string;
    rightSpacing : number;
    show : string;
    showPop : string;
    topSpacing : number;
    width : string;
    [property : string] : any;
  }

  interface Live {
    bottomSpacing : string;
    height : string;
    imageUrl : string;
    rightSpacing : string;
    show : string;
    showPop : string;
    topSpacing : number;
    width : string;
    [property : string] : any;
  }

  interface Lottery {
    isShow : string;
    lotteryId : string;
    [property : string] : any;
  }

  interface PageAdvertDialogNavButton {
    bottomSpacing ?: string;
    height ?: string;
    id ?: number;
    imageUrl ?: string;
    pageUrl ?: string;
    rightSpacing ?: string;
    width ?: string;
    [property : string] : any;
  }

  interface Newcustomerinfo {
    isShow : string;
    newcustomerId : string;
    [property : string] : any;
  }

  interface Redpackage {
    bottomSpacing : string;
    height : string;
    imageUrl : string;
    rightSpacing : string;
    show : string;
    width : string;
    [property : string] : any;
  }

  interface PageAdvertDialogSwiperList {
    height : string;
    id : number;
    imageUrl : string;
    itemTitle : string;
    pageUrl : string;
    width : string;
    [property : string] : any;
  }

  interface TogetherComponent {
    bgImage : string;
    height : number;
    [property : string] : any;
  }
}