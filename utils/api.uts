import __config from "@/config/env";
import { isWeiXinBrowser, isMiniPg, backLoginPage, getSharerUserCode, dataAddSharerUserCode, isUserLogin, toAsync } from "@/utils/util.uts";
import { doLogin } from "@/utils/login.uts";
import { useGlobalDataStore } from "@/stores/globalData";
import CryptoJS from "crypto-js";

// 定义通用响应接口
interface ApiResponse<T = any> {
	code : number;
	msg ?: string;
	data ?: T;
}

// 定义请求参数接口
interface RequestOptions {
	url : string;
	method ?: string;
	data ?: any;
	showLoading ?: boolean;
	errModalHide ?: boolean;
	dialogFinishCallBack ?: (() => void) | null;
	contentType ?: string;
	isSign ?: boolean;
}

/**
 * @params errModalHide 结算领取优惠券时，不提示任何错误信息，不拦截任何错误信息，允许下一步结算操作
 * @params errGoLogin  大转盘没登录的时候，调剩余
 */
const uuid = () => {
	let s : string[] = [];
	let hexDigits = "0123456789abcdef";
	for (var i = 0; i < 36; i++) {
		s[i] = hexDigits.substring(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = "4";
	s[19] = hexDigits.substring((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = "-";
	var uuid = s.join("");
	return uuid;
};

// 1. requestApi 作为主请求方法
export const requestApi = <T = any>(
	url : string,
	method : string = "GET",
	data ?: any,
	showLoading : boolean = true,
	errModalHide : boolean = false,
	dialogFinishCallBack ?: (() => void) | null,
	contentType : string = "application/json",
	isSign : boolean = false
) : Promise<ApiResponse<T>> => {
	let _url = url;
	const userInfo = uni.getStorageSync("user_info");
	//#ifndef H5
	// 通过扫码微信小程序码到pages/pos/agreeauth界面，授权退货申请，调的接口是pos端的服务，只有微信小程序有这个功能
	_url = __config.basePath + url;
	//#endif
	// channel 渠道 全局判断渠道 SONGSHU-微信小程序 APP-APP
	// 先判断data有没有值，再判断data里面有没有channel，再判断channel的值，最后替换channel的值
	if (data) {
		const systemInfo = wx.getSystemInfoSync();
		if (data.channel == "" || data.channel == "SONGSHU" || data.channel == "APP") {
			// console.error("==systemInfo.environment ==================",systemInfo.environment )
			if (systemInfo.environment && systemInfo.environment === "wxwork") {
				data.channel = "WXWORK";
			} else if (systemInfo.uniPlatform == "app") {
				data.channel = "APP";
			} else if (systemInfo.uniPlatform == "mp-weixin") {
				data.channel = "SONGSHU";
			}
		}
	}

	return new Promise((resolve, reject) => {
		if (showLoading) {
			uni.showLoading({
				title: "加载中",
			});
		}

		let header = {
			//#ifdef MP-WEIXIN
			"client-type": "MA", //客户端类型小程序
			"app-id": uni.getAccountInfoSync().miniProgram.appId, //小程序appId
			//#endif
			//#ifdef APP-PLUS
			"client-type": "APP", //客户端类型APP
			"app-id": userInfo?.appId || "",
			"tenant-id": __config.tenantId,
			//#endif
			// 不从store里面取，因为有时候接口接连请求，边取边写会报错
			"third-session": uni.getStorageSync("third_session") || "",
			"user_id": userInfo?.id || "",
			"Content-Type": contentType || "application/json",
		};
		console.log("发送接口url:" + _url + ", 接口方式：" + method + ", 数据data: ", data);
		// 处理如果需要加密的话 header里增加3个参数  timestamp,requestId, sign
		let timestamp = Math.round(new Date().getTime());
		let requestId = uuid();
		let securityKey = "songleishop"; //签名Key
		// sign的获取方式 对字符串进行md5(str)加密得签名sign	，其中params 取 get/delete方法的url参数，取put/post 的 body的json参数，对参数名排序后拼接成如a=1&b=2字符串
		//取key

		let sign = "",
			tempData = {};
		if (method == "get" || method == "GET" || method == "DELETE" || method == "delete") {
			let param = {};
			if (_url && _url.indexOf("?") != -1) {
				const paramsString = _url.split("?")[1];
				const eachParamArray = paramsString.split("&");
				eachParamArray.forEach((p) => {
					const key = p.split("=")[0];
					var value = p.split("=")[1];
					Object.assign(param, {
						[key]: value,
					});
				});
			}

			data = {
				...param,
				...data,
			};
			let keys = [];
			for (let k in data) {
				keys.push(k);
				tempData[k] = data[k] == null ? "" : data[k];
			}
			//排序
			keys.sort();
			//取value
			let str = "";
			for (let k in keys) {
				str = str + "&" + keys[k] + "=" + (data[keys[k]] == null ? "" : data[keys[k]]);
			}
			str = str.slice(1, str.length);
			let paramStr = "timestamp=" + timestamp + "&requestId=" + requestId + "&params=" + str + "&securityKey=" + securityKey;
			sign = CryptoJS.MD5(paramStr).toString();
		} else {
			tempData = data;
			let paramStr = "timestamp=" + timestamp + "&requestId=" + requestId + "&params=" + JSON.stringify(data || {}) + "&securityKey=" + securityKey;
			sign = CryptoJS.MD5(paramStr).toString();
		}
		header = {
			...header,
			timestamp,
			requestId,
			sign,
		};

		uni.request({
			url: _url,
			method: method,
			data: tempData,
			withCredentials: true,
			header,
			success(res) {
				console.log(_url + "接口返回值===", res);
				if (res.statusCode == 200) {
					if (res.data.code != 0) {
						if (res.data.code == 60001 || res.data.code == 60002) {
							// 防止多个同时触发
							const globalDataStore = useGlobalDataStore();
							if (!globalDataStore.isDealPaying) {
								globalDataStore.safeUpdate({
									isDealPaying: true
								});
								if (isMiniPg() || (globalDataStore.appId && isWeiXinBrowser())) {
									//小程序或公众号H5，删除third_session重新登录
									uni.removeStorageSync("third_session");
									doLogin().then((res) => {
										var pages = getCurrentPages(); //获取页面栈
										var currPage = pages[pages.length - 1]; // 当前页面
										currPage.onLoad(currPage.options);
										currPage.onShow();
									});
								} else {
									backLoginPage();
								}
								setTimeout(function () {
									globalDataStore.safeUpdate({
										isDealPaying: false
									});
								}, 2000);
							}
							reject("session过期重新登录");
						} else if (res.data.code == 60003) {
							const globalDataStore = useGlobalDataStore();
							//防止同时多个接口触发登录
							if (!globalDataStore.orderSubLoading) {
								globalDataStore.safeUpdate({
									orderSubLoading: true
								});

								backLoginPage();
								setTimeout(function () {
									globalDataStore.safeUpdate({
										orderSubLoading: false
									});
								}, 2000);
							}
							reject("请先登录商城");
						} else if (res.data.code == 90002 || res.data.code == 90003 || res.data.code == 90001 || res.data.code == 90000) {
							resolve(res.data);
						} else if (res.data.code == 50009) {
							resolve(res.data);
						} else {
							!errModalHide &&
								uni.showModal({
									title: "提示",
									showCancel: false,
									content: res.data.msg ? res.data.msg + "" : "网络连接错误",
									success() { },
									complete() {
										dialogFinishCallBack && dialogFinishCallBack();
									},
								});
							!errModalHide ? reject(res.data.msg || "网络连接错误") : resolve();
						}
					}
					// console.log("res.data.code==>", res.data.code);
					resolve(res.data);
				} else if (res.statusCode == 404) {
					!errModalHide &&
						uni.showModal({
							title: "提示",
							content: "接口请求出错，请检查手机网络",
							showCancel: false,
							success(res) { },
						});
					!errModalHide ? reject() : resolve();
				} else if (res.statusCode == 502) {
					// console.log(502);
					!errModalHide &&
						uni.showModal({
							title: "提示",
							content: "服务器维护中，请稍后再来",
							showCancel: false,
							success(res) { },
						});
					!errModalHide ? reject(res) : resolve();
				} else if (res.statusCode == 503) {
					console.log(503);
					!errModalHide &&
						uni.showModal({
							title: "提示",
							content: "503错误，服务未启动",
							showCancel: false,
							success(res) { },
						});
					!errModalHide ? reject(res) : resolve();
				} else {
					console.log("网络连接错误1", res);
					!errModalHide &&
						uni.showModal({
							title: "提示",
							showCancel: false,
							content: res.data.msg || "网络连接错误",
							success(res) { },
						});
					!errModalHide ? reject(res) : resolve();
				}
			},
			fail(error) {
				console.log("接口报错==>", error);
				// interrupted 是程序在后台请求超过5s，报错的提示
				!errModalHide &&
					error.errMsg &&
					error.errMsg.indexOf("interrupted") == -1 &&
					uni.showModal({
						title: "提示",
						content: "接口请求出错：" + error.errMsg,
						showCancel: false,
						success(res) { },
					});
				!errModalHide ? reject(error) : resolve();
			},
			complete(res) {
				uni.hideLoading();
			},
		});
	});
};

// 2. request 方法，参数为对象
export const request = <T = any>(obj : RequestOptions) : Promise<ApiResponse<T>> => {
	const {
		url = "/",
		method = "get",
		data,
		showLoading = true,
		errModalHide = false,
		dialogFinishCallBack = null,
		contentType = "application/json",
		isSign = false,
	} = obj;
	return requestApi(url, method, data, showLoading, errModalHide, dialogFinishCallBack, contentType, isSign);
}

// 3. 其他API方法 ...
// 例如：
export const loginWxMa = (data : any) => {
	data = dataAddSharerUserCode(data);
	return requestApi("/mallapi/wxuser/loginma", "post", data, false);
}

export const loginWxMp = (data : any) => {
	data = dataAddSharerUserCode(data);
	return requestApi("/mallapi/wxuser/loginmp", "post", data, false);
}

export const loginByPhoneMa = (data : any) => {
	data = dataAddSharerUserCode(data);
	const globalDataStore = useGlobalDataStore();
	return requestApi(
		"/mallapi/userinfo/ma/phone/login",
		"post",
		{
			...data,
			clickId: globalDataStore.gdtVid || "",
		},
		true
	);
}

export const themeMobileGet = () => {
	return requestApi("/mallapi/thememobile", "get", null, false);
}

// 4. default 导出
export default {
	requestApi,
	request,
	loginWxMa,
	loginWxMp,
	loginByPhoneMa,
	themeMobileGet,
};