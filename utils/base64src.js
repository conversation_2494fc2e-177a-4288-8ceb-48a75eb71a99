const FILE_BASE_NAME = 'tmp_base64src'; //自定义文件名
function downLoadImg(imageUrl, cb) {
  var bitmap = new plus.nativeObj.Bitmap();
  bitmap.loadBase64Data(
    imageUrl, () => {
      const url = "_doc/" + new Date().getTime() + ".png";
      bitmap.save(
        url,
        {
          overwrite: true,
          quality: "quality",
        },
        function (i) {
          console.log("app下载图片URL", i.target)
          cb(i.target)
        }
      )
    }
  )
}

function base64src(base64data, cb) {
	let fileName = FILE_BASE_NAME + new Date().getTime();
  base64data = base64data.replace(/[\r\n]/g, '');
  // #ifdef APP
  downLoadImg(base64data, cb)
  // #endif
  // #ifdef MP
  const fsm = uni.getFileSystemManager();
  const filePath = `${uni.env.USER_DATA_PATH}/${fileName}`;
  fsm.unlink({
    filePath: filePath,
    fail: res => {
      console.log(res);
    },
    complete: res => {
      const buffer = uni.base64ToArrayBuffer(base64data);
      fsm.writeFile({
        filePath: filePath,
        data: buffer,
        encoding: 'base64',
        success: res => {
          cb(filePath);
        },
        fail: res => {
          return new Error('ERROR_BASE64SRC_WRITE');
        }
      });
    }
  });
  // #endif
  
}
export { base64src };
