const getDistance = (lat1, lng1, lat2, lng2) => {
	lat1 = lat1 || 0;
	lng1 = lng1 || 0;
	lat2 = lat2 || 0;
	lng2 = lng2 || 0;
	var rad1 = lat1 * Math.PI / 180.0;
	var rad2 = lat2 * Math.PI / 180.0;
	var a = rad1 - rad2;
	var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
	var r = 6378137;
	var distance = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math
		.pow(Math.sin(b / 2), 2)));
	return distance;
};

const getNearestDistance = (lat1, lng1, posList) => {
	let nearestDistancePos = null;
	if (posList && posList.length) {
		posList.forEach(item => {
			item.distance = getDistance(lat1, lng1, item.latitude, item.longitude);
			if (!nearestDistancePos) nearestDistancePos = item;
			else {
				if(nearestDistancePos.distance>item.distance){
					nearestDistancePos = item;
				}
			}
		})
	}
	return nearestDistancePos;
};

const calculateDistance = (lat1, lng1, lat2, lng2) => {
	const radLat1 = toRadians(lat1);
	const radLat2 = toRadians(lat2);
	const deltaLat = radLat1 - radLat2;
	const deltaLng = toRadians(lng1) - toRadians(lng2);
	const a = Math.sin(deltaLat / 2) ** 2 + Math.cos(radLat1) * Math.cos(radLat2) * Math.sin(deltaLng / 2) ** 2;
	const c = 2 * Math.asin(Math.sqrt(a));
	const earthRadius = 6371000; // 地球半径，单位为米
	return earthRadius * c; // 返回距离，单位为米
};

const toRadians = (degrees) =>{
	return degrees * (Math.PI / 180);
}

module.exports = {
	getDistance,
	getNearestDistance,
	calculateDistance
}