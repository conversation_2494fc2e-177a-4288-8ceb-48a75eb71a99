/**
 * 因为页面性质不同，所以跳转的方式也不同，所以这里区分出特殊的页面跳转方式出来
 */
// import {
//   EventBus
// } from "@/utils/eventBus.js";
export const pageUrls = {
  tabPages: [
    "/pages/home/<USER>",
    "/pages/second-tab/index",
    "/pages/third-tab/index",
    "/pages/tab-personal/index",
    "/pages/shopping-cart/index",
  ] //所有的tab页面  '/pages/goods/goods-category/index'
}
import api from './api'
import {
  isUserLogin
} from './util.uts'
// #ifdef MP-WEIXIN
const businessCirclePlugin = requirePlugin('business-circle-plugin');
// #endif
export const gotoPage = (url) => {
  let switchTab = false;
  const pages = getCurrentPages()
  const pageSize = pages.length; //当前页面栈的个数
  pageUrls.tabPages.forEach(item => {
    if (item && url && url.indexOf(item) != -1) {
      switchTab = true;
    }
  })
  if (switchTab) {
    // 不支持参数
    //所以需要缓存参数
    // 跳转到逛逛直播的链接
    // pages/third-tab/index?selectPoi=3
    if (url.indexOf("?") != -1) {
      let paramsString1 = url.split("?")[1];
      if (paramsString1 && paramsString1.indexOf('=') != -1) {
        console.log("======paramsStri======", paramsString1.split("=")[0]);
        uni.setStorageSync("tab-" + paramsString1.split("=")[0], paramsString1.split("=")[1]);
      }
    }
    uni.switchTab({
      url
    })
  } else if (url.startsWith('api:receiveCoupon')) {
    // api:receiveCoupon?couponId=56&name='优惠券名字'
    //截取后面的跳转链接
    if (url.indexOf("?") != -1 && url.indexOf('&') != -1) {
      let paramsString = url.split("?")[1];
      let paramsString1 = url.split("&")[1];
      const couponId = paramsString1.split("=")[1];
      if (couponId > 0) {
        api.couponUserSave({
          couponId
        }).then(res => {
          uni.showToast({
            title: '领取成功',
            icon: 'success',
            duration: 2000
          });
        }).catch(e => {

        });
      } else {
        uni.showToast({
          title: '请先配置优惠券链接',
          icon: 'success',
          duration: 2000
        });
      }
    }
  } else if (url.startsWith('customUrl:')) {
    //自定义链接   pageUrl: "customUrl:plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=1549"
    //截取后面的跳转链接
    const urlContent = url.substr(10);
    if (urlContent) {
      if (urlContent.startsWith('http')) {
        if (pageSize == 10 || pageSize == 7 || pageSize == 6) {
          uni.redirectTo({
            url: "/pages/public/webview/webview?url=" + encodeURIComponent(urlContent)
          });
        } else {
          uni.navigateTo({
            url: "/pages/public/webview/webview?url=" + encodeURIComponent(urlContent)
          });
        }

      } else {
        if (pageSize == 10 || pageSize == 8 || pageSize == 5 || pageSize == 7) {
          uni.redirectTo({
            url: url.substr(10)
          });
        } else {
          uni.navigateTo({
            url: url.substr(10)
          });
        }
      }
    }
  } else if (url.startsWith('mpUrl:')) {
    //小程序自定义链接   pageUrl: "mpUrl:plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=1549"
    //截取后面的跳转链接
    const urlContent = url.substr(6);
    console.log(urlContent)
    if (urlContent) {
      const urlContentObj = JSON.parse(urlContent);
      //如果是进入直播,调统计接口
      if (urlContentObj.pageUrl && urlContentObj.pageUrl.startsWith(
          'plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin')) {
        const app = getApp();
        if (app.globalData.gdtVid) {
          api.postTimelineAdvertAction({
            action: 'LIVE_STREAM',
            clickId: app.globalData.gdtVid || ''
          });
        }
      }

      //如果是进入商圈插件，需要拼接上openId
      if (urlContentObj.pageUrl && urlContentObj.pageUrl.startsWith(
          'plugin://business-circle-plugin/index')) {
        const mchId = urlContentObj.pageUrl.split('mch_id=');
        const userInfo = uni.getStorageSync('user_info');
        urlContentObj.pageUrl += `&openid=` + userInfo.openId;
        if (!mchId || !mchId[1]) {
          uni.showToast({
            icon: 'none',
            title: '请给链接添加商户号'
          })
        }
        if (!isUserLogin()) {
          uni.reLaunch({
            url: '/pages/login/index?fromChannel=SMART&&reUrl=business-circle-plugin'
          });
          return
        }
        // #ifdef MP-WEIXIN
        businessCirclePlugin.getLocation(userInfo.openId).then(
          res => {
            if (res.return_code == 0) {
              api.erpOpenIdBind({
                channelId: 'SMART'
              }).then(res => {
                businessCirclePlugin.getAuthStatus(userInfo.openId, mchId[1]).then(
                  res => {
                    urlContentObj.pageUrl += `&member_status=` + res.status;
                    if (pageSize == 10 || pageSize == 8) {
                      uni.redirectTo({
                        url: urlContentObj.pageUrl
                      });
                    } else {
                      uni.navigateTo({
                        url: urlContentObj.pageUrl
                      });
                    }
                  })
              });
            }
          })
        // #endif
        return
      }

      if (urlContentObj.type == 1) {
        console.log("==urlContent===11==", urlContentObj.pageUrl)
        if (pageSize == 10 || pageSize == 8) {
          uni.redirectTo({
            url: urlContentObj.pageUrl
          });
        } else {
          uni.navigateTo({
            url: urlContentObj.pageUrl
          });
        }

      } else if (urlContentObj.type == 2) {
        console.log("==urlContent===22==", urlContentObj, urlContentObj.appId,
          urlContentObj.pageUrl)
        uni.navigateToMiniProgram({
          appId: urlContentObj.appId,
          path: urlContentObj.pageUrl,
          success(res) {
            // 打开成功
            console.log("打开三方小程序成功")
          }
        })
      }
    }
  } else if (url && url == 'dialog:showNortheastActivity') {
    // EventBus.$emit("dialog:showNortheastActivity");
  } else {
    if (pageSize == 10 || pageSize == 8 || pageSize == 5 || pageSize == 7) {
      uni.redirectTo({
        url: url
      });
    } else {
      uni.navigateTo({
        url: url
      });
    }

  }
}