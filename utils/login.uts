import api from './api.uts'
import { getUrlParam, isMiniPg } from './util.uts'
import { useGlobalDataStore } from '@/stores/globalData'

// #ifdef MP-WEIXIN
// 微信小程序登录
export const loginWxMa = () : Promise<string> => {
	return new Promise((resolve, reject) => {
		uni.login({
			success: (res) => {
				console.log('login res', res)
				if (res.code) {
					console.log("========22====");
					api.loginWxMa({ jsCode: res.code }).then((res : any) => {
						uni.hideLoading()
						const userInfo = res.data;
						const globalDataStore = useGlobalDataStore();
						globalDataStore.safeUpdate({
							token: userInfo.thirdSession,
							userInfo: userInfo,
						})
						resolve('success')
					}).catch((err : any) => {
						reject(err)
					})
				} else {
					reject('获取code失败')
				}
			},
			fail: (err) => {
				reject(err)
			}
		})
	})
}
// #endif


// 主登录函数
export const doLogin = () : Promise<string> => {
	return new Promise((resolve, reject) => {
		uni.showLoading({ title: '登录中' })

		// #ifdef MP-WEIXIN
		loginWxMa().then(() => {
			resolve('success')
		}).catch((err : any) => {
			uni.hideLoading()
			reject(err)
		})
		// #endif

		// #ifdef APP
		api.userInfoGet().then((res : any) => {
			const userInfo = res.data
			const globalDataStore = useGlobalDataStore();
			globalDataStore.safeUpdate({
				token: userInfo.thirdSession,
				userInfo: userInfo,
			})
			uni.hideLoading()
			resolve('success')
		}).catch((err : any) => {
			uni.hideLoading()
			reject(err)
		})
		// #endif
	})
}

// 检查用户状态
export const isUser = () : number => {
	const userInfo = uni.getStorageSync('user_info')
	if (userInfo) {
		const { id, erpCid } = userInfo
		if (id && erpCid) {
			return 1 // 登录中
		} else if (id && !erpCid) {
			return 2 // 登录失效
		}
		return 0
	}
	return -1
}

// 检查是否登录
export const isLogin = (hidden : boolean = false) : boolean => {
	const userStatus = isUser()
	if (userStatus === 1) {
		return true
	} else {
		if (!hidden && userStatus === 2) {
			uni.showModal({
				title: '提示',
				content: '登录失效，请重新登录',
				showCancel: false,
				success: () => {
					const pages = getCurrentPages()
					const url = pages[pages.length - 1].$page.fullPath
					uni.reLaunch({
						url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
					})
				}
			})
		} else {
			const pages = getCurrentPages()
			const url = pages[pages.length - 1].$page.fullPath
			uni.reLaunch({
				url: '/pages/login/index?reUrl=' + encodeURIComponent(url)
			})
		}
		return false
	}
}

// 页面初始化方法，供每个页面调用
// 是否必须微信是登录状态，比如获取手机号，头像昵称解析加密的数据必须保证微信的session有效
export const initPage = (isNeedWxLogin : boolean) : Promise<string> => {
	return new Promise((resolve, reject) => {
		// 小程序或公众号H5，每个页面都进行登录校验
		if (isMiniPg()) {
			if (!uni.getStorageSync('third_session') && !isNeedWxLogin) {
				// 无thirdSession，进行登录
				console.log("========11====");
				doLogin().then(() => {
					resolve('success')
				}).catch((err : any) => {
					reject(err)
				})
			} else {
				if (isMiniPg()) {
					// 小程序需要检查登录态是否过期
					uni.checkSession({
						success() {
							// session_key 未过期，并且在本生命周期一直有效
							resolve('success')
						},
						fail() {
							// session_key 已经失效，需要重新执行登录流程
							doLogin().then(() => {
								resolve('success')
							}).catch((err : any) => {
								reject(err)
							})
						}
					})
				} else {
					resolve('success')
				}
			}
		} else {
			resolve('success')
		}
	})
}

// 登出方法
export const logout = () : void => {
	const globalDataStore = useGlobalDataStore()
	globalDataStore.safeUpdate({
		token: "",
		userInfo: null,
	})
}


// 默认导出所有方法
export default {
	// #ifdef MP-WEIXIN
	loginWxMa,
	// #endif
	doLogin,
	isUser,
	isLogin,
	initPage,
	logout,
}