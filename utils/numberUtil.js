//数字工具
var numberUtil = {
  numberFormat: function (value, num) {
    //小数点精确到指定位数
    return parseFloat(value.toFixed(num));
  },
  numberSubtract: function (value1, value2) {
    //减法运算value1-value2
    var v = parseFloat(value1) - parseFloat(value2);
    return parseFloat(v.toFixed(2));
  },
  numberAddition: function (value1, value2) {
    //加法运算value1+value2
    var v = parseFloat(value1) + parseFloat(value2);
    return parseFloat(v.toFixed(2));
  },
  getAreaDistance: function (lat1, lng1, lat2, lng2) {
    //计算经纬距离
    lat1 = lat1 || 0;
    lng1 = lng1 || 0;
    lat2 = lat2 || 0;
    lng2 = lng2 || 0;

    if (!lat1 || !lng1) {
      return '0';
    }

    var rad1 = lat1 * Math.PI / 180.0;
    var rad2 = lat2 * Math.PI / 180.0;
    var a = rad1 - rad2;
    var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
    var r = 6378137;
    var rs = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
    rs = rs / 1000;
    rs = rs.toFixed(2);
    return rs;
  },
  /**
   * 加法函数，用来得到精确的加法结果
   * 说明：javascript的加法结果会有误差，在两个浮点数相加的时候会比较明显。这个函数返回较为精确的加法结果。
   * @param arg1
   * @param arg2
   * @returns
   */
   accAdd: function(arg1, arg2) {
    let r1, r2;
    try {
      r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
      r1 = 0;
    }
    try {
      r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
      r2 = 0;
    }
    const m = Math.pow(10, Math.max(r1, r2));
    return (arg1 * m + arg2 * m) / m;
  },
  /**
   * 减法函数，用来得到精确的减法结果
   * 说明：javascript的减法结果会有误差，在两个浮点数相减的时候会比较明显。这个函数返回较为精确的减法结果。
   * @param arg1
   * @param arg2
   * @returns
   */
  accSub: function(arg1, arg2) {
    let r1, r2;
    try {
      r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
      r1 = 0;
    }
    try {
      r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
      r2 = 0;
    }
    const m = Math.pow(10, Math.max(r1, r2));
    //动态控制精度长度
    const n = r1 >= r2 ? r1 : r2;
    return ((arg1 * m - arg2 * m) / m).toFixed(n);
  },
  /***
   * 乘法函数，用来得到精确的乘法结果
   * 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
   * @param arg1
   * @param arg2
   * @returns
   */
  accMul: function(arg1, arg2) {
    let m = 0;
    const s1 = arg1.toString();
    const s2 = arg2.toString();
    try {
      m += s1.split('.')[1].length;
    } catch (e) {
      //
    }
    try {
      m += s2.split('.')[1].length;
    } catch (e) {
      //
    }
    return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
  },
  /***
   * 除法函数，用来得到精确的除法结果
   * 说明：javascript的除法结果会有误差，在两个浮点数相除的时候会比较明显。这个函数返回较为精确的除法结果。
   * @param a
   * @param b
   * @returns
   */
  accDiv: function(a, b) {
    let c,
      d,
      e = 0,
      f = 0;
    try {
      e = a.toString().split('.')[1].length;
    } catch (g) {
      //
    }
    try {
      f = b.toString().split('.')[1].length;
    } catch (g) {
      //
    }
    return (
      (c = Number(a.toString().replace('.', ''))),
      (d = Number(b.toString().replace('.', ''))),
      (c / d) * Math.pow(10, f - e)
    );
  }
};
module.exports = {
  //暴露接口调用
  numberFormat: numberUtil.numberFormat,
  numberSubtract: numberUtil.numberSubtract,
  numberAddition: numberUtil.numberAddition,
  getAreaDistance: numberUtil.getAreaDistance,
  accAdd: numberUtil.accAdd,
  accSub: numberUtil.accSub,
  accMul: numberUtil.accMul,
  accDiv: numberUtil.accDiv,
};