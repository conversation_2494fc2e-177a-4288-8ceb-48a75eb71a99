/***
 * 输入金额不能大于最大金额
 *
 */

const numberCompare = (num1, num2) => {
  const [num1Int, num1Dec] = `${num1}`.split('.').map(s => +s);
  const [num2Int, num2Dec] = `${num2}`.split('.').map(s => +s);

  const sign1 = Math.sign(num1);
  const sign2 = Math.sign(num2);

  if (sign1 > sign2) return 'greater';
  if (sign1 < sign2) return 'less';

  switch (sign1) {
    case 1:
      if (num1Int > num2Int) return 'greater';
      if (num1Int < num2Int) return 'less';
      if (num1Dec > num2Dec) return 'greater';
      if (num1Dec < num2Dec) return 'less';
      return 'equal';
    case -1:
      if (num1Int > num2Int) return 'less';
      if (num1Int < num2Int) return 'greater';
      if (num1Dec > num2Dec) return 'less';
      if (num1Dec < num2Dec) return 'greater';
      return 'equal';
    default:
      return 'equal';
  }
};

module.exports = {
  //暴露接口调用
  numberCompare,
};