import { useGlobalDataStore } from '@/stores/globalData'
import api  from '@/utils/api.uts'

// 获取全局样式
export const getGlobalStyle = () => {
  const globalDataStore = useGlobalDataStore()
  api.themeMobileGet().then((res: any) => {
    // const themeMobile = res.data
    // let backgroundColor = 'gradual-scarlet'
    // let themeColor = 'red'
    // let tabbarBackgroundColor = '#ffffff'
    // let tabbarColor = '#666666'
    // let tabbarSelectedColor = '#e53c43'
    // let tabbarBorderStyle = '#black'
    // let customFiled = {
    //   topTabbarIconColor: '#000000',
    //   topTabbarIconBackgroundColor: '#A07A56',
    //   topTabbarBorderColor: '#000',
    //   topTabbarSplitLineColor: '#eee',
    //   integralTab: '-1'
    // }

    // let tabbarItem = [{
    //   index: 0,
    //   text: '松鼠美妆',
    //   iconPath: '/static/public/img/icon/1-001.jpg',
    //   selectedIconPath: '/static/public/img/icon/1-002.jpg'
    // },
    // {
    //   index: 1,
    //   text: '松鼠好物',
    //   iconPath: '/static/public/img/icon/2-001.jpg',
    //   selectedIconPath: '/static/public/img/icon/2-002.jpg'
    // },
    // {
    //   index: 2,
    //   text: '逛逛',
    //   iconPath: '/static/public/img/icon/3-001.jpg',
    //   selectedIconPath: '/static/public/img/icon/3-002.jpg'
    // },
    // {
    //   index: 3,
    //   text: '购物车',
    //   iconPath: '/static/public/img/icon/4-001.jpg',
    //   selectedIconPath: '/static/public/img/icon/4-002.jpg'
    // },
    // {
    //   index: 4,
    //   text: '我的',
    //   iconPath: '/static/public/img/icon/5-001.jpg',
    //   selectedIconPath: '/static/public/img/icon/5-002.png'
    // }]

    // if (themeMobile) {
    //   themeColor = themeMobile.themeColor
    //   backgroundColor = themeMobile.backgroundColor
    //   tabbarBackgroundColor = themeMobile.tabbarBackgroundColor
    //   tabbarColor = themeMobile.tabbarColor
    //   tabbarSelectedColor = themeMobile.tabbarSelectedColor
    //   tabbarBorderStyle = themeMobile.tabbarBorderStyle
    //   customFiled = themeMobile.customFiled
    // }

    // globalDataStore.updateTheme({
    //   backgroundColor,
    //   themeColor,
    //   tabbarBackgroundColor,
    //   tabbarColor,
    //   tabbarSelectedColor,
    //   tabbarBorderStyle,
    //   tabbarItem,
    //   customFiled
    // })

    // if (themeMobile?.tabbarItem?.info?.length > 0) {
    //   const tabbarItemInfo = themeMobile.tabbarItem.info
    //   tabbarItemInfo.forEach((item: any) => {
    //     if (item.text) tabbarItem[item.index].text = item.text
    //     // #ifdef MP
    //     if (item.iconPath) tabbarItem[item.index].iconPath = item.iconPath
    //     if (item.selectedIconPath) tabbarItem[item.index].selectedIconPath = item.selectedIconPath
    //     // #endif
    //   })
    //   globalDataStore.updateTheme({ tabbarItem: tabbarItemInfo })
    //   uni.setStorage({
    //     key: 'tabBar',
    //     data: tabbarItemInfo,
    //   })
    // }
  }).catch((err: any) => {
    console.error('获取主题配置失败:', err)
  })
}

// 设置TabBar
export const setTabBar = () => {
  const globalDataStore = useGlobalDataStore()
  // globalDataStore.setShowingPage(true)
  const themeMobile = globalDataStore.theme
  // 检查当前页面是否为TabBar页面
  const pages = getCurrentPages()
  if (!pages.length) return
  const currPage = pages[pages.length - 1]
  const tabBarPages = [
    '/pages/home/<USER>',
    '/pages/second-tab/index', 
    '/pages/shopping-cart/index',
    '/pages/tab-personal/index',
    '/pages/third-tab/index'
  ]
  if (!tabBarPages.includes(currPage.$page.fullPath)) {
    return
  }

  uni.setTabBarStyle({
    backgroundColor: themeMobile.tabbarBackgroundColor!,
    color: themeMobile.tabbarColor!,
    selectedColor: themeMobile.tabbarSelectedColor!,
    borderStyle: themeMobile.tabbarBorderStyle!
  })

  const tabbarItem = themeMobile.tabbarItem;
  
  tabbarItem.forEach((item: any) => {
    let iconPath = item.iconPath
    let selectedIconPath = item.selectedIconPath

    // #ifdef H5
    if (selectedIconPath.indexOf('http') != -1) {
      const indexTemp = selectedIconPath.indexOf(':/') + 1
      selectedIconPath = selectedIconPath.substring(indexTemp, selectedIconPath.length)
    }
    if (iconPath.indexOf('http') != -1) {
      const indexTemp = iconPath.indexOf(':/') + 1
      iconPath = iconPath.substring(indexTemp, iconPath.length)
    }
    // #endif

    uni.setTabBarItem({
      index: item.index,
      text: item.text,
      iconPath: iconPath,
      selectedIconPath: selectedIconPath
    })
  })
}

export const updateShoppingCartCount = () => {
	api.shoppingCartCount({}).then((res) => {
	  const shoppingCartCount = res.data;
	  const globalDataStore = useGlobalDataStore();
	  globalDataStore.setShoppingCartCount(parseInt(shoppingCartCount));
	
	  if (shoppingCartCount > 0) {
	    uni.setTabBarBadge({
	      index: 3,
	      text: shoppingCartCount.toString(),
	    });
	  } else {
	    uni.removeTabBarBadge({ index: 3 });
	  }
	  uni.$emit("updateCart");
	});
}

// 默认导出所有方法
export default {
  getGlobalStyle,
  setTabBar,
  updateShoppingCartCount
}