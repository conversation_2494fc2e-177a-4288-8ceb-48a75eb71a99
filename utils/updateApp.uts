import { isForceUpdate } from '@/api/app.uts'


// #ifdef MP
//微信小程序检查版本
export const updateManager = () => {
	try {
		const updateManager = uni.getUpdateManager();
		updateManager.onUpdateReady(() => {
			isForceUpdate().then((result) => {
				if (result.data.weixinForceUpdate) {
					uni.showModal({
						title: '更新提示',
						content: '新版本已经准备好，请重启运行新版本！',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								updateManager.applyUpdate()
							}
						}
					})
				}
			}).catch((e) => {
				console.error("微信小程序检查版本===", e)；
			})
		})
	} catch (error) {
		//TODO handle the exception
		console.error("微信小程序检查版本===", error)；
	}
}
// #endif

// 默认导出所有方法
export default {
	// #ifdef MP
	updateManager,
	// #endif
}