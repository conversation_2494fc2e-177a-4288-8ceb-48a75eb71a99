import __config from '@/config/env';
// util 在此文件中未使用，移除导入


const request = ({
  url = '',
  files = [],
  fileType = '',
  file,
  filePath = '',
  name = '',
  timeout,
  formData = {}
}) => {
  let _url = __config.basePath + url;
  const userInfo = uni.getStorageSync('user_info')
  
  // channel 渠道 全局判断渠道 SONGSHU-微信小程序 APP-APP
  // 先判断data有没有值，再判断data里面有没有channel，再判断channel的值，最后替换channel的值
  if (formData) {
  	formData.channel = formData.channel || ''
  	if (formData.channel == '' || formData.channel == 'SONGSHU' || formData.channel == 'APP') {
  		if (uni.getSystemInfoSync().uniPlatform == 'app') { formData.channel = 'APP' }
  		else if (uni.getSystemInfoSync().uniPlatform == 'mp-weixin') { formData.channel = 'SONGSHU' }
  	} else {
  		formData.channel = formData.channel
  	}
  }
  
  return new Promise((resolve, reject) => {
    const header = {
      //#ifdef MP-WEIXIN
      'client-type': 'MA', //客户端类型小程序
      'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
      //#endif
      //#ifdef APP-PLUS
      'client-type': 'APP', //客户端类型APP
      'app-id': uni.getStorageSync('user_info')?.appId || '', 
      'tenant-id': __config.tenantId,
      //#endif
      'third-session': uni.getStorageSync('third_session') ? uni.getStorageSync('third_session') : '',
      'user_id': userInfo ? userInfo.id : ''
    }
    
    uni.uploadFile({
      url: _url,
      files,
      file,
      filePath,
      name,
      header,
      formData,
      success(res) {
        resolve(res)
      },
      fail(error) {
        reject(error)
      },
      complete(res) {
      	uni.hideLoading();
      }
    })
  })
}


module.exports = {
  request
}