import { validatenull, validateEmail, validateMobile, invoiceTax } from "./validate.uts";
import { useGlobalDataStore } from "@/stores/globalData";
import __config from "@/config/env";

// 定义接口类型
interface DateObject {
  year : number;
  month : number;
  day : number;
  hour : number;
  minute : number;
  second : number;
}

interface ClientCode {
  key : string;
  value : string;
}

interface OperationResult {
  times : number;
  num : number;
}

interface FloatOperations {
  add : (a : number, b : number) => number;
  subtract : (a : number, b : number) => number;
  multiply : (a : number, b : number) => number;
  divide : (a : number, b : number) => number;
}

interface StorageParams {
  date : number;
  value : any;
}

interface ShowAdvert {
  showDate : string;
  showTime : number;
  showTimes : number;
}

export function formatTime(date : Date) : string {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();
  return [year, month, day].map(formatNumber).join("/") + " " + [hour, minute, second].map(formatNumber).join(":");
}

function formatNumber(n : number) : string {
  n = n.toString();
  return n[1] ? n : "0" + n;
}

export function dateFilter(time : string | number | null) : string {
  if (!time) {
    // 当时间是null或者无效格式时我们返回空
    return " ";
  } else {
    const date = new Date(time); // 时间戳为10位需*1000，时间戳为13位的话不需乘1000
    const dateNumFun = (num : number) : string => (num < 10 ? `0${num}` : num); // 使用箭头函数和三目运算以及es6字符串的简单操作。因为只有一个操作不需要{} ，目的就是数字小于10，例如9那么就加上一个0，变成09，否则就返回本身。
    // 这是es6的解构赋值。
    const [Y, M, D, h, m, s] = [
      date.getFullYear(),
      dateNumFun(date.getMonth() + 1),
      dateNumFun(date.getDate()),
      dateNumFun(date.getHours()),
      dateNumFun(date.getMinutes()),
      dateNumFun(date.getSeconds()),
    ];
    return `${Y}-${M}-${D} ${h}:${m}:${s}`; // 一定要注意是反引号，否则无效。
  }
}

// 直接
export function handleTime(date : string = "", format : string = "yyyy-MM-dd hh:mm:ss") : string {
  if (!date) {
    return dateFormat(new Date(), format);
  }
  // ios处理
  const newDate = date.split("-").join("/");
  return dateFormat(new Date(newDate), format);
}
/**
 * 日期格式化
 */
function dateFormat(date : Date, format : string = "yyyy-MM-dd hh:mm:ss") : string {
  // let format = 'yyyy-MM-dd hh:mm:ss';
  if (date !== "Invalid Date") {
    const o : UTSJSONObject = {
      "M+": date.getMonth() + 1, // month
      "d+": date.getDate(), // day
      "h+": date.getHours(), // hour
      "m+": date.getMinutes(), // minute
      "s+": date.getSeconds(), // second
      "q+": Math.floor((date.getMonth() + 3) / 3), // quarter
      "S": date.getMilliseconds(), // millisecond
    };
    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (const k in o) {
      if (new RegExp("(" + k + ")").test(format)) {
        format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
      }
    }

    return format;
  }
  return "";
}

//空值过滤器
export function filterForm(form : UTSJSONObject) : UTSJSONObject {
  const obj : UTSJSONObject = {};
  Object.keys(form).forEach((ele) => {
    if (!validatenull(form[ele])) {
      obj[ele] = form[ele];
    }
  });
  return obj;
}

//获取当前页面带参数的url
export function getCurrentPageUrlWithArgs(val ?: any) : string {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const url = currentPage.route;
  const options = currentPage.options;
  let urlWithArgs = `/${url}?`;

  for (let key in options) {
    const value = options[key];
    urlWithArgs += `${key}=${value}&`;
  }

  urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1);
  return urlWithArgs;
}

// 提取url中的解析字符串
export function UrlParamHash(url : string, name : string) : string | null {
  const params : UTSJSONObject = {};
  let h : string[] = [];
  if (typeof url != "string") return null;
  const hash = url.split("&");
  // console.log(hash);
  for (let i = 0; i < hash.length; i++) {
    h = hash[i].split("=") as string[];
    params[h[0]] = h[1];
    // console.log(h);
  }
  return params[name] as string | null;
}

//获取url中的参数
export function getUrlParam(path : string, name : string) : string {
  const reg = new RegExp("(^|\\?|&)" + name + "=([^&]*)(\\s|&|$)", "i");
  if (reg.test(path)) return unescape(RegExp.$2.replace(/\+/g, " "));
  return "";
}

//判断是否为微信浏览器中运行
export function isWeiXinBrowser() : boolean {
  // #ifdef H5
  // window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型
  const ua = (window.navigator.userAgent as string).toLowerCase();
  // 通过正则表达式匹配ua中是否含有MicroMessenger字符串
  if (ua.match(/MicroMessenger/i) == "micromessenger") {
    return true;
  } else {
    return false;
  }
  // #endif
  return false;
}

// //函数节流  触发事件立即执行，但在n秒内连续触发则不执行
// const throttle = (fn, gapTime) => {
// 	if (gapTime == null || gapTime == undefined) {
// 		gapTime = 1500
// 	}

// 	let _lastTime = null
// 	// 返回新的函数
// 	return function () {
// 		console.log("==throttle节流===")
// 		let _nowTime = new Date()
// 		if (_nowTime - _lastTime > gapTime || !_lastTime) {
// 			fn.apply(this, arguments) //将this和参数传给原函数
// 			_lastTime = _nowTime
// 		}
// 	}
// }

/**
 *函数节流  触发事件立即执行，但在n秒内连续触发则不执行 如：按钮点击
 */
export function throttle(fn : () => void, gapTime : number = 1000, immediate : boolean = true) : () => void {
  let timer : number | null = null;
  let flag : boolean = false;
  // 返回新的函数
  return function () {
    if (immediate) {
      if (!flag) {
        flag = true;
        // 如果是立即执行，则在gapTime毫秒内开始时执行
        typeof fn === "function" && fn();
        timer = setTimeout(() => {
          flag = false;
        }, gapTime);
      }
    } else {
      if (!flag) {
        flag = true;
        // 如果是非立即执行，则在gapTime毫秒内的结束处执行
        timer = setTimeout(() => {
          flag = false;
          typeof fn === "function" && fn();
        }, gapTime);
      }
    }
  };
}

/**
 * 函数防抖
 * 触发事件后在n秒后执行，如果n秒内又触发事件，则重新计算时间 如：搜索框，滚动条
 */
// const debounce = (fn, wait = 1000) => {
// 	let timer;
// 	return function () {
// 		let context = this;
// 		let args = arguments;
// 		// 清除定时器
// 		if (timer) clearTimeout(timer);
// 		// 设置定时器，当最后一次操作后，timeout不会再被清除，所以在延时wait毫秒后执行func回调方法
// 		timer = setTimeout(() => {
// 			fn.apply(context, args);
// 		}, wait)
// 	}
// }

/**
 * 函数防抖
 * 触发事件后在n秒后执行，如果n秒内又触发事件，则重新计算时间 如：搜索框，滚动条
 */
export function debounceGet(fn : (...args : any[]) => void, wait : number = 1000) : (...args : any[]) => void {
  let timer : number | null = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, wait);
  };
}

/**
 * 函数防抖
 * 触发事件后在n秒后执行，如果n秒内又触发事件，则重新计算时间 如：搜索框，滚动条
 */
export function debounce(func : () => void, wait : number = 500, immediate : boolean = false) : () => void {
  let timeout : number | null = null;
  return function () {
    // 清除定时器
    if (timeout !== null) clearTimeout(timeout);
    // 立即执行，此类情况一般用不到
    if (immediate) {
      const callNow = !timeout;
      timeout = setTimeout(function () {
        timeout = null;
      }, wait);
      if (callNow) typeof func === "function" && func();
    } else {
      // 设置定时器，当最后一次操作后，timeout不会再被清除，所以在延时wait毫秒后执行func回调方法
      timeout = setTimeout(function () {
        typeof func === "function" && func();
      }, wait);
    }
  };
}

//判断是否是小程序
export function isMiniPg() : boolean {
  let isMiniPg = false;
  //#ifdef MP-WEIXIN
  isMiniPg = true;
  //#endif
  return isMiniPg;
}

//获取客服端代码
export function getClientCode() : ClientCode {
  let code : ClientCode = { key: "", value: "" };
  //#ifdef MP-WEIXIN
  code = {
    key: "MP-WEIXIN",
    value: "微信小程序",
  };
  //#endif
  //#ifdef H5
  //普通H5
  code = {
    key: "H5",
    value: "普通H5",
  };
  if (isWeiXinBrowser()) {
    //微信公众号H5
    code = {
      key: "H5-WX",
      value: "公众号H5",
    };
  }
  //#endif
  return code;
}

//重置url中的参数
export function resetPageUrl(query : UTSJSONObject) : void {
  const ary : string[] = [];
  for (const p in query) {
    if (query.hasOwnProperty(p) && query[p]) {
      ary.push(encodeURIComponent(p) + "=" + encodeURIComponent(query[p]));
    }
  }
  if (ary.length > 0) {
    let url = "?" + ary.join("&");
    history.replaceState(history.state, null, url); //替换页面显示url
  }
}

export function imgUrlToBase64(imageUrl : string) : Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      uni.downloadFile({
        url: imageUrl + "?s=" + Math.random().toString(),
        success: (res) => {
          if (res.statusCode === 200) {
            // uni 如果是H5或者APP会自动转为base64返回
            resolve(res.tempFilePath);
          } else {
            reject(res.errMsg);
          }
        },
        fail(err) {
          reject(err);
        },
      });
    } catch (e) {
      console.log(e);
      resolve(imageUrl);
    }
  });
}

// 设置原生app的分享url为h5的地址url
export function setAppPlusShareUrl(query ?: UTSJSONObject) : string {
  const pages = getCurrentPages();
  const curPage = pages[pages.length - 1];
  const userInfo = uni.getStorageSync("user_info");
  const userCode = userInfo ? userInfo.userCode : "";
  let component_appid = "";
  if (query && query.componentAppId) {
    component_appid = "&component_appid=" + query.componentAppId;
  }
  let fullPath = curPage.$page.fullPath;
  //判断是否有问号
  fullPath = fullPath.indexOf("?") != -1 ? fullPath + "&" : fullPath + "?";
  if (userCode) {
    return __config.h5HostUrl + "/slshop-h5/ma" + fullPath + "tenant_id=" + __config.tenantId + "&app_id=" + __config.wxAppId + "&sharer_user_code=" + userCode + component_appid;
  } else {
    return __config.h5HostUrl + "/slshop-h5/ma" + fullPath + "tenant_id=" + __config.tenantId + "&app_id=" + __config.wxAppId + component_appid;
  }
}

// 设置原生app的分享url为h5的地址url（用于分销，默认首页地址）
export function setAppPlusHomeShareUrl(query ?: UTSJSONObject) : string {
  const userInfo = uni.getStorageSync("user_info");
  const userCode = userInfo ? userInfo.userCode : "";
  let component_appid = "";
  if (query && query.componentAppId) {
    component_appid = "&component_appid=" + query.componentAppId;
  }
  let fullPath = "/pages/home/<USER>";
  if (userCode) {
    return __config.h5HostUrl + fullPath + "tenant_id=" + __config.tenantId + "&app_id=" + __config.wxAppId + "&sharer_user_code=" + userCode + component_appid;
  } else {
    return __config.h5HostUrl + fullPath + "tenant_id=" + __config.tenantId + "&app_id=" + __config.wxAppId + component_appid;
  }
}

// 设置h5的分享地址url
export function setH5ShareUrl(val ?: any) : string {
  const userInfo = uni.getStorageSync("user_info");
  const userCode = userInfo ? userInfo.userCode : "";
  let url = window.location.href;
  // 如果没有 sharer_user_code 就添加，如果有就替换为自己的userCode
  if (userCode) {
    let index = url.indexOf("&sharer_user_code=");
    if (index == -1) {
      url = url + "&sharer_user_code=" + userCode;
    } else {
      let urlTemp = url.slice(0, index);
      url = urlTemp + "&sharer_user_code=" + userCode;
    }
  }
  return url;
}

// 设置H5的分享url（用于分销，默认首页地址）
export function setH5HomeShareUrl(val ?: any) : string {
  const userInfo = uni.getStorageSync("user_info");
  const userCode = userInfo ? userInfo.userCode : "";
  // 如果没有 sharer_user_code 就添加
  let url = window.location.origin + window.location.search;
  if (userCode) {
    let index = url.indexOf("&sharer_user_code=");
    if (index == -1) {
      url = url + "&sharer_user_code=" + userCode;
    } else {
      let urlTemp = url.slice(0, index);
      url = urlTemp + "&sharer_user_code=" + userCode;
    }
  }
  return url;
}
// 获取当前页面路由或 path
export function getCurPage(pages : any[]) : string {
  let curPage = pages[pages.length - 1];
  return curPage.route;
}

// 保存别人分享来的 userCode
export function saveSharerUserCode(options : UTSJSONObject) : void {
  const app = getApp();
  const pages = getCurrentPages(); // 获取页面栈
  let currPage : any = null;
  let lastPage : any = null;
  if (pages && pages.length > 0) {
    currPage = pages[pages.length - 1]; // 当前页面
  }
  if (pages && pages.length > 1) {
    lastPage = pages[pages.length - 2]; // 当前页面
  }
  //,以前需求是只分享商品详情也作为分销员业绩，又来个需求是 微页面分享出去，然后点击里面的商品也可以作为分销员的业绩
  if (lastPage && lastPage.$vm && lastPage.$vm._data) {
    currPage.$vm._data.sharer_user_code = lastPage.$vm._data.sharer_user_code;
  }
  if (options.scene) {
    //接受二维码中参数
    /**
     * 这里需要特别注意：
     * 由于腾讯限制了scenes的长度，导致传参的局限性，为尽可能的利用这有限的长度传参，
     * 故我们定义了scenes的参数格式，当一个页面需要传多个参数时，我们用“&”符号分割开来，第2位固定放分享人的user_code，这样可以最大限度减少长度占用
     * 第1位一般放ID，第2位固定放分享人的user_code，比如商品页面scenes为：goodspuId+&+sharer_user_code
     * 因为固定第2位放分享人的user_code，当有些页面无需传ID时，我们也需要用“&”拼一下，第一位随意用一个字符点位即可，比如页面scenes为：0+&+sharer_user_code
     */
    let scenes = decodeURIComponent(options.scene).split("&");
    //有时候传入的参数sf=XX&nGO=XX  只解析这种的：goodspuId+&+sharer_user_code
    if (scenes[1] && scenes[1].indexOf("=") == -1) {
      if (currPage && currPage.$vm && currPage.$vm._data) {
        currPage.$vm._data.sharer_user_code = scenes[1];
      }
    }
  } else {
    if (options.sharer_user_code) {
      if (currPage && currPage.$vm && currPage.$vm._data) {
        currPage.$vm._data.sharer_user_code = options.sharer_user_code;
      }
    }
  }
}

// 现在分销的需求是 仅仅客户点击分销人分享的商品详情页面立即购买和加购计入分销业绩，退出该页面之后浏览首页等任何页面包括刚刚分享的商品，之后购买加购行为都不算分销业绩
// 也就是说，只有这时候在该商品详情页面点击的购买和加购行为算分销页面。其他都不算
// 加购之后购物车的数量修改跟分销员code挂钩的逻辑由后端处理
//所以 商品详情页面在销毁的时候意味着这次分销行为就结束了
export function removeSharerUserCode(options ?: UTSJSONObject) : void {
  const pages = getCurrentPages(); // 获取页面栈
  let currPage : any = null;
  if (pages && pages.length > 0) {
    currPage = pages[pages.length - 1]; // 当前页面
  }
  if (currPage && currPage.$vm && currPage.$vm._data) {
    currPage.$vm._data.sharer_user_code = "";
  }
}

/**
 * isNoAddDis true 不需要添加distribution: false 如果是数字的话需要添加distribution: 主要分销那边需要加这个
 */
export function getSharerUserCode(isNoAddDis ?: boolean) : string {
  const app = getApp();
  const pages = getCurrentPages(); // 获取页面栈
  let currPage : any = null;
  if (pages && pages.length > 0) {
    currPage = pages[pages.length - 1]; // 当前页面
  }
  let sharer_user_code = "";
  if (currPage && currPage.$vm && currPage.$vm._data) {
    sharer_user_code = currPage.$vm._data.sharer_user_code || "";
  }
  //现在视频号是接口给的链接后面拼接了sharer_user_code, 如果是数字说明的分销的userCode,字符串的话说明是视频号后面拼接了localLive
  if (!isNoAddDis && sharer_user_code && !isNaN(Number(sharer_user_code))) {
    sharer_user_code = `distribution:${sharer_user_code}`;
  }

  if (!sharer_user_code) {
    sharer_user_code = app.globalData.weixinadinfoId ? "ad:" + app.globalData.weixinadinfoId : app.globalData.sf ? app.globalData.sf : "";
  }

  return sharer_user_code;
}

// 如果有分享人则给data带上分享人的user_code
export function dataAddSharerUserCode(data : UTSJSONObject) : UTSJSONObject {
  const app = getApp();
  const pages = getCurrentPages(); // 获取页面栈
  let currPage : any = null;
  if (pages && pages.length > 0) {
    currPage = pages[pages.length - 1]; // 当前页面
  }
  let sharer_user_code = "";
  if (currPage && currPage.$vm && currPage.$vm._data) {
    sharer_user_code = currPage.$vm._data.sharer_user_code || "";
  }
  //现在视频号是接口给的链接后面拼接了sharer_user_code, 如果是数字说明是分销的userCode,字符串的话说明是视频号后面拼接了localLive
  if (sharer_user_code && !isNaN(Number(sharer_user_code))) {
    sharer_user_code = `distribution:${sharer_user_code}`;
  } else if (!sharer_user_code) {
    sharer_user_code = app.globalData.weixinadinfoId ? "ad:" + app.globalData.weixinadinfoId : app.globalData.sf ? app.globalData.sf : "";
  }
  if (sharer_user_code) {
    data = Object.assign(
      {
        // 旧的逻辑
        // sharerUserCode: sharer_user_code
        // 后台接口场景值统一用这个
        scene: sharer_user_code,
      },
      data
    );
  }
  return data;
}

//返回登录页面
export function backLoginPage(data ?: any) : void {
  var pages = getCurrentPages(); // 获取页面栈
  var currPage = pages[pages.length - 1]; // 当前页面
  if (currPage) {
    let curParam = currPage.options;
    // 拼接参数
    let reUrl = "/" + currPage.route;
    if (curParam != null) {
      // 拼接参数
      let param = "";
      for (let key in curParam) {
        param += "&" + key + "=" + curParam[key];
      }
      param = param.substr(1);
      reUrl = reUrl + "?" + param;
      reUrl = encodeURIComponent(reUrl);
    }
    uni.navigateTo({
      url: "/pages/login/index?reUrl=" + reUrl,
    });
  }
}

/**
 * 将时间格式中的 '-' 转换成IOS 下可以识别的 '/'
 * @param {*} date_str  时间串
 */
export function getDateTimeForIOS(date_str : string) : string {
  try {
    // const res = uni.getSystemInfoSync();
    // if (res.platform == 'ios') {
    if (date_str.indexOf("-") != -1) {
      return (date_str + "").replace(/-/g, "/");
    } else {
      return date_str;
    }
    // }
  } catch (e) {
    // error
  }
  return date_str;
}

// 保存有场景值的
export function saveSceneCode(options : UTSJSONObject) : void {
  if (options.scene) {
    const qrCodeScene = decodeURIComponent(options.scene);
    if (qrCodeScene) {
      //接受二维码中参数  参数sf=XXX&id=XXX
      // const qrCodeSceneArray = qrCodeScene.split('&');
      const qrCodeSf = UrlParamHash(qrCodeScene, "sf");
      if (qrCodeSf) {
        const globalDataStore = useGlobalDataStore();
        globalDataStore.setSourceTracking(qrCodeSf, globalDataStore.nGo);
      }
    }
  } else {
    if (options.sf) {
      const globalDataStore = useGlobalDataStore();
      globalDataStore.setSourceTracking(options.sf, globalDataStore.nGo);
    }
  }
}

export function isUserLogin() : boolean {
  const userInfo = uni.getStorageSync("user_info");
  if (userInfo && userInfo.id != null && userInfo.id > 0 && userInfo.erpCid != null && userInfo.erpCid > 0) {
    return true; //有信息 已经登录
  }
  return false; // 无信息 没有登录
}

//设置缓存 (单位为秒)
export function setStorage(value : any, key : string) : void {
  const params : StorageParams = {
    date: new Date().getTime(),
    value,
  };
  uni.setStorageSync(key, JSON.stringify(params));
}

/**
 *
 * @param {*} day 有效期
 * @param {*} key
 */
export function getStorage(day : number = 0.5, key : string) : any | null {
  let obj = uni.getStorageSync(key);
  if (!obj) return null;
  obj = JSON.parse(obj);
  const date = new Date().getTime();
  if (date - obj.date > 86400000 * day) return null;
  return obj.value;
}

export function removeStorage(key : string) : void {
  uni.removeStorageSync(key);
}

/**
 * 递归压缩微信图片
 * @param url 图片路径
 * @param count 已递归次数（有可能压缩不到想要的大小，所以得限制次数）
 * @param isReturnBase64 是否返回base64
 * @param callback 回调函数
 * @return
 */
// const recursionCompressWechat = (url, count, isReturnBase64, callback) => {
// 	// 在递归五次后结束递归
// 	if (count > 5) {
// 		if (isReturnBase64) {
// 			WechatTobase4(url)
// 		} else {
// 			callback && callback(url);
// 		}
// 		return;
// 	}
// 	// 将图片进行压缩
// 	uni.compressImage({
// 		src: url, // 图片路径
// 		quality: 40, // 压缩质量
// 		success: (resCompress) => {
// 			console.log(resCompress, "压缩后");
// 			// 先获取压缩后的体积，大于1M就继续压缩
// 			uni.getFileInfo({
// 				filePath: resCompress.tempFilePath,
// 				success: (resFileInfo) => {
// 					if (resFileInfo.size > 500 * 500) {
// 						//压缩后大于1M就继续压缩
// 						count++;
// 						recursionCompressWechat(resCompress.tempFilePath, count,
// 							isReturnBase64, callback);
// 						return;
// 					} else {
// 						if (isReturnBase64) {
// 							WechatTobase4(resCompress.tempFilePath)
// 						} else {
// 							callback && callback(resCompress.tempFilePath)
// 						}
// 					}
// 				},
// 			});
// 		},
// 		fail: (resCompress) => {
// 			callback(url);
// 		},
// 	});

// 	// 微信-url转base64
// 	function WechatTobase4(url) {
// 		uni.getFileSystemManager().readFile({
// 			filePath: url, //选择图片返回的相对路径
// 			encoding: "base64", //编码格式
// 			success: async (res) => {
// 				callback && callback("data:image/" + "png" + ";base64," + res.data);
// 			},
// 			fail: (res) => {
// 				console.log(res, "wxPathTobase64-error");
// 			},
// 		});
// 	};
// };

//把base64转换成图片
export function getBase64ImageUrl(base64Url : string) : string {
  /// 获取到base64Data
  var base64Data = base64Url;
  /// 通过微信小程序自带方法将base64转为二进制去除特殊符号，再转回base64
  base64Data = uni.arrayBufferToBase64(uni.base64ToArrayBuffer(base64Data));
  /// 拼接请求头，data格式可以为image/png或者image/jpeg等，看需求
  const base64ImgUrl = "data:image/png;base64," + base64Data;
  /// 得到的base64ImgUrl直接给图片:src使用即可
  return base64ImgUrl;
}

function formatDate(date : Date) : string {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  return [year, month, day].map(formatNumber).join("-");
}

/**
 * @param {*} intervalTime 间隔展示小时数    intervalNumber 每天最多展示次数
 * @param {*} key 哪个广告  screen  开屏广告   dialog 弹窗广告
 * return 本次打开程序 首页广告是否展示
 */
export function isShowAdvert(intervalTime : number, intervalNumber : number, key : string) : boolean {
  const TAG = "SONGLEI-";
  if (!intervalTime || intervalTime <= 0) {
    // 间隔0小时展示就是一直展示
    removeStorage(TAG + key);
    return true;
  }
  if (intervalNumber <= 0 || !intervalNumber) {
    // 每天显示0次 意思就是不显示
    removeStorage(TAG + key);
    return false;
  }
  let lastString = uni.getStorageSync(TAG + key);
  const date = new Date();
  const current : ShowAdvert = {
    showDate: formatDate(date),
    showTime: date.getTime(),
    showTimes: 0,
  };
  if (!lastString) {
    // 之前没存储过,首次计时
    current.showTimes = 1;
    uni.setStorageSync(TAG + key, JSON.stringify(current));
    return true;
  }
  const lastObj = JSON.parse(lastString);
  if (lastObj.showDate != current.showDate) {
    // 新的一天开始了，首次计时
    current.showTimes = 1;
    uni.setStorageSync(TAG + key, JSON.stringify(current));
    return true;
  }
  if (lastObj.showTimes < intervalNumber && current.showTime - lastObj.showTime >= intervalTime * 3600000) {
    current.showTimes = lastObj.showTimes + 1;
    uni.setStorageSync(TAG + key, JSON.stringify(current));
    return true;
  }
  return false;
}

export function isIOS() : boolean {
  const info = uni.getSystemInfoSync();
  console.log("========isIOS=========", info.platform);
  return info.platform == "ios";
}

function promisify(wxapi : any) : (options : any, ...params : any[]) => Promise<any> {
  return (options, ...params) => {
    return new Promise((resolve, reject) => {
      wxapi(
        Object.assign({}, options, {
          success: resolve,
          fail: reject,
        }),
        ...params
      );
    });
  };
}

export function toAsync(names : string[]) : UTSJSONObject {
  // 这里 names 期望是一个数组
  return (names || [])
    .map((name) => ({
      name,
      member: wx[name],
    }))
    .filter((t) => typeof t.member === "function")
    .reduce((r, t) => {
      r[t.name] = promisify(wx[t.name]);
      return r;
    }, {});
}

// 计算实付金额
export const floatObj : FloatOperations = (function () : FloatOperations {
  /*
   * 判断obj是否为一个整数
   */
  function isInteger(obj : number) : boolean {
    return Math.floor(obj) === obj;
  }
  /*
   * 将一个浮点数转成整数，返回整数和倍数。如 3.14 >> 314，倍数是 100
   * @param floatNum {number} 小数
   * @return {object}
   *   {times:100, num: 314}
   */
  function toInteger(floatNum : number) : OperationResult {
    const ret : OperationResult = {
      times: 1,
      num: 0,
    };
    if (isInteger(floatNum)) {
      ret.num = floatNum;
      return ret;
    }
    const strfi = floatNum.toString();
    const dotPos = strfi.indexOf(".");
    const len = strfi.substr(dotPos + 1).length;
    const times = Math.pow(10, len);
    const intNum = parseInt((floatNum * times + 0.5).toString(), 10);
    ret.times = times;
    ret.num = intNum;
    return ret;
  }

  /*
   * 核心方法，实现加减乘除运算，确保不丢失精度
   * 思路：把小数放大为整数（乘），进行算术运算，再缩小为小数（除）
   *
   * @param a {number} 运算数1
   * @param b {number} 运算数2
   * @param op {string} 运算类型，有加减乘除（add/subtract/multiply/divide）
   *
   */
  function operation(a : number, b : number, op : string) : number {
    const o1 = toInteger(a);
    const o2 = toInteger(b);
    const n1 = o1.num;
    const n2 = o2.num;
    const t1 = o1.times;
    const t2 = o2.times;
    const max = t1 > t2 ? t1 : t2;
    let result : number = 0;
    switch (op) {
      case "add":
        if (t1 === t2) {
          // 两个小数位数相同
          result = n1 + n2;
        } else if (t1 > t2) {
          // o1 小数位 大于 o2
          result = n1 + n2 * (t1 / t2);
        } else {
          // o1 小数位 小于 o2
          result = n1 * (t2 / t1) + n2;
        }
        return result / max;
      case "subtract":
        if (t1 === t2) {
          result = n1 - n2;
        } else if (t1 > t2) {
          result = n1 - n2 * (t1 / t2);
        } else {
          result = n1 * (t2 / t1) - n2;
        }
        return result / max;
      case "multiply":
        result = (n1 * n2) / (t1 * t2);
        return result;
      case "divide":
        result = (n1 / n2) * (t2 / t1);
        return result;
    }
  }

  // 加减乘除的四个接口
  function add(a : number, b : number) : number {
    return operation(a, b, "add");
  }

  function subtract(a : number, b : number) : number {
    return operation(a, b, "subtract");
  }

  function multiply(a : number, b : number) : number {
    return operation(a, b, "multiply");
  }

  function divide(a : number, b : number) : number {
    return operation(a, b, "divide");
  }

  // exports
  return {
    add: add,
    subtract: subtract,
    multiply: multiply,
    divide: divide,
  };
})();

export function rpx2px(rpx : number) : number {
  return (rpx / 750) * uni.getSystemInfoSync().windowWidth;
}

export function maskMiddle(str : string) : string {
  if (str.length <= 2) {
    return str;
  }
  const firstChar = str[0];
  const lastChar = str[str.length - 1];
  const middle = "*".repeat(str.length - 2);
  return firstChar + middle + lastChar;
}

export function handleCustomerService() : void {
  const app = getApp();
  const wxCustomerUrl = app.globalData.wxCustomerUrl;
  if (wxCustomerUrl) {
    // #ifdef MP
    wx.openCustomerServiceChat({
      extInfo: {
        url: wxCustomerUrl,
      },
      corpId: __config.chatId,
      success(res) { },
    });
    // #endif
    // #ifdef APP
    uni.share({
      provider: "weixin",
      scene: "WXSceneSession",
      openCustomerServiceChat: true,
      corpid: __config.chatId,
      customerUrl: wxCustomerUrl,
      fail(err) {
        console.log("打开客服错误", err);
        // that.handleCall();
      },
    });
    // #endif
  } else {
    uni.showToast({
      title: "请店铺客服先配置下客服链接",
      icon: "none",
      duration: 2000,
    });
  }
}

// #ifdef MP-WEIXIN
function loadFontWithCache(fontUrl : string, fontFamily : string) : void {
  const cachedPath = uni.getStorageSync(fontUrl);
  if (cachedPath) {
    loadFont(cachedPath, fontFamily, fontUrl);
  } else {
    downloadAndCacheFont(fontUrl, fontFamily);
  }
}

function downloadAndCacheFont(fontUrl : string, fontFamily : string) : void {
  uni.downloadFile({
    url: fontUrl,
    success: (res) => {
      if (res.statusCode === 200) {
        const tempPath = res.tempFilePath;
        const fs = uni.getFileSystemManager();
        fs.saveFile({
          tempFilePath: tempPath,
          success: (savedRes) => {
            const savedPath = savedRes.savedFilePath;
            uni.setStorageSync(fontUrl, savedPath);
            loadFont(savedPath, fontFamily, fontUrl);
          },
          fail: (err) => {
            console.error("保存字体文件失败", err);
          },
        });
      }
    },
    fail: (err) => {
      console.error("下载字体文件失败", err);
    },
  });
}

function loadFont(path : string, fontFamily : string, fontUrl : string) : void {
  uni.loadFontFace({
    family: fontFamily,
    source: `url("${path}")`,
    success: () => {
      console.error("字体加载成功" + fontFamily);
    },
    fail: (err) => {
      console.error(fontFamily + "字体加载失败", err);
      // 可尝试重新下载
      uni.removeStorageSync(fontUrl);
      downloadAndCacheFont(fontUrl, fontFamily);
    },
  });
}

// #endif

// code 扫到的H5链接
export function scanH5ToMaPage(code : string, parseUrlLink : (params : UTSJSONObject) => Promise<any>, type ?: string) : void {
  if (code) {
    //现在这些逻辑统一接口里面给了
    // if (code.indexOf("ma/couponActivation") > -1) {
    // 	const id = UrlParamHash(code.split('?')[1], 'id');
    // 	uni.navigateTo({
    // 		url: '/pages/coupon/coupon-activation/index?id=' + id
    // 	});
    // 	return;
    // } else if (code.indexOf('/app-router/index.html') > -1) {
    // 	let url = UrlParamHash(code.split('?')[1], 'pathUrl');
    // 	url = decodeURIComponent(url);
    // 	uni.navigateTo({
    // 		url
    // 	});
    // 	return;
    // } else if (code.indexOf("ma/giftCardActivation") > -1) {
    // 	const id = UrlParamHash(code.split('?')[1], 'id');
    // 	const shopid = UrlParamHash(code.split('?')[1], 'shopid');
    // 	uni.navigateTo({
    // 		url: `/pages/gift/new-card/index?id=${id}&shopid=${shopid}`
    // 	});
    // 	return;
    // } else if (code.indexOf("/ma/goods-detail") > -1) {
    // 	const id = UrlParamHash(code.split('?')[1], 'id');
    // 	uni.navigateTo({
    // 		url: `/pages/goods/goods-detail/index?id=${id}`
    // 	});
    // 	return;
    // } else if (code.indexOf("/ma/order") > -1) {
    // 	const id = UrlParamHash(code.split('?')[1], 'id');
    // 	uni.navigateTo({
    // 		url: `/pages/order/order-detail/index?id=${id}`
    // 	});
    // 	return;
    // } else {
    const decodedUrl = decodeURIComponent(code);
    console.log("======", JSON.stringify(parseUrlLink));
    parseUrlLink({
      qrcodeUrl: decodedUrl,
    })
      .then((res) => {
        console.error("===res======", res);
        if (res.ok) {
          const query = res.data.query as string;
          const path = res.data.path as string;
          const url = path && path.startsWith("/") ? path : "/" + path;
          if (query) {
            if (type == "redirect") {
              uni.redirectTo({
                url: `/${path}?${query}`,
              });
            } else {
              uni.navigateTo({
                url: `/${path}?${query}`,
              });
            }
          } else {
            if (type == "redirect") {
              uni.redirectTo({
                url: `/${path}`,
              });
            } else {
              uni.navigateTo({
                url: `/${path}`,
              });
            }
          }
        }
      })
      .catch((e) => {
        uni.showToast({
          title: `抱歉，未找到相应数据，请联系客服`,
          duration: 2000,
        });
      });
    // }
  }
}

// 导出所有函数（UTS 中使用 export 而不是 module.exports）
// 所有函数已经通过 export 关键字单独导出