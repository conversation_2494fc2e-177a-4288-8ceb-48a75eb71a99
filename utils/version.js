//  获取当前在版本号
const getversion = () => {
	let version, Authorization
	uni.getSystemInfo({
		success(res) {
			version = Number(res.SDKVersion.replace(/\./g, ""))
			if (version >= 2212) {
				Authorization = 1
			} else if (2104 <= version && version >= 2210) {
				Authorization = 2
			} else if (version < 295) {
				Authorization = 3
			}
		}
	})


	return Authorization
}
module.exports = {
	getversion: getversion
}

// 判断版本号
